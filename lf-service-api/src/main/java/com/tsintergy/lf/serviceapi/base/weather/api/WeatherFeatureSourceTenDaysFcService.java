package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureSourceTenDaysFcDO;

import java.util.List;

public interface WeatherFeatureSourceTenDaysFcService {

    List<WeatherFeatureSourceTenDaysFcDO> findByParam(String cityId, String startYm, String endYm, String sourceType) throws Exception;

    void saveOrUpdate(WeatherFeatureSourceTenDaysFcDO weatherFeatureSourceTenDaysFcDO) throws Exception;
}
