package com.tsintergy.lf.serviceapi.large_model.api;

import com.tsintergy.lf.serviceapi.large_model.pojo.LmStatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmStatisticsCityNear15DayFcDO;

import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 11:08
 **/
public interface LmStatisticsCityNear15DayFcService {

    void saveOrUpdateBatch(List<LmStatisticsCityNear15DayFcDO> statisticsCityDayFcDOList);

    List<LmStatisticsCityNear15DayFcDO> find(String cityId, String caliberId, Date date,Integer type);
}
