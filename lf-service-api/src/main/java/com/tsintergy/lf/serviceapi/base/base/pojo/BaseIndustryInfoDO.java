package com.tsintergy.lf.serviceapi.base.base.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "base_industry_info")
public class BaseIndustryInfoDO extends BaseVO {

    @Id
    @Column(name = "code")
    private String code;

    @Column(name = "name")
    private String name;

    @Column(name = "parent_code")
    private String parentCode;

    @Column(name = "level")
    private Integer level;

    @Column(name = "order_no")
    private Integer orderNo;

    @Column(name = "valide")
    private Integer valide;
}
