
package com.tsintergy.lf.serviceapi.base.forecast.api;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralResult;
import com.tsintergy.lf.serviceapi.base.forecast.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadDecomposeCityWeekStabilityDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预测服务
 *
 * <AUTHOR>
 * @version $Id: CheckService.java, v 0.1 2018-01-31 10:15:11 tao Exp $$
 */

public interface ForecastService {

    /**
     * 超短期曲线平滑
     */
    List<BigDecimal> smoothLineVslf(List<BigDecimal> singleLoadCityVOList, String startTime, String endTime) throws Exception;


    /**
     * 获取预测概览信息
     *
     * @param cityId 城市ID
     * @param date 日期
     */
    ForecastOverviewDTO getForecastOverview(String cityId, Date date, String caliberId) throws Exception;

    /**
     * 曲线平滑
     */
    List<BigDecimal> smoothLine(List<BigDecimal> singleLoadCityVOList) throws Exception;

    /**
     * 曲线修正
     */
    List<BigDecimal> recorrectLoad(List<BigDecimal> originLoad, BigDecimal distMax, BigDecimal distMin)
        throws BusinessException;

    /**
     * 高级正常日预测
     */
    List<ForecastNormalDTO> doAdvancedForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
        List<AlgorithmEnum> algorithmEnums)
        throws Exception;

    List<LoadCityFcDO> doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
        List<AlgorithmEnum> algorithmEnums)
        throws Exception;

    /**
     * 用户页面手动调用算法入口
     */
    void doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
        List<AlgorithmEnum> algorithmEnums, String weatherCode)
        throws Exception;


    List<LoadCityFcDO> insertNormalData(String cityId, String caliberId,
        GeneralResult result)
        throws Exception;
    /**
     * 自动预测通用接口
     */
    void doForecast(String cityId, String caliber, List<Date> forecastDates, AlgorithmEnum algorithmEnums)
        throws Exception;

    /**
     * 获取最大or最小置信上限或下限
     */
    Map<String, List<BigDecimal>> getMaxMinConfidence(String cityId, String caliberId, String algorithmId, Date date)
        throws Exception;

    /**
     * 执行稳定度算法 入库
     */
    LoadDecomposeCityWeekStabilityDO doPrecisionAnalysize(String caliberId, String cityId, Date date, int weeks,
        String userId) throws Exception;

    /**
     * 获取最近一次预测
     */
    List<ForecastNormalDTO> getForecastNormal(String cityId, String caliberId, Date startDate, Date EndDate)
        throws BusinessException;

    /**
     * 节假日预测
     */
    List<ForecastNormalDTO> doForecastHoliday(String uid, String cityId, String cailberId, Date startDate, Date endDate)
        throws Exception;

    void insertOrUpdateHoliday(String cityId, String caliberId, GeneralResult result) throws Exception;
    /**
     * 台风预测
     *
     * @param loginDay 台风登陆日
     */
    void doForecastTyphoon(String cityId, String caliberId, Date loginDay, Integer type)
        throws Exception;

    /**
     * 功能描述: <br> 调用数据修正算法
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    void doDataRepairAlgorithm(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    /**
     * 平均偏差
     */
    List<BigDecimal> getAvgDeviation(String cityId, String caliberId, String algorithmId, Date date) throws Exception;


    /**
     * 灵敏度分析计算&查询接口
     */
    ResultDTO doSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception;


    void doShortForecast(String cityId, String caliberId, Date date, Integer timeSpan,
        Integer startTimePoint) throws Exception;


    List<BigDecimal> doUltraForecast(String cityId, String caliberId, Integer timeSpan, Date startDate,
        Integer startTime, com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum algorithmEnum,String param) throws Exception;



    LoadCityFcUltraDO creatFcDO(String cityId, String caliberId, Date date, Integer timeSpan, String algorithmId,
        List<BigDecimal> zeroOrNullList, Integer tarPointInBatch, IfEnum report, Boolean success);

    String getAiAlgorithmRecommendContent(String cityId, String caliberId, Date forecastDate) throws Exception;

    void getAiAlgorithmRecommendHandler(String cityId, String caliberId, Date forecastDate) throws Exception;

    WeatherSourceFcAccuracyCompareDTO getForecastSuggestion(String cityId, Date systemDate) throws Exception;
}