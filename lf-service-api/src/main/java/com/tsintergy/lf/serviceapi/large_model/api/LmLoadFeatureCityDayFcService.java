package com.tsintergy.lf.serviceapi.large_model.api;

import com.tsintergy.lf.serviceapi.large_model.pojo.LmLoadFeatureCityDayFcServiceDO;

import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/7 14:35
 **/
public interface LmLoadFeatureCityDayFcService {

    void saveOrUpdateBatch(List<LmLoadFeatureCityDayFcServiceDO> list);

    List<LmLoadFeatureCityDayFcServiceDO> find(String algorithmId, Date startDate,Date endDate);
}
