/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/3/11 17:19 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.enums;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/3/11
 * @since 1.0.0
 */
public enum WeatherEnum {
    //湿度
    HUMIDITY(1, "湿度", "humidity"),

    TEMPERATURE(2, "温度", "temperature"),

    RAINFALL(3, "降雨量", "rainfall"),

    WINDSPEED(4, "风速", "windspeed"),

    //实感温度
    EFFECTIVE_TEMPERATURE(5, "体感温度", "effective_temperature"),

    //实感温度重新计算
    EFFECTIVE_NEW_TEMPERATURE(6, "实感温度", "effective_temperature"),

    COLD_DAMPNESS(14, "寒湿指数", "cold_dampness"),

    ENTHALPY(7, "焓值", "enthalpy"),

    // add by yangzm 20210812 气象数据采集
    PRESSURE(8, "气压", "pressure"),

    // add by yangzm 20210812 气象数据采集
    WIND_DIRECTION(9, "风向", "wind_direction"),

    // add by yangzm 20210812 气象数据采集
    WEATHER_PHENOMENON(10, "天气现象", "weather_phenomenon"),

    // add by yangzm 20210812 气象数据采集
    TOTAL_CLOUD_COVER(11, "总云量", "total_cloud_cover"),

    WINDDIRECTION(12, "风向", "winddirection"),

    RADIANCE(13, "辐照", "radiance"),

    TOTAL_AVG_TEMPERATURE(54,"累计平均温度","total_avg_temperature");
    private Integer type;

    private String typeName;

    /**
     *  映射到setting_system_init的名字
     */
    private String systemSettingName;

    WeatherEnum(Integer type, String typeName, String systemSettingName) {
        this.type = type;
        this.typeName = typeName;
        this.systemSettingName = systemSettingName;
    }

    public static Integer getTypeByName(String name) {
        for (WeatherEnum w : WeatherEnum.values()) {
            if (w.getTypeName().equals(name)) {
                return w.getType();
            }
        }
        return null;
    }

    public static String getValueByName(Integer type) {
        for (WeatherEnum w : WeatherEnum.values()) {
            if (w.getType().equals(type)) {
                return w.getTypeName();
            }
        }
        return null;
    }

    public static WeatherEnum getEnumByType(Integer type) {
        for (WeatherEnum w : WeatherEnum.values()) {
            if (w.getType().equals(type)) {
                return w;
            }
        }
        return null;
    }


    public static Integer getTypeBySettingName(String systemSettingName) {
        for (WeatherEnum w : WeatherEnum.values()) {
            if (w.getSystemSettingName().equals(systemSettingName)) {
                return w.getType();
            }
        }
        return null;
    }


    public Integer value() {
        return this.type;
    }

    public String typeName() {
        return this.typeName;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getSystemSettingName() {
        return systemSettingName;
    }

    public void setSystemSettingName(String systemSettingName) {
        this.systemSettingName = systemSettingName;
    }
}
