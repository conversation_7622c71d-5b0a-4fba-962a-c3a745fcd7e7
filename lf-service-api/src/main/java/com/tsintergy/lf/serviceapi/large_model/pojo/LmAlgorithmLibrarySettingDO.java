package com.tsintergy.lf.serviceapi.large_model.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/** @Description
 * <AUTHOR>
 * 人工智能算法库设置
 * @Date 2025/7/6 13:25
 **/
@Data
@Entity
@Table(name = "LM_ALGORITHM_LIBRARY_SETTING")
public class LmAlgorithmLibrarySettingDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "route_name")
    private String routeName;

    @Column(name = "algorithm_names")
    private String algorithmNames;
}
