package com.tsintergy.lf.serviceapi.large_model.pojo;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 13:42
 **/

import com.tsieframework.core.base.dao.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;


@Data
@Entity
@Table(name = "LM_MODEL_DETAILS")
public class LmModelDetailsDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "algorithm_name")
    private String algorithmName;

    /**
     * 详情
     */
    @ApiModelProperty("详情")
    @Column(name = "details")
    private String details;

    /**
     * 优势
     */
    @ApiModelProperty("优势")
    @Column(name = "advantage")
    private String advantage;
}
