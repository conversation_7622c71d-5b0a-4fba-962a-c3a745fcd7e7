
package com.tsintergy.lf.serviceapi.base.assess.api;


import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 考核点设置
 *
 * <AUTHOR>
 */
public interface SettingAssessService {


    /**
     * 查询考核点列表；一个对象包含多个月份的数据
     *
     * @param year 年度
     * @return 结果list
     */
    List<SettingAssessDataDTO> findAssessDataList(String year, String caliberId);

    List<SettingAssessDO> findAssessByDataList(String year, String month, String caliberId, String assessName);

    /**
     * 保存考核点数据
     */
    void doSaveOrUpdateAssess(List<SettingAssessDataDTO> data);

    /**
     * 删除考核点配置
     */
    void doDeleteByAssessName(String year, String caliberId, String assessName);

    /**
     * 查询应用状态的考核点名称列表
     */
    List<String> findAllValidAssess(String year, String caliberId);

    /**
     * 判断考核点名称是否可用 （禁止重复名称）
     *
     * @param assessName 准确率名称
     * @return true 校验通过 false 校验失败；名称重复
     */
    Boolean checkNameAvailable(String year, String caliberId, String assessName);

    /**
     * 查询时间段内 应用状态的所有考核点；
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param caliberId 口径id
     * @return key yy-mm  vale 月份对应的考核点数据
     */
    Map<String, List<SettingAssessDO>> findAssessSettingByData(Date startDate, Date endDate, String caliberId);

}