package com.tsintergy.lf.serviceapi.base.weather.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class WeatherSourceCurveDTO implements Serializable {

    private List<BigDecimal> hisWeatherList;

    private List<BigDecimal> fcWeatherList;

    private List<BigDecimal> accuracyList;

    private String source;

    private String sourceName;

}


