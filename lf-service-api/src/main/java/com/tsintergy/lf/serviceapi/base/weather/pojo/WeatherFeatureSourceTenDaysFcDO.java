package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

@Data
@Entity
@Table(name = "weather_feature_source_ten_days_fc_service")
public class WeatherFeatureSourceTenDaysFcDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "ym")
    private String ym;

    @Column(name = "city_id")
    private String cityId;

    @Column(name = "type")
    private String type;

    @Column(name = "highest_temperature")
    private BigDecimal highestTemperature;

    @Column(name = "lowest_temperature")
    private BigDecimal lowestTemperature;

    @Column(name = "ave_temperature")
    private BigDecimal aveTemperature;

    @Column(name = "highest_humidity")
    private BigDecimal highestHumidity;

    @Column(name = "lowest_humidity")
    private BigDecimal lowestHumidity;

    @Column(name = "ave_humidity")
    private BigDecimal aveHumidity;

    @Column(name = "max_winds")
    private BigDecimal maxWinds;

    @Column(name = "min_winds")
    private BigDecimal minWinds;

    @Column(name = "ave_winds")
    private BigDecimal aveWinds;

    @Column(name = "ave_radiance")
    private BigDecimal aveRadiance;

    @Column(name = "rainfall")
    private BigDecimal rainfall;

    @Column(name = "extreme_highest_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date extremeHighestDate;

    @Column(name = "extreme_lowest_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date extremeLowestDate;

    @Column(name = "max_rainfall")
    private BigDecimal maxRainfall;

    @Column(name = "max_rainfall_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date maxRainfallDate;

    @Column(name = "extreme_max_winds_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date extremeMaxWindsDate;

    @Column(name = "source_type")
    private String sourceType;

    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

}
