package com.tsintergy.lf.serviceapi.base.weather.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WeatherSourcePeriodDTO implements Serializable {

    private String type;

    private String typeName;

    private String source;

    private String sourceName;

    private BigDecimal accuracy;

    private String dateStr;

    public WeatherSourcePeriodDTO(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public WeatherSourcePeriodDTO() {
    }
}
