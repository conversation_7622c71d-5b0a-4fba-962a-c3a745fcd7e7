package com.tsintergy.lf.serviceapi.base.weather.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WeatherFeatureSourceDTO implements DTO {

    private String cityName;

    private String startDate;

    private String endDate;

    private String sourceName;

    private BigDecimal avgHighTemp;

    private BigDecimal avgLowTemp;

    private String extremeHighTempDate;

    private String extremeLowTempDate;

    private BigDecimal avgHumidity;

    private String rainfallDate;

    private BigDecimal avgMaxWindSpeed;

    private String extremeMaxWindSpeedDate;

    private BigDecimal avgRadiance;

}
