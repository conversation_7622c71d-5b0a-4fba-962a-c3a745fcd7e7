/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;


import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryMonthHisServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryQuarterHisServiceDO;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: Liujp
 */
public interface LoadFeatureIndustryQuarterHisService {
    List<LoadFeatureIndustryQuarterHisServiceDO> getLoadFeatureIndustryQuarterHisServiceList(String cityId, String caliberId, String year, String quarter);

    List<LoadFeatureIndustryQuarterHisServiceDO> getLoadFeatureIndustryQuarterHisServiceAllList(String cityId, String industryId, String startYear, String endYear);

    void saveOrUpdate(LoadFeatureIndustryQuarterHisServiceDO loadFeatureIndustryQuarterHisServiceDO);

    List<LoadFeatureIndustryQuarterHisServiceDO> getLoadFeatureIndustryQuarterHisServiceListByType(String cityId, String type, String year, String quarter);

    List<LoadFeatureIndustryQuarterHisServiceDO>  getLoadFeatureIndustryQuarterHisServiceList(String cityId, String type, Date startDate, Date endDate);
}