/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description: 数据管理负荷数据返回类 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/11/24 17:22
 * @Version: 1.0.0
 */
@Data
public class LoadHisDataDTO implements Load{
    private String id;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date date;

    private String city;

    private String industryName;

    private String industryNo;

    private String factoryType;

    private String factoryName;

    private String week;

    private String algorithm;

    private List<BigDecimal> data;

    @Override
    public List<BigDecimal> getloadList() {
        return data;
    }
}