package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcMeteoBatchDO;

import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
public interface WeatherCityFcMeteoBatchService {

    void doSaveOrUpdate(WeatherCityFcMeteoBatchDO weatherCityFcMeteoBatchDO);

    void insertYesterday24HourData(WeatherCityFcMeteoBatchDO weatherCityFcMeteoBatchDO);

    List<WeatherCityFcMeteoBatchDO> findBatchWeatherFcByHour(String cityId, Date date, Integer type, String hour);

    List<WeatherCityFcMeteoBatchDO> findBatchWeatherFcByHourDate(String cityId, Date startDate, Date endDate, Integer type, String hour);

    List<WeatherCityFcMeteoBatchDO> findWeatherCityFcDO(String cityId, Integer type, Date date, String orderId);

}
