package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmCurveDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyDetailValueDataDTO;

import java.util.Date;
import java.util.List;

public interface AccuracyAlgorithmService {

    List<AccuracyAlgorithmDataDTO> getAlgorithmListData(String cityId, Date startDate, Date endDate,
                                                        List<String> algorithmIds, String caliberId) throws Exception;

    List<AccuracyDetailValueDataDTO> getAlgorithmListDetailData(String cityId, Date startDate, Date endDate,
                                                                List<String> algorithmIds, String caliberId, String days) throws Exception;

    List<AccuracyAlgorithmCurveDataDTO> getAlgorithmListDetailCurveData(String cityId, Date startDate, Date endDate,
                                                                        List<String> algorithmIds, String caliberId, String days) throws Exception;
}
