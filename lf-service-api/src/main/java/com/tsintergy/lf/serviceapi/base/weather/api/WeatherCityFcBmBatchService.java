package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBmBatchDO;

import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
public interface WeatherCityFcBmBatchService {

    void doSaveOrUpdate(WeatherCityFcBmBatchDO weatherCityFcBmBatchDO);

    void insertYesterday24HourData(WeatherCityFcBmBatchDO weatherCityFcBmBatchDO);

    List<WeatherCityFcBmBatchDO> findBatchWeatherFcByHour(String cityId, Date date, Integer type, String hour);

    List<WeatherCityFcBmBatchDO> findBatchWeatherFcByHourDate(String cityId, Date startDate, Date endDate, Integer type, String hour);

    List<WeatherCityFcBmBatchDO> findWeatherCityFcDO(String cityId, Integer type, Date date, String orderId);

}
