
package com.tsintergy.lf.serviceapi.base.evalucation.api;


import com.tsintergy.lf.serviceapi.base.analyze.dto.StatisticsDeviationAnalyzeDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityDayAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.MultipleAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsCityDayDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: StatisticsCityDayFcService.java, v 0.1 2018-01-31 10:20:07 tao Exp $$
 */

public interface StatisticsCityDayFcService {

    /**
     * search entity list
     */
    List<StatisticsCityDayFcDO> queryStatisticsCityDayFcDO(Date date) throws Exception;

    List<MultipleAccuracyDTO> getMultipleDayAccuracyList(Map<String, String> map, String caliberId, String algorithmId,
        Date startDate, Date endDate) throws Exception;
    List<StatisticsCityDayFcDO> queryStatisticsCity(String cityId, String algorithmId, String caliberId, Date date,
        Boolean report) throws Exception;

    List<StatisticsCityDayFcDO> findStatisticsByDate(String cityId, String algorithmId, String caliberId,
        Date startDate, Date endDate, Boolean report) throws Exception;

    List<StatisticsCityDayFcDO> getDayAccuracyList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception;
    List<StatisticsCityDayFcDO> getDayAccuracyList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate, Boolean isReport) throws Exception;

    /**
     * create entity
     */
    StatisticsCityDayFcDO doCreate(StatisticsCityDayFcDO vo) throws Exception;

    /**
     * delete entity by object
     */
    void doRemoveStatisticsCityDayFcDO(StatisticsCityDayFcDO vo) throws Exception;

    /**
     * delete entity by PK
     */
    void doRemoveStatisticsCityDayFcDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     */
    StatisticsCityDayFcDO doUpdateStatisticsCityDayFcDO(StatisticsCityDayFcDO vo) throws Exception;

    /**
     * find entity by PK
     */
    StatisticsCityDayFcDO findStatisticsCityDayFcDOByPk(Serializable pk) throws Exception;

    /**
     * 功能描述: <br> 误差分析
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/8/8 13:53
     */
    public StatisticsDeviationAnalyzeDTO findStatisticsDeviationAnalyzeDTO(String cityId, String caliberId,
        Date startTime, Date endTime) throws Exception;


    /**
     * 功能描述: 查询最终上报准确率<br> 〈〉
     *
     * @return:java.util.List<com.load.evalucation.persistent.StatisticsCityDayFcDO>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/23 9:12
     */
    public List<StatisticsCityDayFcDO> getReportAccuracy(String cityId, String caliberId, Date startTime, Date endTime)
        throws Exception;


    StatisticsCityDayDTO getAccuracyAvg(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception;

    List<StatisticsDayDTO> getDayAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception;

    List<StatisticsCityDayFcDO> getDayAlgorithmsAccuracy(String cityId, String caliberId, List<String> algorithmIds, Date startDate,
                                          Date endDate) throws Exception;


    List<StatisticsCityDayFcDO> doSaveOrUpdateStatisticsCityDayFcDOs(
        List<StatisticsCityDayFcDO> statisticsCityDayFcVOS) throws Exception ;


    List<CityDayAccuracyDTO> getCityDayAccuracyDTOList(List<String> cityIds,String caliberId,String algorithmId,Date date)throws Exception ;
}