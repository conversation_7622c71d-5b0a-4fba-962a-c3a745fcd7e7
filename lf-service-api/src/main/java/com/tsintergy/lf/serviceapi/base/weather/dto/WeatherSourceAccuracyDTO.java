package com.tsintergy.lf.serviceapi.base.weather.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WeatherSourceAccuracyDTO implements Serializable {

    private String algorithmName;

    private String dateStr;

    private String source;

    private String sourceName;

    private BigDecimal compositeAccuracy;

    private BigDecimal dayBgdAccuracy;

    private BigDecimal dayTroughAccuracy;

    private BigDecimal dayNightTroughAccuracy;

    private BigDecimal dayMaxAccuracy;

    private String dayBgdSourceName;

    private String daySourceName;

    private String dayNightSourceName;

    private String dayMaxSourceName;

    public WeatherSourceAccuracyDTO(String algorithmName, String source, String sourceName, BigDecimal compositeAccuracy, BigDecimal dayBgdAccuracy, BigDecimal dayTroughAccuracy, BigDecimal dayNightTroughAccuracy, BigDecimal dayMaxAccuracy, String dateStr) {
        this.algorithmName = algorithmName;
        this.source = source;
        this.sourceName = sourceName;
        this.compositeAccuracy = compositeAccuracy;
        this.dayBgdAccuracy = dayBgdAccuracy;
        this.dayTroughAccuracy = dayTroughAccuracy;
        this.dayNightTroughAccuracy = dayNightTroughAccuracy;
        this.dayMaxAccuracy = dayMaxAccuracy;
        this.dateStr = dateStr;
    }

    public WeatherSourceAccuracyDTO() {
    }

}
