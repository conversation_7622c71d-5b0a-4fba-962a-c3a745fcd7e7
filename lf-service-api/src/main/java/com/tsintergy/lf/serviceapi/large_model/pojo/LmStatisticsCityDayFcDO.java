package com.tsintergy.lf.serviceapi.large_model.pojo;


import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 预测统计结果（日）
 */
@Data
@Entity
@Table(name = "lm_statistics_city_day_fc_service")
@EntityUniqueIndex({"cityId", "date", "algorithmId", "caliberId"})
public class LmStatisticsCityDayFcDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_name")
    private String algorithmName;


    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 准确率
     */
    @Column(name = "accuracy")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal accuracy;

    /**
     * 偏差率
     */
    @Column(name = "deviation")
    private BigDecimal deviation;

    /**
     * 合格率
     */
    @Column(name = "pass")
    private BigDecimal pass;

    /**
     * 离散度
     */
    @Column(name = "dispersion")
    private BigDecimal dispersion;

    /**
     * 考核标准准确率
     */
    @Column(name = "standard_accuracy")
    private BigDecimal standardAccuracy;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /**
     * 是否上报
     */
    @Column(name = "report")
    private Boolean report;


}
