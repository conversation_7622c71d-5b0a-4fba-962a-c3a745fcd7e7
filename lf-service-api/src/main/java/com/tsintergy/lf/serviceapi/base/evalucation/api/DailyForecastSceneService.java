package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.core.enums.DateTypeEnum;
import com.tsintergy.lf.core.enums.WeatherSeceneEnum;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAlgorithmAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.ForecastDeviationDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.ForecastSceneAccracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DailyForecastSceneDO;

import java.util.Date;
import java.util.List;

public interface DailyForecastSceneService {
    List<DailyForecastSceneDO> findDailyForecastScene(String cityId, Date startDate, Date endDate, Integer dateType,
                                                      Integer weatherType);

    void saveOrUpdate(List<DailyForecastSceneDO> dailyForecastSceneList) throws Exception;

    void doStatDailyForecastScene(String cityIdList, String settingId) throws Exception;

    ForecastSceneAccracyDTO getCityAvgAccuracyList(String cityId, String caliberId, Date startDate, Date endDate,
                                                   WeatherSeceneEnum weatherSecene, DateTypeEnum dateType, List<String> algorithmId);

    List<CityAccuracyDTO> getCityAccuracyList(String cityId, String caliberId, Date startDate, Date endDate, WeatherSeceneEnum weatherSecene,
                                              DateTypeEnum dateType, String algorithmId, String accuracyName);

    List<CityAlgorithmAccuracyDTO> getCityAlgorithmAccuracyList(String cityId, String caliberId, Date startDate, Date endDate, WeatherSeceneEnum weatherSecene,
                                                                DateTypeEnum dateType, List<String> algorithmId, String accuracyName) throws Exception;

    List<ForecastDeviationDTO> getForecastDeviationList(String cityId, String caliberId, Date startDate, Date endDate,
                                                        WeatherSeceneEnum weatherSecene, DateTypeEnum dateType, String algorithmId, String accuracyName) throws Exception;

    void deleteDailyForecastScene(List<String> settingId);
}
