/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCityDayLongListVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureLongDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureSourceDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureSourceLongDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayLongFcDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/17 15:03
 * @Version: 1.0.0
 */
public interface WeatherFeatureCityDayLongFcService {
    WeatherFeatureLongDTO findMonthFcPageByParam(String cityId, Date startDate, Date endDate, String caliberId) throws Exception;
    List<WeatherFeatureCityDayLongFcDO> findByParam(String cityId, Date startDate,Date endDate) throws Exception;

    List<WeatherFeatureSourceLongDTO> findSourceByParam(String cityId, Integer type, String startDate, String endDate, String sourceType) throws Exception;

    WeatherFeatureSourceDTO findSourceFeatureByParam(String cityId, Integer type, String startDate, String endDate, String sourceType) throws Exception;

    void TenDaysFcLongFeature(Date startDate, Date endDate, String ym, String name, String code) throws Exception;

    void MonthFcLongFeature(Date startDate, Date endDate, String ym, String code) throws Exception;

    void saveOrUpdate(WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO) throws Exception;
    void saveOrUpdate(WeatherFeatureCityDayLongListVO weatherFeatureCityDayLongListVO) throws Exception;

    void saveOrUpdateList(List<WeatherFeatureCityDayLongFcDO> fcDOS);
}
