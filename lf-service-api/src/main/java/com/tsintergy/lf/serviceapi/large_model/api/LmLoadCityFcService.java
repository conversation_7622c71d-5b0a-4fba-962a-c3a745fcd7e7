package com.tsintergy.lf.serviceapi.large_model.api;

import com.tsintergy.lf.serviceapi.large_model.pojo.LmLoadCityFcDO;

import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/7 13:53
 **/
public interface LmLoadCityFcService {

    void saveOrUpdateList(List<LmLoadCityFcDO> list);

    List<LmLoadCityFcDO> find(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate);

}
