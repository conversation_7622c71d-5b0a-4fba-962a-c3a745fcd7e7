package com.tsintergy.lf.serviceapi.large_model.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;

/** @Description
 * <AUTHOR>
 * 近15天平均准确率
 * @Date 2025/7/6 11:03
 **/
@Data
@Entity
@Table(name = "LM_STATISTICS_CITY_NEAR_15_DAY_FC_SERVICE")
@EntityUniqueIndex({"cityId", "date","type", "algorithmId", "caliberId"})
public class LmStatisticsCityNear15DayFcDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 1常规日 2休息日 3节假日 4极端天气
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_name")
    private String algorithmName;


    @Column(name = "algorithm_id")
    private String algorithmId;


    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 准确率
     */
    @Column(name = "accuracy")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal accuracy;


    /**
     * 合格率
     */
    @Column(name = "pass")
    private BigDecimal pass;
}
