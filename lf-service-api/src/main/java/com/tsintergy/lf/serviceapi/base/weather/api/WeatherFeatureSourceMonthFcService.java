package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureSourceMonthFcDO;

import java.util.List;

public interface WeatherFeatureSourceMonthFcService {

    List<WeatherFeatureSourceMonthFcDO> findByParam(String cityId, String startDate, String endDate, String sourceType) throws Exception;

    void saveOrUpdate(WeatherFeatureSourceMonthFcDO weatherFeatureSourceMonthFcDO) throws Exception;
}
