package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.datamanage.dto.LoadBatchQueryDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/3 14:29
 */
public interface LoadCityFcBatchService {

    void doSave(LoadCityFcDO loadCityFcDO) throws Exception;

    List<LoadCityFcBatchDO> findByCondition(String cityId, Date date, String caliberId, String algorithmId)
            throws Exception;

    LoadBatchQueryDTO findLoad(List<Integer> batchIds, List<String> algorithmIds, String cityId, String caliberId,
                               Date startDate, Date endDate) throws Exception;

    LoadBatchQueryDTO findWeather(List<Integer> batchIds, List<String> algorithmIds, String cityId,
                                  String caliberId, Date startDate, Date endDate, Integer type) throws Exception;

    List<LoadCityFcBatchDO> findByCondition(String cityId, Date startDate, Date endDate, String caliberId, String algorithmId)
            throws Exception;

    List<LoadCityFcBatchDO> findByConditionByBatchId(String cityId, Date startDate, Date endDate,
                                                     String caliberId, String algorithmId, Integer batchId) throws Exception;

    List<LoadCityFcBatchDO> findByAllAlgorithmCondition(String cityId, Date startDate,Date endDate, String caliberId, List<String> algorithmId)
            throws Exception;


    List<LoadCityFcBatchDO> findDataList(Date date, String cityId, String algorithmId, String caliberId,
                                         Boolean report) throws Exception;

    void doDelete(List<LoadCityFcBatchDO> data) throws Exception;

    void doSaveOrUpdate(LoadCityFcBatchDO data) throws Exception;

    void saveOrUpdate(LoadCityFcBatchDO data) throws Exception;

    List<LoadCityFcBatchDO> findOneByBatchTime(String cityId, Date startDate, Date endDate, String caliberId, String algorithmId, String batchIds);

    List<LoadCityFcBatchDO> findByBatchData(String cityId, Date startDate, Date endDate, String caliberId, String algorithmId, String batchIds) throws Exception;
}
