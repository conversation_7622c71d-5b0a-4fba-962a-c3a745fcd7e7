
package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.load.dto.CityValueDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityHisService.java, v 0.1 2018-01-31 11:00:06 tao Exp $$
 */

public interface WeatherCityHisService {
    /**
     * search entity list
     *
     * @param param
     * @return
     * @throws BusinessException
     */
    DataPackage queryWeatherCityHisDO(DBQueryParam param) throws Exception;

    /**
     * create entity
     *
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityHisDO doCreate(WeatherCityHisDO vo) throws Exception;

    /**
     * delete entity by object
     *
     * @param vo
     * @throws Exception
     */
     void doRemoveWeatherCityHisDO(WeatherCityHisDO vo) throws Exception;

    /**
     * delete entity by PK
     *
     * @param pk
     * @throws Exception
     */
     void doRemoveWeatherCityHisDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     *
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityHisDO doUpdateWeatherCityHisDO(WeatherCityHisDO vo) throws Exception;

    /**
     * find entity by PK
     *
     * @param pk
     * @return
     * @throws Exception
     */
     WeatherCityHisDO findWeatherCityHisDOByPk(Serializable pk) throws Exception;

    /**
     * 查询气象历史数据
     *
     * @param cityId    城市ID
     * @param type      气象类型
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
     List<WeatherCityHisDO> findWeatherCityHisDOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * find 24weatherCity by date and cityIds
     *
     * @param date
     * @param cityIds
     * @return  返回的结果对象城市为城市id  不是气象城市id
     * @throws Exception
     */
     List<CityValueDTO> find24WeahterCityByDateAndCityIds(Date date, List<String> cityIds, Integer weatherType) throws Exception;

    /**
     * 查询气象历史数据
     *
     * @param cityId    城市ID
     * @param type      气象类型
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
    List<WeatherDTO> findWeatherCityHisDTOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    List<WeatherDTO> findWeatherCityHisAllDTOs(String cityId, Integer type, Date startDate, Date endDate, String source) throws Exception;

    void doInsertOrUpdate(WeatherCityHisDO weatherCityHisVO) throws Exception;

    void doSaveAllData(WeatherCityHisDO weatherCityHisDO);

    List<WeatherCityHisDO> findWeatherCityHisDOSBySQLDate(String cityId, Integer type, java.sql.Date startDate,
        java.sql.Date endDate) throws Exception;

    List<WeatherCityHisDO> findWeatherCityHisDOSByCityIds(List<String> cityIds, Integer type, Date date) throws Exception;
    WeatherCityHisDO findWeatherCityHisDO(String cityIds, Integer type, Date date) throws Exception;

    List<WeatherCityHisDO> findAllWeather() throws Exception;

    List<WeatherCityHisDO> findWeatherByDates(String cityId, Integer type, List<Date> dateList) throws Exception;

    List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception;


    List<BigDecimal> findWeatherCityHisValueList(String cityId, Date date, Integer weatherType) throws Exception;

    List<BigDecimal> findWeatherCityHisValueList(String cityId, Date startDate,Date endDate ,Integer weatherType) throws Exception;



    List<BigDecimal> find96WeatherCityHisValue(Date date, String cityId, Integer type) throws Exception;

    List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception;

    void doSaveOrUpdateBatch(List<WeatherCityHisDO> WeatherCityHisDOS);

    List<WeatherCityHisDO> queryWeatherCityHisDOAllTypeList(String weatherCityId, List<Integer> typeList, List<Date> dateList);

    List<WeatherCityHisDO> queryWeatherCityHisDOList(List<Date> showDates, String cityId, Integer type);

    void downloadWeatherReportDaily(Date date, String key) throws Exception;

}
