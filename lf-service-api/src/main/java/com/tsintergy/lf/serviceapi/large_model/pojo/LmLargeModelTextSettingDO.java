package com.tsintergy.lf.serviceapi.large_model.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Data
@Entity
@NoArgsConstructor
@Table(name = "LM_LARGE_MODEL_TEXT_SETTING")
public class LmLargeModelTextSettingDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "1")
    private String id;

    /**
     * 参数键
     */
    @Column(name = "field")
    @ApiModelProperty(value = "参数键",example = "peak_time")
    private String field;

    /**
     * 参数值
     */
    @Column(name = "value")
    @ApiModelProperty(value = "参数值",example = "05:30~07:00,10:00~12:00,18:00~20:00")
    private String value;

    /**
     * 参数名
     */
    @Column(name = "name")
    @ApiModelProperty(value = "参数名",example = "尖峰时段")
    private String name;

    /**
     * 描述
     */
    @Column(name = "description")
    @ApiModelProperty(value = "描述",example = "无")
    private String description;
}
