package com.tsintergy.lf.serviceapi.large_model.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/7 14:31
 **/
@Data
@Entity
@Table(name = "LM_LOAD_FEATURE_CITY_DAY_FC_SERVICE")
@EntityUniqueIndex({"date", "algorithmId"})
public class LmLoadFeatureCityDayFcServiceDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @ApiModelProperty("预测日期")
    @Column(name = "date")
    private Date date;

    @Column(name = "algorithm_id")
    private String algorithmId;

    @Column(name = "max_time")
    private String maxTime;

    @Column(name = "max_index")
    private Integer maxIndex;

    @ApiModelProperty("最大负荷")
    @Column(name = "max_load")
    private BigDecimal maxLoad;

    @ApiModelProperty("保供平均负荷")
    @Column(name = "bg_ave_load")
    private BigDecimal bgAveLoad;

    @ApiModelProperty("午间最小")
    @Column(name = "noon_min")
    private BigDecimal noonMin;

    @ApiModelProperty("夜间最小")
    @Column(name = "evening_min")
    private BigDecimal eveningMin;

    @ApiModelProperty("保供时段爬坡率")
    @Column(name = "bg_rate")
    private BigDecimal bgRate;

    @Column(name = "different")
    private BigDecimal different;

    @Column(name = "min_load")
    private BigDecimal minLoad;

    @Column(name = "min_time")
    private String minTime;

    @Column(name = "min_index")
    private Integer minIndex;

    @Column(name = "bg_max_index")
    private Integer bgMaxIndex;


}
