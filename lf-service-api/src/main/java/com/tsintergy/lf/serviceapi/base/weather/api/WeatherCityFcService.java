
package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDeatilDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcCopy1DO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcCopy2DO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherCityFcService.java, v 0.1 2018-01-31 10:59:49 tao Exp $$
 */

public interface WeatherCityFcService  extends BaseFcWeatherService{
     /**
     *  search entity list
     * @param param
     * @return
     * @throws BusinessException
     */
    DataPackage queryWeatherCityFcDO(DBQueryParam param) throws Exception;

    DataPackage queryWeatherCityFcDO1(DBQueryParam param) throws Exception;


    DataPackage queryWeatherCityFcDO2(DBQueryParam param) throws Exception;


    List<WeatherDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    List<WeatherDTO> findWeatherCityFcAllDTOs(String cityId, Integer type, Date startDate, Date endDate, String source) throws Exception;

    /**
     * create entity
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityFcDO doCreate(WeatherCityFcDO vo) throws Exception;

    WeatherCityFcCopy1DO doCreate(WeatherCityFcCopy1DO vo) throws Exception;

    WeatherCityFcCopy2DO doCreate(WeatherCityFcCopy2DO vo) throws Exception;

    /**
     *  delete entity by object
     * @param vo
     * @throws Exception
     */
     void doRemoveWeatherCityFcDO(WeatherCityFcDO vo) throws Exception;

    /**
     * delete entity by PK
     * @param pk
     * @throws Exception
     */
     void doRemoveWeatherCityFcDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityFcDO doUpdateWeatherCityFcDO(WeatherCityFcDO vo) throws Exception;

    WeatherCityFcCopy1DO doUpdateWeatherCityFcDO(WeatherCityFcCopy1DO vo) throws Exception;


    WeatherCityFcCopy2DO doUpdateWeatherCityFcDO(WeatherCityFcCopy2DO vo) throws Exception;


    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws Exception
     */
     WeatherCityFcDO findWeatherCityFcDOByPk(Serializable pk) throws Exception;

    /**
     * 查询气象预测数据
     * @param cityId 气象城市ID
     * @param type 气象类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * 查询該城市目标日的所有基础气象数据
     * @param cityId
     * @param targetDate
     * @return
     * @throws Exception
     */
    List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate, String algorithmId) throws Exception;

    List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date startDate, Date endDate) throws Exception;

    WeatherCityFcDO findWeatherCityFcDO(String cityId,Integer type, Date date) throws Exception;

    void doInsertOrUpdate(WeatherCityFcDO weatherCityFcVO) throws Exception;

    void doSaveAllData(WeatherCityFcDO weatherCityFcDO) throws Exception;

    void doSaveAllData1(WeatherCityFcCopy1DO weatherCityFcDO) throws Exception;

    void doInsertOrUpdate(WeatherCityFcCopy1DO weatherCityFcVO) throws Exception;

    void doInsertOrUpdate(WeatherCityFcCopy2DO weatherCityFcVO) throws Exception;


    void doInsertOrUpdateList0(List<WeatherCityFcDO> weatherCityFcVOs) throws Exception;
    void doInsertOrUpdateList1(List<WeatherCityFcCopy1DO> weatherCityFcVOs) throws Exception;

    void doInsertOrUpdateList2(List<WeatherCityFcCopy2DO> weatherCityFcVOs) throws Exception;

    List<BigDecimal> find96WeatherCityFcValue(Date date, String cityId, Integer type) throws Exception;

    List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception;

    WeatherDeatilDTO findWeatherDeatilDTO(Date date, String cityId) throws Exception;

    List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Date date, Date date1) throws Exception;

    void doInsertOrUpdateBatch(List<WeatherCityFcDO> updateD0List);

    List<WeatherCityFcCopy1DO> findWeatherCityFcDOs1(String cityId, Date date, Date date1,Integer type,String orderId) throws Exception;

    List<WeatherCityFcCopy2DO> findWeatherCityFcDOs2(String cityId, Date date, Date date1,Integer type) throws Exception;

    List<WeatherCityFcDO> getWeatherCityFcDOList(List<String> cityIds,Integer type,Date startDate,Date endDate)throws Exception;

    List<WeatherCityFcDO> queryWeatherCityHisDOAllTypeList(String weatherCityId, List<Integer> typeList, List<Date> dateList);

    /***
     * @Description 批量插入数据
     * @Date 2023/1/16 16:10
     * <AUTHOR>
     **/
    void doSaveOrUpdateList(List<WeatherCityFcDO> result);

    List<WeatherCityFcCopy1DO> findBatchWeatherFcByHour(String cityId, Date date, Integer type, String hour);

    List<WeatherCityFcCopy1DO> findBatchWeatherFcByHourData(String cityId, Date startDate, Date endDate, Integer type, String hour) throws Exception;

    List<WeatherCityFcCopy1DO> findBatchWeatherFcByHourAllData(String cityId, Date startDate, Date endDate, Integer type, String hour) throws Exception;

    List<WeatherCityFcDO> statProvinceWeather(List<? extends BaseWeatherDO> weathers);
}
