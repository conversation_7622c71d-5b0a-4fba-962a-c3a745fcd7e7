package com.tsintergy.lf.serviceapi.large_model.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 13:50
 **/

@Data
@Entity
@Table(name = "LM_PARAMETER_DETAILS")
public class LmParameterDetailsDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @ApiModelProperty("算法名称")
    @Column(name = "algorithm_name")
    private String algorithmName;

    @ApiModelProperty("参数名称")
    @Column(name = "param_name")
    private String paramName;

    @ApiModelProperty("参数值")
    @Column(name = "param_value")
    private String paramValue;

    @ApiModelProperty("类型")
    @Column(name = "type")
    private String type;

    @ApiModelProperty("参数所属类型 0 控制参数 1算法参数,只可修改belong_type =1d的")
    @Column(name = "belong_type")
    private String belongType;

    @ApiModelProperty("参数描述")
    @Column(name = "details")
    private String details;
}
