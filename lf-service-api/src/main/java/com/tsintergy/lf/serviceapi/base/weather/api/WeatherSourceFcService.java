package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.dto.*;

import java.util.Date;
import java.util.List;

/**
 *  Description:  <br>     <AUTHOR>  @create 2021/7/27  @since 1.0.0 
 */
public interface WeatherSourceFcService {

    List<WeatherFcDTO> findSourceWeatherPoint(List<String> cityIdS, List<String> sources, Date startDate, Date endDate,
                                              String type, String hour, Integer weatherType) throws Exception;

    List<WeatherFcFeatureDTO> findSourceWeatherFeature(List<String> cityIdS, List<String> sources,
                                                       Date startDate, Date endDate, String hour) throws Exception;

    List<WeatherSourceCurveDTO> findSourceWeatherCurve(String cityId, List<String> sources, Date date, String hour, String type) throws Exception;

    List<WeatherSourcePeriodDTO> findSourceWeatherPeriod(String cityId, List<String> sources, Date date, String hour, String type) throws Exception;

    List<WeatherSourcePeriodDTO> findSourceOneWeatherPeriod(String cityId, List<String> sources, Date date, String hour, String type, String periodType) throws Exception;

    List<WeatherSourcePeriodDTO> findSourceDaysWeatherPeriod(String cityId, List<String> sources, Date date, String hour, String type, String periodType, String days) throws Exception;

    List<WeatherDeviationDTO> findSourceWeatherNewFeature(List<String> cityIdS, List<String> sources,
                                                       Date startDate, Date endDate, String hour, Integer weatherType, Integer weatherFeature) throws Exception;

    List<WeatherFcAccuracyDTO> findSourceWeatherAccuracy(String cityId, List<String> sources, Date startDate,
                                                         Date endDate, String hour) throws Exception;

    WeatherFeatureValuesDTO findSourceWeatherNewAccuracy(String cityId, List<String> sources, Date startDate,
                                                         Date endDate, String hour, Integer weatherType, Integer weatherFeature) throws Exception;

    WeatherSourceAlgorithmDTO findSourceAlgorithmResult(String cityId, List<String> sources, Date startDate,
                                                         Date endDate, String batchId, String algorithmId) throws Exception;

    List<WeatherSourceAccuracyDTO> findSourceAlgorithmAccuracy(String cityId, List<String> sources, Date startDate,
                                                        Date endDate, String batchId) throws Exception;

    List<WeatherSourceAccuracyDTO> findSourceAlgorithmFeature(String cityId, List<String> sources, Date startDate,
                                                               Date endDate, String hour, String algorithmId) throws Exception;

    List<WeatherFcPointDTO> findSourceWeatherPointList(String cityId, Date startDate, Date endDate) throws Exception;

}
