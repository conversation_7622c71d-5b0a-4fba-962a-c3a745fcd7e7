package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.math.BigDecimalFunctions;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

public class WeatherSourceFcAccuracyCompareDTO implements Serializable {

    public static final String TEMP_DIFF_CONCLUSION_UP = "上升趋势";

    public static final String TEMP_DIFF_CONCLUSION_DOWN = "下降趋势";

    public static final String TEMP_DIFF_CONCLUSION_STABLE = "相对稳定";

    public static final String MAX_LOAD_FORECAST_UP = "有所上涨";

    public static final String MAX_LOAD_FORECAST_DOWN = "有所下降";

    public static final String MAX_LOAD_FORECAST_STABLE = "相对稳定";

    public static final String PERIOD_BAOGONG = "保供";

    public static final String PERIOD_NOON = "午间";

    public static final String PERIOD_NIGHT = "夜间";

    public static final String DATE_TYPE_CHANGE_NO = "无";

    public static final String DATE_TYPE_CHANGE_YES = "有";

    private static final long serialVersionUID = 5498054366908507070L;

    private BigDecimal accuracy;

    // 1: 气象局气象；2: BM气象；3: EC气象
    private Integer weatherFcSource;

    private String weatherFcSourceName;

    private String forecastDate;

    private String forecastDateType;

    private String compareDate;

    private String compareDateType;

    private String dateTypeChange;

    private BigDecimal maxTemperature;

    private BigDecimal avgTemperature;

    private BigDecimal minTemperature;

    private BigDecimal maxTemperatureDiff;

    private BigDecimal avgTemperatureDiff;

    private BigDecimal minTemperatureDiff;

    private BigDecimal baogongAvgTemperatureDiff;

    private BigDecimal noonAvgTemperatureDiff;

    private BigDecimal nightAvgTemperatureDiff;

    private String rainPeriod;

    private BigDecimal avgHumidityFeatureDiff;

    private BigDecimal avgEffectiveTemperatureDiff;

    private String temperatureConclusion;

    private String maxLoadForecast;

    private String changeMaxPeriod;

    private String socialLoadForecast;

    private String dateTypeChangeConclusion;

    private String loadChangeConclusion;

    public BigDecimal getAccuracy() {
        return accuracy == null ? accuracy : accuracy.setScale(4, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setAccuracy(BigDecimal accuracy) {
        this.accuracy = accuracy;
    }

    public Integer getWeatherFcSource() {
        return weatherFcSource;
    }

    public void setWeatherFcSource(Integer weatherFcSource) {
        this.weatherFcSource = weatherFcSource;
    }

    public String getWeatherFcSourceName() {
        return weatherFcSourceName;
    }

    public void setWeatherFcSourceName(String weatherFcSourceName) {
        this.weatherFcSourceName = weatherFcSourceName;
    }

    public String getForecastDate() {
        return forecastDate;
    }

    public void setForecastDate(String forecastDate) {
        this.forecastDate = forecastDate;
    }

    public String getForecastDateType() {
        return forecastDateType;
    }

    public void setForecastDateType(String forecastDateType) {
        this.forecastDateType = forecastDateType;
    }

    public String getCompareDate() {
        return compareDate;
    }

    public void setCompareDate(String compareDate) {
        this.compareDate = compareDate;
    }

    public String getCompareDateType() {
        return compareDateType;
    }

    public void setCompareDateType(String compareDateType) {
        this.compareDateType = compareDateType;
    }

    public String getDateTypeChange() {
        return dateTypeChange;
    }

    public void setDateTypeChange(String dateTypeChange) {
        this.dateTypeChange = dateTypeChange;
    }

    public BigDecimal getMaxTemperature() {
        return maxTemperature == null ? maxTemperature : maxTemperature.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setMaxTemperature(BigDecimal maxTemperature) {
        this.maxTemperature = maxTemperature;
    }

    public BigDecimal getAvgTemperature() {
        return avgTemperature == null ? avgTemperature : avgTemperature.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setAvgTemperature(BigDecimal avgTemperature) {
        this.avgTemperature = avgTemperature;
    }

    public BigDecimal getMinTemperature() {
        return minTemperature == null ? minTemperature : minTemperature.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setMinTemperature(BigDecimal minTemperature) {
        this.minTemperature = minTemperature;
    }

    public BigDecimal getMaxTemperatureDiff() {
        return maxTemperatureDiff == null ? maxTemperatureDiff : maxTemperatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setMaxTemperatureDiff(BigDecimal maxTemperatureDiff) {
        this.maxTemperatureDiff = maxTemperatureDiff;
    }

    public BigDecimal getAvgTemperatureDiff() {
        return avgTemperatureDiff == null ? avgTemperatureDiff : avgTemperatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setAvgTemperatureDiff(BigDecimal avgTemperatureDiff) {
        this.avgTemperatureDiff = avgTemperatureDiff;
    }

    public BigDecimal getMinTemperatureDiff() {
        return minTemperatureDiff == null ? minTemperatureDiff : minTemperatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setMinTemperatureDiff(BigDecimal minTemperatureDiff) {
        this.minTemperatureDiff = minTemperatureDiff;
    }

    public BigDecimal getBaogongAvgTemperatureDiff() {
        return baogongAvgTemperatureDiff == null ? baogongAvgTemperatureDiff : baogongAvgTemperatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setBaogongAvgTemperatureDiff(BigDecimal baogongAvgTemperatureDiff) {
        this.baogongAvgTemperatureDiff = baogongAvgTemperatureDiff;
    }

    public BigDecimal getNoonAvgTemperatureDiff() {
        return noonAvgTemperatureDiff == null ? noonAvgTemperatureDiff : noonAvgTemperatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setNoonAvgTemperatureDiff(BigDecimal noonAvgTemperatureDiff) {
        this.noonAvgTemperatureDiff = noonAvgTemperatureDiff;
    }

    public BigDecimal getNightAvgTemperatureDiff() {
        return nightAvgTemperatureDiff == null ? nightAvgTemperatureDiff : nightAvgTemperatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setNightAvgTemperatureDiff(BigDecimal nightAvgTemperatureDiff) {
        this.nightAvgTemperatureDiff = nightAvgTemperatureDiff;
    }

    public String getRainPeriod() {
        return rainPeriod;
    }

    public void setRainPeriod(String rainPeriod) {
        this.rainPeriod = rainPeriod;
    }

    public BigDecimal getAvgHumidityFeatureDiff() {
        return avgHumidityFeatureDiff == null ? avgHumidityFeatureDiff : avgHumidityFeatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setAvgHumidityFeatureDiff(BigDecimal avgHumidityFeatureDiff) {
        this.avgHumidityFeatureDiff = avgHumidityFeatureDiff;
    }

    public BigDecimal getAvgEffectiveTemperatureDiff() {
        return avgEffectiveTemperatureDiff == null ? avgEffectiveTemperatureDiff : avgEffectiveTemperatureDiff.setScale(2, BigDecimalFunctions.ROUNDING_MODE);
    }

    public void setAvgEffectiveTemperatureDiff(BigDecimal avgEffectiveTemperatureDiff) {
        this.avgEffectiveTemperatureDiff = avgEffectiveTemperatureDiff;
    }

    public String getTemperatureConclusion() {
        return temperatureConclusion;
    }

    public void setTemperatureConclusion(String temperatureConclusion) {
        this.temperatureConclusion = temperatureConclusion;
    }

    public String getMaxLoadForecast() {
        return maxLoadForecast;
    }

    public void setMaxLoadForecast(String maxLoadForecast) {
        this.maxLoadForecast = maxLoadForecast;
    }

    public String getChangeMaxPeriod() {
        return changeMaxPeriod;
    }

    public void setChangeMaxPeriod(String changeMaxPeriod) {
        this.changeMaxPeriod = changeMaxPeriod;
    }

    public String getSocialLoadForecast() {
        return socialLoadForecast;
    }

    public void setSocialLoadForecast(String socialLoadForecast) {
        this.socialLoadForecast = socialLoadForecast;
    }

    public String getDateTypeChangeConclusion() {
        return dateTypeChangeConclusion;
    }

    public void setDateTypeChangeConclusion(String dateTypeChangeConclusion) {
        this.dateTypeChangeConclusion = dateTypeChangeConclusion;
    }

    public String getLoadChangeConclusion() {
        return loadChangeConclusion;
    }

    public void setLoadChangeConclusion(String loadChangeConclusion) {
        this.loadChangeConclusion = loadChangeConclusion;
    }
}
