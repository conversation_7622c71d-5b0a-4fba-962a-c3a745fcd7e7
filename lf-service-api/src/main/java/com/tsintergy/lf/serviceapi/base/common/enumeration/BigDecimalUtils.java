/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/1/9 14:28 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.common.enumeration;

import java.util.ArrayList;
import java.util.Arrays;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/1/9
 * @since 1.0.0
 */
public class BigDecimalUtils {

    public static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;
    //    public static final int SCALE = 6;
    public static final int SCALE = 4;
    public static final MathContext MATH_CONTEXT = new MathContext(SCALE, ROUNDING_MODE);

    public static BigDecimal effTem(BigDecimal tem, BigDecimal wind, BigDecimal humidity) {
        if (tem == null) {
            return null;
        } else {
            if (wind == null || humidity == null) {
                return tem;
            }
        }
        BigDecimal divide = BigDecimal.valueOf(17.27).multiply(tem).divide((BigDecimal.valueOf(237.27).add(tem)),9,
                BigDecimal.ROUND_HALF_UP);
        double exp = Math.exp(divide.doubleValue());
        BigDecimal e = humidity.divide(BigDecimal.valueOf(100)).multiply(BigDecimal.valueOf(6.105)).multiply(BigDecimal.valueOf(exp));
        BigDecimal effTem = BigDecimal.valueOf(1.07).multiply(tem).add(BigDecimal.valueOf(0.2).multiply(e)).subtract(
                BigDecimal.valueOf(0.065).multiply(wind)).subtract(BigDecimal.valueOf(2.7)).setScale(2, BigDecimal.ROUND_HALF_UP);
        return effTem;
    }

    public static boolean equalsBigDecimal(BigDecimal num1, BigDecimal num2) {
        if (num1 == null) {
            if (num2 == null) {
                return true;
            } else {
                return false;
            }
        } else {
            boolean equals = num1.setScale(4, BigDecimal.ROUND_HALF_DOWN)
                .equals(num2.setScale(4, BigDecimal.ROUND_HALF_DOWN));
            return equals;
        }

    }

    /**
     * 平均值, 如果列表所有元素为null，则返回null
     *
     * @param values
     * @return
     */
    public static BigDecimal listAvg(List<BigDecimal> values) {
        //除不尽会抛出异常，需设置四舍五入
        return listSum(values).divide(new BigDecimal(values.size()), MATH_CONTEXT);
    }

    /**
     * 列表求和
     *
     * @param values
     * @return
     */
    public static BigDecimal listSum(List<BigDecimal> values) {
        return listStatistics(values, (v1, v2) -> valueCalc(v1, v2, BigDecimal::add)).orElse(new BigDecimal("0"));
    }

    /**
     * @param values
     * @param function 统计函数
     * @param <T>
     * @return
     */
    public static <T> Optional<T> listStatistics(List<T> values, BiFunction<T, T, T> function) {
        if (CollectionUtils.isEmpty(values)) {
            return Optional.empty();
        } else {
            return values.stream().filter(Objects::nonNull).reduce(function::apply);
        }
    }

    /**
     * v1 operator v2, operator: + - * / 等。如果v1 == null，返回v2，如果v2 == null，返回v1，否则 v1 operator v2
     *
     * @param v1
     * @param v2
     * @param operator
     * @return
     */
    public static BigDecimal valueCalc(BigDecimal v1, BigDecimal v2, BiFunction<BigDecimal, BigDecimal, BigDecimal> operator) {
        if (v1 == null) {
            return v2;
        } else if (v2 == null) {
            return v1;
        } else {
            return operator.apply(v1, v2);
        }
    }

    public static List<String> getTimePoint(Integer num) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 1445; i += 15) {
            int hour = i / 60;
            int min = i % 60;
            String format = String.format("%04d", hour * 100 + min);
            list.add(format.substring(0, 2) + ":" + format.substring(2, 4));
        }
        if (num == null) {
            return list;
        } else {
            return Arrays.asList(list.get(num));
        }
    }
}
