package com.tsintergy.lf.serviceapi.large_model.api;

import com.tsintergy.lf.serviceapi.large_model.pojo.LmStatisticsCityDayFcDO;

import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 11:08
 **/
public interface LmStatisticsCityDayFcService {

    void saveOrUpdateBatch(List<LmStatisticsCityDayFcDO> statisticsCityDayFcDOList);

    List<LmStatisticsCityDayFcDO> find(String cityId, String caliberId, Date startDate,Date endDate, String algorithmId);
}
