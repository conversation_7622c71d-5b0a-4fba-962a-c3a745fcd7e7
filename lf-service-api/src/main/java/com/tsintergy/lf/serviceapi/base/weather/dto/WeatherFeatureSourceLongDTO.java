package com.tsintergy.lf.serviceapi.base.weather.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WeatherFeatureSourceLongDTO implements DTO {

    private String cityName;

    private String dateStr;

    private String sourceName;

    private BigDecimal highTemp;

    private BigDecimal lowTemp;

    private BigDecimal avgTemp;

    private BigDecimal maxHumidity;

    private BigDecimal minHumidity;

    private BigDecimal avgHumidity;

    private BigDecimal rainfall;

    private BigDecimal maxWindSpeed;

    private BigDecimal minWindSpeed;

    private BigDecimal avgWindSpeed;

    private BigDecimal avgRadiance;

}
