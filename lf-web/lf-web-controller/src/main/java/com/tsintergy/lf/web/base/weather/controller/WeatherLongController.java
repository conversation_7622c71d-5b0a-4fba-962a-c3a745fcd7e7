/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.weather.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.algorithm.api.LongForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayLongFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.web.base.controller.BaseController;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/19 10:19
 * @Version: 1.0.0
 */
@RestController
@RequestMapping(value = "/mediumAndLong")
public class WeatherLongController extends BaseController {

    @Autowired
    WeatherFeatureCityDayLongFcService weatherFeatureCityDayLongFcService;

    @Autowired
    LongForecastAlgorithmService longForecastAlgorithmService;

    @Autowired
    ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @GetMapping("/fcData")
    public BaseResp fcData(String cityId, Date startDate, Date endDate) throws Exception {
        WeatherFeatureLongDTO monthFcPageByParam = weatherFeatureCityDayLongFcService
            .findMonthFcPageByParam(cityId, startDate, endDate, this.getCaliberId());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(monthFcPageByParam);
        return baseResp;
    }

    @PostMapping("/fcData")
    public BaseResp longFeature(@RequestBody WeatherFeatureCityDayLongListVO weatherFeatureCityDayLongListVO) {
        try {
            weatherFeatureCityDayLongFcService.saveOrUpdate(weatherFeatureCityDayLongListVO);
        } catch (Exception e) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetMsg(e.getMessage());
            return baseResp;
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @PostMapping("/manualPrediction")
    public BaseResp manualPrediction(@RequestBody WeatherFeatureCommonLongDTO weatherFeatureCommonLongDTO,
        HttpServletRequest httpServletRequest) {
        try {
            httpServletRequest.getSession().setAttribute("longForecast", 0);
            HttpSession session = httpServletRequest.getSession();
            String caliberId = this.getCaliberId();
            scheduledThreadPoolExecutor.submit(((Runnable) () -> {
                try {
                    longForecastAlgorithmService
                        .statsLongForecast(caliberId, weatherFeatureCommonLongDTO.getCityId(),
                            weatherFeatureCommonLongDTO.getStartDate(), weatherFeatureCommonLongDTO.getEndDate());
                    session.setAttribute("longForecast", 1);
                } catch (Exception e) {
                    session.setAttribute("longForecast", 2);
                }
            }));
        } catch (Exception e) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetMsg(e.getMessage());
            return baseResp;
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @PostMapping("/polling")
    public BaseResp polling(HttpServletRequest httpServletRequest) {
        HttpSession session = httpServletRequest.getSession();
        Integer longForecast = (Integer) session.getAttribute("longForecast");
        if (longForecast.equals(1)) {
            BaseResp baseResp = BaseResp.succResp();
            baseResp.setRetMsg("手动预测完成，正在查询最新结果。");
            return baseResp;
        } else if (longForecast.equals(2)) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetCode("T888");
            baseResp.setRetMsg("手动预测失败！请检查算法运行条件。");
            return baseResp;
        } else {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetMsg("算法未调用完成");
            return baseResp;
        }
    }

    @ApiOperation("获取中长期预报气象数据")
    @RequestMapping(value = "/source/fc/weather", method = RequestMethod.GET)
    public BaseResp<List<WeatherFeatureSourceLongDTO>> getWeatherFcSource(String cityId, Integer type, String startDate,
                                                                          String endDate, String sourceType) throws Exception {
        BaseResp baseResp = BaseResp.succResp("查询成功");
        List<WeatherFeatureSourceLongDTO> sourceByParam = weatherFeatureCityDayLongFcService.findSourceByParam(cityId,
                type, startDate, endDate, sourceType);
        baseResp.setData(sourceByParam);
        return baseResp;
    }

    @ApiOperation("获取中长期预报气象特性数据")
    @RequestMapping(value = "/source/feature/weather", method = RequestMethod.GET)
    public BaseResp<WeatherFeatureSourceDTO> getWeatherFcFeature(String cityId, Integer type, String startDate,
                                                                 String endDate, String sourceType) throws Exception {
        BaseResp baseResp = BaseResp.succResp("查询成功");
        WeatherFeatureSourceDTO sourceFeatureByParam = weatherFeatureCityDayLongFcService.findSourceFeatureByParam(cityId,
                type, startDate, endDate, sourceType);
        baseResp.setData(sourceFeatureByParam);
        return baseResp;
    }

    /*@RequestMapping(value = "/test", method = RequestMethod.GET)
    public BaseResp<Void> getTest(Date startDate,  Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp("查询成功");
        String startMonth = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").substring(0, 7);
        String endMonth = DateUtil.getDateToStrFORMAT(endDate, "yyyy-MM-dd").substring(0, 7);
        for (String ymn : DateUtil.getTargetTenDaysList(startMonth, endMonth)) {
            List<Date> targetTenDaysDateList = DateUtil.getTargetTenDaysDateList(ymn.substring(0, 7), ymn.substring(8, 10));
            startDate = targetTenDaysDateList.get(0);
            endDate = targetTenDaysDateList.get(1);
            for (WeatherSourceEnum value : WeatherSourceEnum.values()) {
                if (value.getCode().equals(WeatherSourceEnum.HIS.getCode())) {
                    continue;
                }
                weatherFeatureCityDayLongFcService.TenDaysFcLongFeature(startDate, endDate, ymn.substring(0, 7), ymn.substring(8, 10), value.getCode());
            }
        }

        String start = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").substring(0, 7);
        String end = DateUtil.getDateToStrFORMAT(endDate, "yyyy-MM-dd").substring(0, 7);
        List<String> targetYearList = DateUtil.getTargetYearList(start, end);
        for (String ym : targetYearList) {
            for (WeatherSourceEnum value : WeatherSourceEnum.values()) {
                if (value.getCode().equals(WeatherSourceEnum.HIS.getCode())) {
                    continue;
                }
                Date firstDayDateOfMonth = DateUtil.getFirstDayDateOfMonth(DateUtil.getDate(ym + "-01", "yyyy-MM-dd"));
                Date lastDayOfMonth = DateUtil.getLastDayOfMonth(DateUtil.getDate(ym + "-01", "yyyy-MM-dd"));
                weatherFeatureCityDayLongFcService.MonthFcLongFeature(firstDayDateOfMonth, lastDayOfMonth, ym, value.getCode());
            }
        }
        return baseResp;
    }*/
}