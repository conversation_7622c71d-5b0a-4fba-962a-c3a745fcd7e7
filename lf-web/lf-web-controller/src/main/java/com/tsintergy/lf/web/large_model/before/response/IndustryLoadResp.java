/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/17 18:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.large_model.before.response;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @description: 产业负荷
 * <AUTHOR>
 * @date 2025/07/05 11:36
 * @version: 1.0
 */
@ApiModel(description = "产业负荷")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndustryLoadResp implements DTO {


    @ApiModelProperty(value = "产业类型")
    private String type;

    @ApiModelProperty(value = "产业负荷")
    private List<BigDecimal> loadList;

    @ApiModelProperty(value = "负荷")
    private BigDecimal load;

}
