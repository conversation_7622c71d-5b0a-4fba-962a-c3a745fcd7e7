package com.tsintergy.lf.web.base.weather.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *  Description:  <br>     <AUTHOR>  @create 2021/7/27  @since 1.0.0 
 */

@Data
public class WeatherSourceFcRequest implements Serializable {

    @ApiModelProperty(value = "城市列表")
    private List<String> cityId;

    @ApiModelProperty(value = "来源列表")
    private List<String> source;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "小时")
    private String hour;

    @ApiModelProperty(value = "气象类型")
    private Integer weatherType;

    @ApiModelProperty(value = "气象特性")
    private Integer weatherFeature;

}
