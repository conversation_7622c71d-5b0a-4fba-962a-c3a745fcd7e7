package com.tsintergy.lf.web.large_model.before.controller;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.tool.core.utils.date.DateUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryDayHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryDayHisServiceDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.trade.api.IndustryCityLoadDayHisClctService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.IndustryCityLoadDayHisClctDO;
import com.tsintergy.lf.serviceapi.large_model.api.LmLargeModelTextSettingService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLargeModelTextSettingDO;
import com.tsintergy.lf.web.base.load.controller.BaseLoadFeatureController;
import com.tsintergy.lf.web.large_model.before.response.IndustryLoadContrastPackageResp;
import com.tsintergy.lf.web.large_model.before.response.IndustryLoadContrastResp;
import com.tsintergy.lf.web.large_model.before.response.IndustryLoadResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: 【事前】-产业负荷
 * @date 2025/07/05 11:14
 * @version: 1.0
 */
@RequestMapping("/before/day/industry")
@RestController
@Api(tags = "日度事前-产业负荷控制器")
public class BeforeDayTradeController extends BaseLoadFeatureController {

    private Logger logger = LogManager.getLogger(BeforeDayTradeController.class);


    @Autowired
    CityService cityService;
    @Autowired
    LmLargeModelTextSettingService lmLargeModelTextSettingService;
    @Autowired
    IndustryCityLoadDayHisClctService industryCityLoadDayHisClctService;
    @Autowired
    LoadCityHisService loadCityHisService;
    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;
    @Autowired
    LoadFeatureIndustryDayHisService loadFeatureIndustryDayHisService;

    public static final List<String> INDUSTRY_IDS = Arrays.asList("99A0", "99B0", "99C0", "9900");

    public static final List<String> THREE_INDUSTRY_IDS = Arrays.asList("99B0", "99C0", "9900");

    /**
     * what: 产业负荷
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("产业负荷和占比")
    @RequestMapping(value = "/loadList", method = RequestMethod.GET)
    public BaseResp<List<IndustryLoadResp>> loadList() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        Date date = DateUtils.addDays(systemDate, -1);
        List<IndustryLoadResp> result = new ArrayList<>();
        List<IndustryCityLoadDayHisClctDO> industryCityLoadDayHisClctDOS = industryCityLoadDayHisClctService
                .findIndustryCityLoadDayHisClctDOS(cityId, INDUSTRY_IDS, date, date);
        Map<String, List<BigDecimal>> indusrtyLoadMap = industryCityLoadDayHisClctDOS.stream().collect(Collectors.toMap(x -> x.getTradeCode(),
                x -> x.getloadList(), (k1, k2) -> k1));
        List<BigDecimal> loadCityList = loadCityHisService.findLoadCityHisDO(date, cityId, caliberId);
        int index = loadCityList.indexOf(BigDecimalFunctions.listMax(loadCityList));
        indusrtyLoadMap.keySet().forEach(key -> {
            IndustryLoadResp industryLoadResp = new IndustryLoadResp();
            industryLoadResp.setType(key);
            List<BigDecimal> loadList = BigDecimalFunctions.listMultiplyValue(indusrtyLoadMap.get(key), BigDecimal.valueOf(10));
            industryLoadResp.setLoadList(loadList);
            BigDecimal load = loadList.get(index);
            industryLoadResp.setLoad(load);
            result.add(industryLoadResp);
        });
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("负荷对照和产业结构")
    @RequestMapping(value = "/loadContrast", method = RequestMethod.GET)
    public BaseResp<IndustryLoadContrastPackageResp> loadContrast() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        String tradeId = Constants.TOTAL_SOCIAL_TRADE_CODE;
        Date date = DateUtils.addDays(systemDate, -1);
        Date startDate = DateUtils.addDays(systemDate, -15);
        List<IndustryLoadContrastResp> dataList = new ArrayList<>();
        List<LoadFeatureIndustryDayHisServiceDO> industryDayHisServiceDOS = loadFeatureIndustryDayHisService
                .getLoadFeatureIndustryDayHisServiceList(startDate, date, cityId, tradeId);
        Map<String, BigDecimal> indusrtyLoadMap = industryDayHisServiceDOS.stream()
                .collect(Collectors.toMap(x -> DateUtil.date2String(x.getDate(), DateUtil.DATE_FORMAT2),
                        x -> x.getMaxLoad(), (k1, k2) -> k1));

        List<LoadFeatureCityDayHisDO> loadCityList = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(cityId, startDate, date, caliberId);
        Map<String, BigDecimal> cityLoadMap = loadCityList.stream()
                .collect(Collectors.toMap(x -> DateUtil.date2String(
                                x.getDate(), DateUtil.DATE_FORMAT2),
                        x -> x.getMaxLoad(), (k1, k2) -> k1));
        Set<String> dateStrSet = cityLoadMap.keySet();
        dateStrSet.retainAll(indusrtyLoadMap.keySet());
        List<BigDecimal> rateList = new ArrayList<>();
        List<BigDecimal> deviationList = new ArrayList<>();
        dateStrSet.forEach(key -> {
            IndustryLoadContrastResp dto = new IndustryLoadContrastResp();
            dto.setDate(key);
            BigDecimal cityMaxLoad = cityLoadMap.get(key);
            BigDecimal indusrtyMaxLoad = BigDecimalFunctions.multiply(indusrtyLoadMap.get(key), BigDecimal.valueOf(10));
            BigDecimal subtract = BigDecimalFunctions.subtract(cityMaxLoad, indusrtyMaxLoad);
            dto.setCityLoad(cityMaxLoad);
            dto.setIndustryLoad(indusrtyMaxLoad);
            dto.setDeviationLoad(subtract);
            BigDecimal rate = BigDecimalFunctions.divide(indusrtyMaxLoad, cityMaxLoad).setScale(4, BigDecimal.ROUND_HALF_UP);
            dto.setRate(BigDecimalFunctions.multiply(rate, BigDecimal.valueOf(100)));
            deviationList.add(subtract);
            rateList.add(rate);
            dataList.add(dto);
        });
        BaseResp baseResp = BaseResp.succResp();
        IndustryLoadContrastPackageResp result = new IndustryLoadContrastPackageResp();
        result.setDataList(dataList);
        result.setDeviationLoad(BigDecimalFunctions.listAvg(deviationList));
        result.setRate(BigDecimalFunctions.listAvg(rateList));
        result.setSimilarity(BigDecimal.valueOf(95.6));
        baseResp.setData(result);
        return baseResp;
    }

    /**
     * what: 调度预测AI大模型
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("调度预测AI大模型-三、产业数据")
    @GetMapping(value = "/industryText")
    public BaseResp<String> industryText() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        Date date = DateUtils.addDays(systemDate, -1);
        Date startDate = DateUtils.addDays(systemDate, -4);
        Date last15StartDate = DateUtils.addDays(systemDate, -15);
        List<LoadFeatureIndustryDayHisServiceDO> industryDayHisServiceDOS = loadFeatureIndustryDayHisService
                .getLoadFeatureIndustryListByCodeList(startDate, date, cityId, THREE_INDUSTRY_IDS);
        Map<String, List<LoadFeatureIndustryDayHisServiceDO>> industryMapByType = industryDayHisServiceDOS.stream().collect(Collectors.groupingBy(x -> x.getType()));
        List<Date> dateList = DateUtil.getListBetweenDay(DateUtils.addDays(systemDate, -3), date);
        List<BigDecimal> oneRateList = new ArrayList<>();
        List<BigDecimal> twoRateList = new ArrayList<>();
        List<BigDecimal> threeRateList = new ArrayList<>();
        for (String type : THREE_INDUSTRY_IDS) {
            List<LoadFeatureIndustryDayHisServiceDO> oneIndustryList = industryMapByType.get(type);
            Map<Date, BigDecimal> oneIndustryMap = oneIndustryList.stream().collect(Collectors.toMap(x -> x.getDate(), x -> x.getMaxLoad()));
            for (Date dateTime : dateList) {
                BigDecimal maxLoad = oneIndustryMap.get(dateTime);
                BigDecimal lastMaxLoad = oneIndustryMap.get(DateUtil.addDays(dateTime, -1));
                BigDecimal yoy = this.calcBasicsNumber(maxLoad, lastMaxLoad);
                switch (type) {
                    case "99B0":
                        oneRateList.add(yoy);
                        break;
                    case "99C0":
                        twoRateList.add(yoy);
                        break;
                    case "9900":
                        threeRateList.add(yoy);
                        break;
                }
            }
        }

        BigDecimal oneAvgRate = BigDecimalFunctions.listAvg(oneRateList);
        BigDecimal twoAvgRate = BigDecimalFunctions.listAvg(twoRateList);
        BigDecimal threeAvgRate = BigDecimalFunctions.listAvg(threeRateList);
        List<BigDecimal> rateList = Arrays.asList(oneAvgRate, twoAvgRate, threeAvgRate);
        long count = rateList.stream().filter(x -> x.compareTo(BigDecimal.valueOf(5)) >= 0).count();
        String a = "近期各产业负荷用电相对稳定，企业生产及居民生活用电需求较为平稳，建议考虑该现象进行预测";
        if (count > 0) {
            String maxWithSource = this.findMaxWithSource(oneAvgRate, twoAvgRate, threeAvgRate);
            switch (maxWithSource) {
                case "99B0":
                    if (oneAvgRate.compareTo(BigDecimal.ZERO) > 0) {
                        a = "第二产业负荷近期增长较为显著，工业市场可能存在一定变化，企业生产热情相对旺盛，工业生产在夜间较为集中，建议重点考虑夜间负荷变化";
                    }else {
                        a = "第二产业负荷近期下降较为显著，工业市场可能存在一定变化，企业生产热情相对萎靡，工业生产在夜间较为集中，建议重点考虑夜间负荷变化";
                    }
                    break;
                case "99C0":
                    if (twoAvgRate.compareTo(BigDecimal.ZERO) > 0) {
                        a = "第三产业负荷近期增长较为显著，反应湖北地区综合市场发展较快，第三产业用电在午后及保供时段较为集中，建议重点考虑对应时段负荷变化";
                    }else {
                        a = "第三产业负荷近期下降较为显著，反应湖北地区综合市场发展放缓，第三产业用电在午后及保供时段较为集中，建议重点考虑对应时段负荷变化";
                    }
                    break;
                case "9900":
                    if (threeAvgRate.compareTo(BigDecimal.ZERO) > 0) {
                        a = "居民负荷近期增长较为显著，反应湖北地区居民用电需求旺盛，可能由于气候变化因素导致空调负荷出现波动，建议重点结合温度变化进行预测";
                    }else {
                        a = "居民负荷近期下降较为显著，反应湖北地区居民用电需求旺盛收缩，可能由于气候变化因素导致空调负荷出现波动，建议重点结合温度变化进行预测";
                    }
                    break;
            }
        }


        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOList = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(cityId, last15StartDate, date, caliberId);
        Map<String, BigDecimal> cityLoadMap = loadFeatureCityDayHisDOList.stream()
                .collect(Collectors.toMap(x -> DateUtil.date2String(x.getDate(), DateUtil.DATE_FORMAT2), x -> x.getMaxLoad()));
        List<LoadFeatureIndustryDayHisServiceDO> industryDayHisServiceDOList = loadFeatureIndustryDayHisService
                .getLoadFeatureIndustryDayHisServiceList(startDate, date, cityId, Constants.TOTAL_SOCIAL_TRADE_CODE);
        Map<String, BigDecimal> industryLoadMap = industryDayHisServiceDOList.stream()
                .collect(Collectors.toMap(x -> DateUtil.date2String(x.getDate(), DateUtil.DATE_FORMAT2), x -> x.getMaxLoad()));

        Set<String> dateStrSet = cityLoadMap.keySet();
        dateStrSet.retainAll(industryLoadMap.keySet());
        List<BigDecimal> subtractList = new ArrayList<>();
        List<BigDecimal> rates = new ArrayList<>();
        dateStrSet.forEach(key -> {
            BigDecimal cityMaxLoad = cityLoadMap.get(key);
            BigDecimal indusrtyMaxLoad = BigDecimalFunctions.multiply(industryLoadMap.get(key), BigDecimal.valueOf(10));
            BigDecimal subtract = BigDecimalFunctions.subtract(cityMaxLoad, indusrtyMaxLoad);
            BigDecimal rate = BigDecimalFunctions.divide(indusrtyMaxLoad, cityMaxLoad).setScale(4, BigDecimal.ROUND_HALF_UP);
            subtractList.add(subtract);
            rates.add(BigDecimalFunctions.multiply(rate,BigDecimal.valueOf(100)));
        });

        BigDecimal b = BigDecimalFunctions.listAvg(rates);
        BigDecimal c = BigDecimalFunctions.listMin(subtractList);
        BigDecimal d = BigDecimalFunctions.listMax(subtractList);
        String e = "近期占比相对平稳";
        List<Date> threeDateList = DateUtil.getListBetweenDay(DateUtils.addDays(systemDate, -3), DateUtils.addDays(systemDate, -1));
        List<BigDecimal> threeRates = new ArrayList<>();
        threeDateList.forEach(key -> {
            String dated2String = DateUtil.date2String(key, DateUtil.DATE_FORMAT2);
            BigDecimal cityMaxLoad = cityLoadMap.get(dated2String);
            BigDecimal indusrtyMaxLoad = BigDecimalFunctions.multiply(industryLoadMap.get(dated2String), BigDecimal.valueOf(10));
            BigDecimal rate = BigDecimalFunctions.divide(indusrtyMaxLoad, cityMaxLoad).setScale(4, BigDecimal.ROUND_HALF_UP);
            threeRates.add(rate);
        });
        e = this.analyzeTrend(threeRates);
        LmLargeModelTextSettingDO beforeIndustry = lmLargeModelTextSettingService.getByField("before_industry");
        //要替换的变量
        Map<String, Object> params = new HashMap<>();
        params.put("a",a);
        params.put("b",b);
        params.put("c",c);
        params.put("d",d);
        params.put("e",e);
        String result = StringSubstitutor.replace(beforeIndustry.getValue(), params);
        result = result.replace("\\n", "\n")    // 处理字面量 \n → 真实换行
                .replace("\\\\n", "\n");
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    public  String analyzeTrend(List<BigDecimal> lastThreeDays) {
        // 检查数据是否有效
        if (lastThreeDays == null || lastThreeDays.size() < 3) {
            throw new IllegalArgumentException("需要至少三天的数据");
        }

        BigDecimal day1 = lastThreeDays.get(0);
        BigDecimal day2 = lastThreeDays.get(1);
        BigDecimal day3 = lastThreeDays.get(2);

        // 检查是否为0（避免除0错误）
        boolean hasZero = day1.compareTo(BigDecimal.ZERO) == 0 ||
                day2.compareTo(BigDecimal.ZERO) == 0;

        // 1. 判断连续上升
        if (day2.compareTo(day1) > 0 && day3.compareTo(day2) > 0) {
            return "近期占比逐渐上升";
        }

        // 2. 判断连续下降
        if (day2.compareTo(day1) < 0 && day3.compareTo(day2) < 0) {
            return "近期占比呈下降趋势";
        }

        // 3. 判断平稳（平均变化率 < 5%）
        if (!hasZero) {
            // 计算每日变化率
            BigDecimal changeRate1 = day2.subtract(day1)
                    .divide(day1, 4, RoundingMode.HALF_UP)
                    .abs(); // 取绝对值
            BigDecimal changeRate2 = day3.subtract(day2)
                    .divide(day2, 4, RoundingMode.HALF_UP)
                    .abs();

            // 计算平均变化率
            BigDecimal avgChangeRate = changeRate1.add(changeRate2)
                    .divide(BigDecimal.valueOf(2), 4, RoundingMode.HALF_UP);

            // 判断是否小于5%
            if (avgChangeRate.compareTo(BigDecimal.valueOf(0.05)) < 0) {
                return "近期占比相对平稳";
            }
        }

        // 默认返回波动较大（但根据需求可不处理，这里返回平稳的补充说明）
        return "近期占比波动较大"; // 或根据需求返回其他默认值
    }

    public  String findMaxWithSource(BigDecimal num1, BigDecimal num2, BigDecimal num3) {
        // 检查空值
        if (num1 == null || num2 == null || num3 == null) {
            throw new IllegalArgumentException("参数不能为null");
        }

        BigDecimal max = num1;
        String source = "99B0";

        if (num2.compareTo(max) > 0) {
            max = num2;
            source = "99C0";
        }

        if (num3.compareTo(max) > 0) {
            max = num3;
            source = "9900";
        }

        return source;
    }

    /**
     * 环比 =（对比日数值-参考日数值）/对比日数值X100%（亦适用于同比）
     *
     * @param comparison 对比日
     * @param reference  参考日
     * @return
     * @throws Exception
     */
    private BigDecimal calcBasicsNumber(BigDecimal comparison, BigDecimal reference) throws Exception {
        BigDecimal number = null;
        if (comparison != null && reference != null) {
            BigDecimal sub = LoadCalUtil.sub(comparison, reference);
            number = LoadCalUtil.divide(sub, comparison, 4);
        }
        return BigDecimalFunctions.multiply(number,BigDecimal.valueOf(100));
    }

}
