package com.tsintergy.lf.web.base.common.controller;


import com.alibaba.fastjson.JSONObject;
import com.tsieframework.core.base.enums.TsieEnum;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.*;
import com.tsintergy.lf.core.enums.WeatherSourceAlgorithmEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.BaseDataService;
import com.tsintergy.lf.serviceapi.base.base.api.BaseIndustryInfoService;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.dto.BaseIndustryDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.CaliberDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.CityDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.PeakTimeDTO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseIndustryInfoDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ManualAlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.web.base.check.response.CommonResp;
import com.tsintergy.lf.web.base.common.response.PeriodsResp;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

/**
 * 通用模块 User:taojingui Date:18-2-8 Time:下午8:10
 */
@Api(tags = "通用模块")
@RequestMapping("/common")
@RestController
public class CommonController extends CommonBaseController {

    private final Logger logger = LogManager.getLogger(CommonController.class);

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private CaliberService caliberService;


    @Autowired
    private UserService userService;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    BaseIndustryInfoService baseIndustryInfoService;

    /**
     * 获取系统时间
     */
    @ApiOperation("获取系统时间")
    @GetMapping("/system-time")
    public BaseResp<Map<String, Object>> getSystemTime() {
        Date date = this.getSystemDate();
        if (date == null) {
            date = new Date();
        }

        Calendar sysCal = Calendar.getInstance();
        sysCal.setTime(date);

        Calendar nowCal = Calendar.getInstance();
        sysCal.set(Calendar.HOUR_OF_DAY, nowCal.get(Calendar.HOUR_OF_DAY));
        sysCal.set(Calendar.MINUTE, nowCal.get(Calendar.MINUTE));
        sysCal.set(Calendar.SECOND, nowCal.get(Calendar.SECOND));

        BaseResp resp = BaseResp.succResp();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("time", sysCal.getTimeInMillis());
        resp.setData(map);
        return resp;
    }

    /**
     * 设置系统日期
     */
    @ApiOperation("设置系统日期")
    @PostMapping("/system-time/set")
    public BaseResp setSystemDate(@ApiParam(value = "时间")String time) {
        try {
            Date date = DateUtils.string2Date(time, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            request.getSession().setAttribute(CacheConstants.SYSTEM_DATE, date);
            return BaseResp.succResp();
        } catch (Exception e) {
            return BaseResp.failResp("时间格式不对");
        }
    }

    /**
     * 获取算法列表
     */
    @ApiOperation("超短期算法列表")
    @GetMapping("/short/algorithm")
    public BaseResp<CommonResp<AlgorithmDTO>> getShortAlgorithm(@ApiParam(value = "城市id")String cityId) {
        try {
            // 获取算法列表
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                    t -> AlgorithmConstants.SHORT_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());

            List<AlgorithmDO> viewAlgorithms = null;
            if(cityId == null){
                cityId = this.getLoginCityId();
            }
            CityDO cityDO = cityService.findCityById(cityId);
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                } else if (CityConstants.CITY_TYPE.equals(cityDO.getType())) {
                    viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true)
                        .filter(t -> !"201".equals(t.getCode())).collect(Collectors.toList());
                }
            }

            List<AlgorithmDTO> dtos = new ArrayList<>();
            for (AlgorithmDO algorithmVO : viewAlgorithms) {
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(algorithmVO.getId());
                dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                dtos.add(dto);
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }









    /**
     * 获取多源气象算法列表
     */
    @ApiOperation("多气象源展示算法")
    @GetMapping("/source/algorithm")
    public BaseResp<List<AlgorithmDTO>> getSourceAlgorithm() {
        List<AlgorithmDTO> result = new ArrayList<>();
        BaseResp baseResp = BaseResp.succResp();
        try {
            for (WeatherSourceAlgorithmEnum value : WeatherSourceAlgorithmEnum.values()) {
                AlgorithmDTO algorithmDTO = new AlgorithmDTO();
                algorithmDTO.setAlgorithm(value.getDescription());
                algorithmDTO.setId(value.getCode());
                if (AlgorithmEnum.FORECAST_INNOVATION.getId().equals(value.getCode())) {
                    algorithmDTO.setDefault(true);
                } else {
                    algorithmDTO.setDefault(false);
                }
                result.add(algorithmDTO);
            }
            baseResp.setData(result);
            return baseResp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取算法列表
     */
    @ApiOperation("获取算法列表")
    @GetMapping("/algorithm")
    public BaseResp<CommonResp<AlgorithmDTO>> getAlgorithm(@ApiParam(value = "城市id")String cityId) {
        try {
            // 获取算法列表
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                    .equals(t.getType()) || AlgorithmConstants.HOLIDAY_ALGORITHM_TYPE.equals(t.getType())
                    || AlgorithmConstants.SPECIAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());


            // 获取自动预测算法
            String defaultAlgorithm = null;
            SystemData systemSetting = settingSystemService.getSystemSetting();
            List<AlgorithmDO> viewAlgorithms = null;
            if(cityId == null){
                cityId = this.getLoginCityId();
            }
            CityDO cityDO = cityService.findCityById(cityId);
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    defaultAlgorithm = systemSetting.getProvinceNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                } else if (CityConstants.CITY_TYPE.equals(cityDO.getType())) {
                    defaultAlgorithm = systemSetting.getCityNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true)
                        .filter(t -> !"201".equals(t.getCode())).collect(Collectors.toList());
                }
            }

            viewAlgorithms.addAll(algorithmService.getAdditionalAlgorithmsToDisplay(cityId));
            List<AlgorithmDTO> dtos = new ArrayList<>();
            for (AlgorithmDO algorithmVO : viewAlgorithms) {
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(algorithmVO.getId());
                dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                if ("分区预测".equals(algorithmVO.getAlgorithmCn())) {
                    dto.setAlgorithm("子网累加");
                }
                if (defaultAlgorithm.equals(algorithmVO.getId())) {
                    dto.setDefault(true);
                }
                dtos.add(dto);
            }
            // 地市新增大用户手动计算算法预测
            if (Constants.USER_CITY_ID.contains(cityId)) {
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(Constants.USER_ALGORITHM_ID);
                dto.setAlgorithm(Constants.USER_ALGORITHM_NAME);
                dtos.add(dto);
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            if (!CollectionUtils.isEmpty(dtos)) {
                List<AlgorithmDTO> collect = dtos.stream().distinct().collect(Collectors.toList());
                data.setDataList(collect);
            }
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }


    @ApiOperation("上报算法")
    @GetMapping("/algorithm-report")
    public BaseResp<CommonResp<AlgorithmDTO>> getAlgorithmReportV2(@ApiParam(value = "时间")Date date,@ApiParam(value = "口径id") String caliberId, @ApiParam(value = "城市")String cityId) throws Exception{
        if(date == null){
            date = this.getSystemDate();
        }

        if(StringUtils.isEmpty(caliberId)){
            caliberId = this.getCaliberId();
        }

        if(StringUtils.isEmpty(cityId)){
            cityId = this.getLoginCityId();
        }


        List<AlgorithmDTO> dtos = new ArrayList<AlgorithmDTO>();
        CityDO cityDO = cityService.findCityById(cityId);
        String reportAlgorithmId = null;
        LoadCityFcDO fcVO = loadCityFcService.getReport(cityId, caliberId, date);

        if (fcVO != null) {
            reportAlgorithmId = AlgorithmConstants.MD_ALGORITHM_ID;
        } else {
            SystemData systemSetting = settingSystemService.getSystemSetting();
            if (cityId.equals(CityConstants.PROVINCE_ID)) {
                reportAlgorithmId = systemSetting.getProvinceNormalAlgorithm();
            } else {
                reportAlgorithmId = systemSetting.getCityNormalAlgorithm();
            }
        }


        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        if(!CollectionUtils.isEmpty(allAlgorithmsNotCache)){
            List<AlgorithmDO> pageAlgorithms = null;



            pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                    .equals(t.getType()))
                .collect(Collectors.toList());


            List<AlgorithmDO> viewAlgorithms = null;
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
                }
            }


            // flag 标识位，用来标识是否有算法设置位true
            Boolean flag = false;
            if(!CollectionUtils.isEmpty(viewAlgorithms)){
                for(AlgorithmDO algorithmDO:viewAlgorithms){
                    AlgorithmDTO dto = new AlgorithmDTO();
                    dto.setAlgorithm(algorithmDO.getAlgorithmCn());
                    dto.setId(algorithmDO.getId());
                    if(algorithmDO.getId().equals(reportAlgorithmId)){
                        dto.setDefault(true);
                        flag = true;
                    }
                    dtos.add(dto);
                }
            }


            if(!flag){
                for(AlgorithmDTO dto:dtos){
                    if(dto.getId().equals(reportAlgorithmId)){
                        dto.setDefault(true);
                        flag = true;
                        break;
                    }
                }
            }


            if(!flag){
                AlgorithmDTO dto = dtos.get(0);
                dto.setDefault(true);
                dtos.set(0,dto);
            }
        }
        BaseResp resp = BaseResp.succResp();
        CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
        data.setDataList(dtos);
        resp.setData(data);
        return resp;
    }


    @GetMapping("/manualPredictionAlgorithms")
    @ApiOperation(value = "正常日修正上报手动预测中算法下拉列表接口")
    public BaseResp<List<ManualAlgorithmDTO>> getAlgorithmListManualPrediction(@RequestParam(required = false) @ApiParam(value = "城市id")String cityId) throws Exception{

        if(StringUtils.isEmpty(cityId)){
            cityId = this.getLoginCityId();
        }
        CityDO cityDO = cityService.findCityById(cityId);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> viewAlgorithms = null;
        if(!CollectionUtils.isEmpty(allAlgorithmsNotCache)){
            List<AlgorithmDO> pageAlgorithms = null;
            pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());


            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
                }
            }
        }

        List<ManualAlgorithmDTO> dtos = new ArrayList<ManualAlgorithmDTO>();
        if(!CollectionUtils.isEmpty(viewAlgorithms)){
            for(AlgorithmDO algorithmDO:viewAlgorithms){
                ManualAlgorithmDTO dto = new ManualAlgorithmDTO();
                dto.setId(algorithmDO.getId());
                dto.setName(algorithmDO.getAlgorithmCn());
                dto.setCode(algorithmDO.getCode());
                dtos.add(dto);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(dtos);
        return baseResp;
    }


    @GetMapping("/systemSettingAlgorithms")
    @ApiOperation(value = "系统设置中算法下拉列表接口")
    public BaseResp<CommonResp<AlgorithmDTO>> getAlgorithmListSystemSetting(@ApiParam(value = "城市Type")Integer cityType) throws Exception{

        try {
            // 获取算法列表
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t ->  AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType())
                    || AlgorithmConstants.HOLIDAY_ALGORITHM_TYPE.equals(t.getType())
                    || AlgorithmConstants.SPECIAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());

            // 获取自动预测算法
            String defaultAlgorithm = null;
            SystemData systemSetting = settingSystemService.getSystemSetting();
            List<AlgorithmDO> viewAlgorithms = null;
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityType)){
                    defaultAlgorithm = systemSetting.getProvinceNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityType)){
                    defaultAlgorithm = systemSetting.getCityNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
                }
            }

            List<AlgorithmDTO> dtos = new ArrayList<>();
            for (AlgorithmDO algorithmVO : viewAlgorithms) {
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(algorithmVO.getId());
                dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                if (defaultAlgorithm.equals(algorithmVO.getId())) {
                    dto.setDefault(true);
                }
                dtos.add(dto);
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }



    /**
     * 获取算法列表
     */
    public BaseResp getAlgorithmReport(String date, String caliberId, String cityId) {
        try {
            Date d = this.getSystemDate();
            if (date != null) {
                d = DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            }
            if (caliberId == null) {
                caliberId = this.getCaliberId();
            }
            if (StringUtils.isEmpty(cityId)) {
                cityId = this.getLoginCityId();
            }
            String reportAlgorithmId = null;
            LoadCityFcDO fcVO = loadCityFcService.getReport(cityId, caliberId, d);
            if (fcVO != null) {
                reportAlgorithmId = AlgorithmConstants.MD_ALGORITHM_ID;
            } else {
                SystemData systemSetting = settingSystemService.getSystemSetting();
                if (cityId.equals(CityConstants.PROVINCE_ID)) {
                    reportAlgorithmId = systemSetting.getProvinceNormalAlgorithm();
                } else {
                    reportAlgorithmId = systemSetting.getCityNormalAlgorithm();
                }
            }
            //先查人工决策算法，无，再查系统默认算法，无，就找任意一条有预测数据的算法
            List<AlgorithmDTO> dtos = new ArrayList<>();
            // 获取算法列表
            List<AlgorithmDO> algorithmVOS = algorithmService.getAllAlgorithms();
            for (AlgorithmDO algorithmVO : algorithmVOS) {
                String code = algorithmVO.getCode();
                String id = algorithmVO.getId();
                if (id.equals(AlgorithmConstants.MD_ALGORITHM_ID) || code.equals(AlgorithmEnum.FORECAST_XGBOOST.getType())
                    || code.equals(AlgorithmEnum.FORECAST_SVM_LARGE.getType())
                    || code.equals(AlgorithmEnum.FORECAST_INNOVATION.getType()) || code
                    .equals(AlgorithmEnum.FORECAST_SVM.getType()) || code
                    .equals(AlgorithmEnum.COMPREHENSIVE_MODEL.getType())
                    || code.equals(AlgorithmEnum.REPLENISH_LGB.getType())) {
                    AlgorithmDTO algorithmDTO = new AlgorithmDTO();
                    algorithmDTO.setId(algorithmVO.getId());
                    algorithmDTO.setAlgorithm(algorithmVO.getAlgorithmCn());
                    if (id.equals(reportAlgorithmId)) {
                        algorithmDTO.setDefault(true);
                    }
                    dtos.add(algorithmDTO);
                }

            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            throw newBusinessException("02I20180001");
        }

    }

    /**
     * 获取城市列表
     */
    @ApiOperation("获取城市列表")
    @GetMapping("/city")
    public BaseResp<CommonResp<CityDTO>> getCity() {
        try {
            String cityId = this.getLoginCityId();
            CityDO city = cityService.findCityById(cityId);
            List<CityDO> cityVOS = new ArrayList<>();
            if (city.getType() == 1) {
                cityVOS = cityService.findAllCitys();
            } else {
                cityVOS.add(city);
            }
            List<CityDTO> dtos = new ArrayList<CityDTO>();
            for (CityDO cityVO : cityVOS) {
                CityDTO dto = new CityDTO();
                dto.setId(cityVO.getId());
                dto.setCity(cityVO.getCity());
                dtos.add(dto);
            }
            CommonResp<CityDTO> commonResp = new CommonResp<CityDTO>();
            commonResp.setDataList(dtos);
            BaseResp resp = BaseResp.succResp();
            resp.setData(commonResp);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取早中晚高峰
     */
    @ApiOperation("获取早中晚高峰")
    @GetMapping("/peak")
    public BaseResp<PeakTimeDTO> getPeak() {
        try {
            // 获取高峰时段
            String peakTime = settingSystemService.getValue(SystemConstant.PEAK_TIME);
            String[] times = peakTime.split(",");
            PeakTimeDTO peakTimeDTO = new PeakTimeDTO();
            peakTimeDTO.setEarlyPeak(Arrays.asList(times[0].split("~")));
            peakTimeDTO.setNoonPeak(Arrays.asList(times[1].split("~")));
            peakTimeDTO.setNightPeak(Arrays.asList(times[2].split("~")));
            BaseResp<PeakTimeDTO> resp = BaseResp.succResp();
            resp.setData(peakTimeDTO);
            return resp;
        } catch (Exception e) {
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取时段列表
     */
    @ApiOperation("获取时段列表")
    @GetMapping("/periods")
    public BaseResp<PeriodsResp> getPeriods(Integer type) {
        try {
            PeriodsResp periodsResp = new PeriodsResp();
            // 负荷时刻点数
            periodsResp.setLoadPeriods(ColumnUtil.getDayTimes(Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO));
            // 气象时刻点数
            periodsResp.setWeatherPeriods(ColumnUtil.getDayTimes(Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO));
            BaseResp<PeriodsResp> resp = BaseResp.succResp();
            resp.setData(periodsResp);
            return resp;
        } catch (Exception e) {
            throw newBusinessException("02I20180001");
        }
    }

    @ApiOperation("获取产业列表")
    @RequestMapping(value = "/industry", method = RequestMethod.GET)
    public BaseResp getIndustryInfo(Integer level) {
        BaseResp resp = BaseResp.succResp();
        List<BaseIndustryDTO> typeList = new ArrayList<>();
        // 展示前5层级
        List<BaseIndustryInfoDO> industryInfoByLevel = baseIndustryInfoService.findIndustryInfoByLevel().stream().
                filter(t->t.getLevel() < level).sorted(
                Comparator.comparing(BaseIndustryInfoDO::getOrderNo)).collect(Collectors.toList());;
        if (!CollectionUtils.isEmpty(industryInfoByLevel)) {
            for (BaseIndustryInfoDO industryInfo : industryInfoByLevel) {
                BaseIndustryDTO industryDTO = new BaseIndustryDTO();
                industryDTO.setId(industryInfo.getCode());
                industryDTO.setName(industryInfo.getName());
                industryDTO.setLevel(industryInfo.getLevel());
                industryDTO.setParentId(industryInfo.getParentCode());
                typeList.add(industryDTO);
            }
        }
        try {
            if (!CollectionUtils.isEmpty(typeList)) {
                typeList.stream().filter(t -> t.getParentId() != null).map(menu -> {
                    List<BaseIndustryDTO> children = getChildren(menu, typeList);
                    menu.setChildrenDTOList(children);
                    return menu;
                }).collect(Collectors.toList());
            }
            resp.setData(typeList.get(0));
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取口径列表
     */
    @ApiOperation("获取口径列表")
    @GetMapping("/caliber")
    public BaseResp<CommonResp<CaliberDTO>> getCaliberList() {
        try {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            List<CaliberDTO> dtos = new ArrayList<CaliberDTO>();
            for (CaliberDO caliberVO : caliberVOS) {
                if (!this.getLoginCityId().equals(Constants.PROVINCE_ID) && caliberVO.getId()
                    .equals(Constants.CALIBER_ID_BG_QW)) {
                    continue;
                }
                CaliberDTO dto = new CaliberDTO();
                dto.setId(caliberVO.getId());
                dto.setCaliber(caliberVO.getName());
                dto.setDefault(this.getCaliberId().equals(caliberVO.getId()));
                if (!this.getLoginCityId().equals(Constants.PROVINCE_ID) && caliberVO.getId()
                    .equals(Constants.CALIBER_ID_BG_DS)) {
                    dto.setDefault(true);
                }
                dtos.add(dto);
            }
            CommonResp<CaliberDTO> commonResp = new CommonResp<CaliberDTO>();
            commonResp.setDataList(dtos);
            BaseResp resp = BaseResp.succResp();
            resp.setData(commonResp);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 设置默认口径
     */
    @ApiOperation("设置默认口径")
    @RequestMapping(value = "/caliber/default", method = RequestMethod.POST)
    public BaseResp setDefaultCaliber(@RequestBody JSONObject jsonObject) {
        try {
            String caliberId = jsonObject.get("caliberId").toString();
            if (StringUtils.isBlank(caliberId)) {
                return BaseResp.failResp("口径不可为空");
            }
            request.getSession().setAttribute(CacheConstants.CALIBER_ID, caliberId);
            return BaseResp.succResp();
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 上报结果查询页面 获取算法列表 增加最终上报选项
     */
    @ApiOperation("上报结果查询页面")
    @GetMapping("/reportResult/algorithm")
    public BaseResp<CommonResp<AlgorithmDTO>> geReportResulttAlgorithm() {
        try {
            // 获取算法列表
            List<AlgorithmDO> algorithmVOS = algorithmService.getAllAlgorithms();
            List<AlgorithmDTO> dtos = new ArrayList<AlgorithmDTO>();
            CityDO cityById = cityService.findCityById(getLoginCityId());
            AlgorithmDTO algorithmDTO = new AlgorithmDTO();
            algorithmDTO.setId(AlgorithmEnum.RESULT_REPORT.getId());
            algorithmDTO.setAlgorithm(AlgorithmEnum.RESULT_REPORT.getDescription());
            algorithmDTO.setDefault(true);
            dtos.add(algorithmDTO);
            for (AlgorithmDO algorithmVO : algorithmVOS) {
                if (algorithmVO.getCode().equals(AlgorithmEnum.FORECAST_SIMILAR.getType())) {
                    continue;
                }
                if (cityById.getType().equals(CityConstants.CITY_TYPE)) {
                    if (AlgorithmEnum.COMPREHENSIVE_MODEL.getType().equals(algorithmVO.getCode()) ||
                        AlgorithmEnum.FORECAST_INNOVATION.getType().equals(algorithmVO.getCode())) {
                        continue;
                    }
                }
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(algorithmVO.getId());
                dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                dtos.add(dto);
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取城市列表
     */
    @ApiOperation("获取城市列表")
    @PostMapping("/editTour")
    public BaseResp editTour() {
        LoadUserDO loadUserDO = userService.findUserById(this.getLoginUserId());
        if (Objects.isNull(loadUserDO)){
            return BaseResp.failResp("未找到用户");
        }
        loadUserDO.setIsNeedAiTour(false);
        userService.doSaveOrUpdateUser(loadUserDO);
        return BaseResp.succResp();
    }

    @ApiOperation(value = "获取所有枚举")
    @GetMapping("/getAllEnum")
    public BaseResp<TsieEnum> getAllEnum() {
        return baseDataService.getAllEnum();
    }

    private List<BaseIndustryDTO> getChildren(BaseIndustryDTO menu, List<BaseIndustryDTO> list) {
        List<BaseIndustryDTO> collect = list.stream()
                .filter(o -> menu.getId().equals(o.getParentId()))
                .map(cat-> {
                    List<BaseIndustryDTO> children = getChildren(cat, list);
                    cat.setChildrenDTOList(children);
                    return cat;
                })
                .collect(Collectors.toList());
        return collect;
    }
}

