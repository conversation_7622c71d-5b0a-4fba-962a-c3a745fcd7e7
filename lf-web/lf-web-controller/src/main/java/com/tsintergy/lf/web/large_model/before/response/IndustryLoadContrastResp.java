/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/17 18:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.large_model.before.response;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * @description: 负荷对照
 * <AUTHOR>
 * @date 2025/07/05 11:36
 * @version: 1.0
 */
@ApiModel(description = "负荷对照")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndustryLoadContrastResp implements DTO {


    @ApiModelProperty(value = "日期")
    private String date;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "系统负荷")
    private BigDecimal cityLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "产业负荷")
    private BigDecimal industryLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷偏差")
    private BigDecimal deviationLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷占比")
    private BigDecimal rate;

}
