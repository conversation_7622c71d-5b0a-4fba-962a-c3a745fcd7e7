package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAlgorithmService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmCurveDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyDetailValueDataDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.evalucation.request.AccuracyAlgorithmIdsRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description: 多算法预测准确率查询页面
 *
 * <AUTHOR>
 * @create 2025-02-13
 * @since 1.0.0
 */
@RequestMapping("/algorithm")
@RestController
public class AccuracyAlgorithmController extends BaseController {

    @Autowired
    AccuracyAlgorithmService accuracyAlgorithmService;

    @RequestMapping(value = "/accuracy", method = RequestMethod.POST)
    public BaseResp<List<AccuracyAlgorithmDataDTO>> getListByCityIdAndAlgorithmDate(@RequestBody AccuracyAlgorithmIdsRequest request)
            throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyAlgorithmDataDTO> algorithmListData = accuracyAlgorithmService.getAlgorithmListData(request.getCityId(),
                request.getStartDate(), request.getEndDate(), request.getAlgorithmIds(), this.getCaliberId());
        baseResp.setData(algorithmListData);
        return baseResp;
    }

    @RequestMapping(value = "/accuracy/detail", method = RequestMethod.POST)
    public BaseResp<List<AccuracyDetailValueDataDTO>> getListByCityIdAndAlgorithmDetailDate(@RequestBody AccuracyAlgorithmIdsRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyDetailValueDataDTO> algorithmListDetailData = accuracyAlgorithmService.getAlgorithmListDetailData(
                request.getCityId(), request.getStartDate(), request.getEndDate(), request.getAlgorithmIds(), this.getCaliberId(), request.getDays());
        baseResp.setData(algorithmListDetailData);
        return baseResp;
    }

    @RequestMapping(value = "/accuracy/detail/curve", method = RequestMethod.POST)
    public BaseResp<List<AccuracyAlgorithmCurveDataDTO>> getListByCityIdAndAlgorithmDetailCurveDate(@RequestBody AccuracyAlgorithmIdsRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyAlgorithmCurveDataDTO> algorithmListDetailData = accuracyAlgorithmService.getAlgorithmListDetailCurveData(
                request.getCityId(), request.getStartDate(), request.getEndDate(), request.getAlgorithmIds(), this.getCaliberId(), request.getDays());
        baseResp.setData(algorithmListDetailData);
        return baseResp;
    }
}
