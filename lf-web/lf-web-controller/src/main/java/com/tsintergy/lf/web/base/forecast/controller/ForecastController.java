package com.tsintergy.lf.web.base.forecast.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsieframework.core.component.cache.service.RedisCacheUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.dto.SkipHolidayDTO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadCityDayReportServiceDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CommonSetting;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.*;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.web.base.check.response.CommonResp;
import com.tsintergy.lf.web.base.common.util.ExcelMoreDayForecastListener;
import com.tsintergy.lf.web.base.common.util.TxtTransferUtil;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.evalucation.controller.EvalucationController;
import com.tsintergy.lf.web.base.evalucation.request.AccuracyRequest;
import com.tsintergy.lf.web.base.forecast.request.*;
import com.tsintergy.lf.web.base.forecast.response.*;
import com.tsintergy.lf.web.base.util.CheckoutFileTypeSecurity;
import com.tsintergy.lf.web.base.weather.response.WeatherResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.SpelParserConfiguration;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

/**
 * 预测
 *
 * <AUTHOR>
 * @date 2018-02-10
 */
@Api(tags = "预测")
@RequestMapping("/forecast")
@RestController
public class ForecastController extends CommonBaseController {

    private Logger logger = LoggerFactory.getLogger(ForecastController.class);

    /**
     * 为空的错误码
     */
    private final String NULL_ERR = "01C20180002";
    //结果上报
    private final Integer PUSH = 1;
    //结果暂存
    private final Integer SAVE = 2;
    @Autowired
    private ForecastService forecastService;
    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;
    @Autowired
    private SettingSystemService settingSystemService;
    @Autowired
    private SimilarDayService similarDayService;
    @Autowired
    private WeatherCityFcService weatherCityFcService;
    @Autowired
    private LoadCityHisService loadCityHisService;
    @Autowired
    private LoadCityFcService loadCityFcService;
    @Autowired
    private LoadCityFcTempService loadCityFcTempService;
    @Autowired
    private WeatherCityHisService weatherCityHisService;
    @Autowired
    private ForecastInfoService forecastInfoService;
    @Autowired
    private AlgorithmService algorithmService;
    @Autowired
    private ForecastResultStatService forecastResultStatService;
    @Autowired
    private HolidayService holidayService;
    @Autowired
    private CityService cityService;
    @Autowired
    private LoadFeatureStatService loadFeatureStatService;
    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    private ReportSystemService reportSystemService;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 预测概况
     */
    @ApiOperation("预测概况")
    @GetMapping("/overview")
    public BaseResp<ForecastOverviewDTO> getMethodsPrecision(@ApiParam(value = "日期") Date date, @ApiParam(value = "口径id") String caliberId) throws Exception {

        if (date == null) {
            // 显示昨天的数据
            date = DateUtils.addDays(this.getSystemDate(), -1);
        }
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }
        String cityId = this.getLoginCityId();
        BaseResp resp = BaseResp.succResp();
        ForecastOverviewDTO forecastOverviewDTO = forecastService.getForecastOverview(cityId, date, caliberId);
        if (forecastOverviewDTO != null) {
            resp.setData(forecastOverviewDTO);
            return resp;
        } else {
            return new BaseResp("T706");
        }


    }

    /**
     * 获取气象预测的特征和综合指标 气象预报信息
     */
    @ApiOperation("获取气象预测的特征和综合指标")
    @GetMapping("/weather/overview")
    public BaseResp<WeatherFeatureCityDayFcDO> getFcWeather(@ApiParam(value = "日期") Date targetDate) throws Exception {
        String cityId = this.getLoginCityId();
        // cityId = generateCityId(cityId);
        cityId = cityService.findWeatherCityId(cityId);
        BaseResp<WeatherFeatureCityDayFcDO> response = BaseResp.succResp("查询成功");
        if (null == targetDate) {
            throw newBusinessException("T701");
        }
        response.setData(weatherFeatureCityDayFcService.findWeatherFeatureCityFcVOByDate(cityId, targetDate));
        return response;
    }


    /**
     * get autoForecast data and its ref data
     */
    @ApiOperation("获取预测负荷、基准日、相似日和高峰时间")
    @RequestMapping(value = "/auto", method = RequestMethod.GET)
    public BaseResp<ForecastResp> getAutoForecast(ForecastRequest forecastRequest) throws Exception {
        Date today = this.getSystemDate();
        BaseResp<ForecastResp> resp = BaseResp.succResp("查询成功");
        ForecastResp forecastResp = new ForecastResp();
        //目标日---》系统日期后一天
        //基准日---》目标日期前两天
        //预测曲线-》 预测负荷
        //相似日曲线》预测气象数据当作目标气象，查询相似日
        if (forecastRequest.getTargetDay() == null) {
            forecastRequest.setTargetDay(DateUtil.getMoveDay(today, 1));
        }
        if (forecastRequest.getCityId() == null) {
            forecastRequest.setCityId(this.getLoginCityId());
        }
        if (forecastRequest.getCaliberId() == null) {
            forecastRequest.setCaliberId(getCaliberId(forecastRequest.getCityId()));
        }
        String cityId = forecastRequest.getCityId();
        //预测曲线,算法id为空时,默认查询上报数据 否则查询对应算法
        if (StringUtils.isBlank(forecastRequest.getAlgorithmId())) {
            ForecastDTO forecastDTO = loadCityFcService
                    .findReportForecastDTO(forecastRequest.getTargetDay(), cityId, forecastRequest.getCaliberId());
            forecastResp.setForecast(forecastDTO);
        } else {
            //查询预测曲线
            ForecastDTO forecastDTO = loadCityFcService
                    .findForecastDTO(forecastRequest.getTargetDay(), cityId, forecastRequest.getAlgorithmId(),
                            forecastRequest.getCaliberId());
            //查询最终上报的曲线
            LoadCityFcDO report = loadCityFcService
                    .getReport(forecastRequest.getCityId(), forecastRequest.getCaliberId(),
                            forecastRequest.getTargetDay());
            forecastResp.setForecast(forecastDTO);
            //如果是人工决策的id  则是查询最终上报的曲线
            if (forecastRequest.getAlgorithmId().equals(AlgorithmConstants.MD_ALGORITHM_ID)) {
                //如果算法id是人工决策的算法id 则是查询最终上报的曲线
                ForecastDTO forecastDTO1 = new ForecastDTO();
                if (report != null) {
                    forecastDTO1.setList(BasePeriodUtils
                            .toList(report, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                    forecastResp.setForecast(forecastDTO1);
                }
            }
            if (forecastResp.getForecast() != null && forecastResp.getForecast().getList() != null) {
                forecastResp.setModifyLoad(forecastResp.getForecast().getList());
            } else {
                //如果查询的数据为空，则返回一条修正曲线为0的曲线。
                List reportLoad = new ArrayList() {
                    {
                        for (int i = 0; i < 96; i++) {
                            add(new BigDecimal(0));
                        }
                    }
                };
                forecastResp.setModifyLoad(reportLoad);
            }
            if (report != null) {//有最终上报的曲线 展示最终上报的曲线
                forecastResp.setReportLoad(BasePeriodUtils
                        .toList(report, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                forecastResp.setModifyLoad(forecastResp.getReportLoad());
            }

            if (forecastDTO != null && !forecastRequest.getAlgorithmId()
                    .equals(AlgorithmConstants.MD_ALGORITHM_ID)) {//人工修正的时候不返回置信最上限下限
                //获取置信最大最小限
                Map<String, List<BigDecimal>> map = forecastService
                        .getMaxMinConfidence(cityId, forecastRequest.getCaliberId(),
                                forecastResp.getForecast().getAlgorithmId(), forecastRequest.getTargetDay());
                if (!CollectionUtils.isEmpty(map) || map != null) {
                    //置信上限
                    forecastResp.setMax(map.get("max"));
                    //置信下限
                    forecastResp.setMin(map.get("min"));
                }
            }

        }
        //基准日
        List<LoadCityHisDO> loadCityHisVOS = loadCityHisService
                .getLoadCityHisDOS(cityId, forecastRequest.getCaliberId(),
                        DateUtils.addDays(forecastRequest.getTargetDay(), -2),
                        DateUtils.addDays(forecastRequest.getTargetDay(), -2));
        if (!CollectionUtils.isEmpty(loadCityHisVOS)) {
            forecastResp.setBase(BasePeriodUtils.toList(loadCityHisVOS.get(0), Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        //获取近七日平均偏差率
        List<BigDecimal> list = forecastService
                .getAvgDeviation(cityId, forecastRequest.getCaliberId(), forecastRequest.getAlgorithmId(),
                        forecastRequest.getTargetDay());
        forecastResp.setDeviation(list);
        //气象取值----如果是省 则用省会城市的气象
        cityId = cityService.findWeatherCityId(cityId);
        forecastResp
                .setWeatherNameDTOS(weatherCityFcService.findAllByDateAndCityId(cityId,
                        forecastRequest.getTargetDay(), StringUtils.EMPTY));
        // 预测曲线
        if (forecastResp.getForecast() != null && forecastResp.getForecast().getList() != null) {
            forecastResp.setForecastLoad(forecastResp.getForecast().getList());
        }
        // 历史曲线
        Date hisDate = DateUtils.addDays(forecastRequest.getTargetDay(), -1);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(cityId, hisDate, hisDate,
                forecastRequest.getCaliberId());
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
            forecastResp.setHistoryLoad(bigDecimals);
        }
        // 子网累加
        ForecastDTO forecastDTO = loadCityFcService
                .findForecastDTO(forecastRequest.getTargetDay(), cityId, AlgorithmEnum.REGION_FORECAST.getId(),
                        forecastRequest.getCaliberId());
        if (forecastDTO != null && forecastDTO.getList() != null && CityConstants.PROVINCE_ID.equals(cityId)) {
            forecastResp.setSubnetsLoad(forecastDTO.getList());
        }
        resp.setData(forecastResp);
        return resp;
    }


    /**
     * get all type weather 逐时气象
     */
    @ApiOperation("获取预测负荷、基准日、相似日和高峰时间")
    @RequestMapping(value = "/weather", method = RequestMethod.GET)
    public BaseResp<WeatherResp> getWeatherFc(@ApiParam(value = "目标日") Date targetDate,
                                              @ApiParam(value = "城市id") String cityId,
                                              @ApiParam(value = "算法id") String algorithmId) throws Exception {
        if (null == targetDate) {
            throw newBusinessException("T701");
        }
        if (cityId == null) {
            cityId = this.getLoginCityId();
        }
        BaseResp<WeatherResp> resp = BaseResp.succResp("查询成功");
        WeatherResp weatherResp = new WeatherResp();
        weatherResp.setWeatherList(weatherCityFcService.findAllByDateAndCityId(cityId, targetDate, algorithmId));
        resp.setData(weatherResp);
        return resp;
    }

    private String generateCityId(String cityId) throws Exception {
        CityDO cityVO = cityService.findCityById(cityId);
        if (cityVO.getType() == 1) {
            // todo wangchen 城市表设计不太合理，这里是要选择省会，先写死
            cityId = "2";
        }
        return cityId;
    }

    /**
     * 曲线平滑
     */
    @ApiOperation("曲线平滑")
    @RequestMapping(value = "/auto/{id}/data", method = RequestMethod.POST)
    public BaseResp<SmoothLineResp> smoothLine(@PathVariable("id") String id, @Validated @RequestBody SmoothLineRequest smoothLineRequest,
                                               BindingResult result) throws Exception {
        validateFormValue(result);
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("平滑成功");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService.smoothLine(smoothLineRequest.getForecast()));
        resp.setData(smoothLineResp);
        return resp;
    }

    /**
     * 曲线修正
     */
    @ApiOperation("曲线修正")
    @RequestMapping(value = "/auto/recorrect", method = RequestMethod.POST)
    @OperateLog(operate = "曲线修正")
    public BaseResp<SmoothLineResp> recorrectLoad(@Validated @RequestBody RecorrectRequest recorrectRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("修正完成");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService
                .recorrectLoad(recorrectRequest.getForecast(), recorrectRequest.getMaxLoad(),
                        recorrectRequest.getMinLoad()));
        resp.setData(smoothLineResp);
        return resp;
    }

    /**
     * 结果上报or暂存
     */
    @ApiOperation("结果上报or暂存")
    @RequestMapping(value = "/auto", method = RequestMethod.POST)
    @OperateLog(operate = "结果上报")
    public BaseResp saveResult(@RequestBody @Validated ResultPushOrSaveRequest resultRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp resp = BaseResp.succResp("保存成功");
        Date startDate = org.apache.commons.lang3.time.DateUtils.addDays(new Date(), +1);
        String dateToStrFORMAT = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd");
        if (StringUtils.isBlank(resultRequest.getCaliberId())) {
            resultRequest.setCaliberId(getCaliberId(resultRequest.getCityId()));
        }
        if (StringUtils.isBlank(resultRequest.getCityId())) {
            resultRequest.setCityId(this.getLoginCityId());
        }
        // 结果上报
        if (PUSH.equals(resultRequest.getType())) {
            BaseResp checkResp = checkReportDeadlineTime(resultRequest.getCityId(), CommonSetting.ORDINARY_DAY,
                    resultRequest.getForecastList().getTargetDay(), resultRequest.getForecastList().getTargetDay(), null);
            if (checkResp.getRetCode().equals("T0")) {
                return checkResp;
            }
            LoadCityFcDO loadCityFcVO = new LoadCityFcDO();
            LoadCityDayReportServiceDO loadCityDayReportDO = new LoadCityDayReportServiceDO();
            // 人工决策算法
            loadCityFcVO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            loadCityFcVO.setCityId(resultRequest.getCityId());
            loadCityFcVO.setDate(new java.sql.Date(resultRequest.getForecastList().getTargetDay().getTime()));
            loadCityFcVO.setUserId(this.getLoginUserId());
            Map<String, BigDecimal> dataMap = ColumnUtil
                    .listToMap(resultRequest.getForecastList().getValue(), Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(loadCityFcVO, dataMap);
            loadCityFcVO.setCaliberId(resultRequest.getCaliberId());
            loadCityFcVO.setReport(true);
            loadCityFcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadCityFcVO.setSucceed(true);
            loadCityFcVO.setRecommend(false);

            loadCityDayReportDO.setDate(new java.sql.Date(resultRequest.getForecastList().getTargetDay().getTime()));
            loadCityDayReportDO.setCityId(resultRequest.getCityId());
            loadCityDayReportDO.setCaliberId(Constants.CALIBER_ID_BG_DS);
            loadCityDayReportDO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            if (DateUtil.getDateToStr(resultRequest.getForecastList().getTargetDay()).equals(dateToStrFORMAT)) {
                Date today = org.apache.commons.lang3.time.DateUtils.addDays(
                        resultRequest.getForecastList().getTargetDay(), -1);
                Date date = DateUtil.getDate(DateUtil.getDateToStrFORMAT(
                        today, "yyyy-MM-dd") + " 09:00", "yyyy-MM-dd HH:mm");
                if (System.currentTimeMillis() <= date.getTime()) {
                    loadCityDayReportDO.setFirstReportTime(new Timestamp(System.currentTimeMillis()));
                }
            } else {
                loadCityDayReportDO.setFirstReportTime(new Timestamp(System.currentTimeMillis()));
            }
            loadCityDayReportDO.setLastReportTime(new Timestamp(System.currentTimeMillis()));
            try {
                // 更新地市上报
                if (!Constants.PROVINCE_TYPE.equals(loadCityDayReportDO.getCityId())) {
                    loadCityFcService.doSaveOrUpdate(loadCityDayReportDO);
                }
                loadCityFcService.doReport(loadCityFcVO);
                loadCityFcBatchService.doSave(loadCityFcVO);
            } catch (BusinessException e) {
                if ("03D20180601".equals(e.getErrorCode())) {
                    resp.setRetCode("T0");
                    resp.setRetMsg("操作失败 超过上报时间，不允许上报！");
                    return resp;
                }
            }
            // 统计预测结果
            forecastResultStatService.statForecastResult(loadCityFcVO.getCityId(), null, loadCityFcVO.getCaliberId(),
                    loadCityFcVO.getDate(), loadCityFcVO.getDate());
            //统计预测负荷特性
            loadFeatureStatService.doStatLoadFeatureCityDayFc(Arrays.asList(resultRequest.getCityId()), loadCityFcVO.getDate(), loadCityFcVO.getDate(),
                    loadCityFcVO.getCaliberId());
        }
        // 结果暂存
        else if (SAVE.equals(resultRequest.getType())) {
            LoadCityFcTempDO loadCityFcTempVO = new LoadCityFcTempDO();
            loadCityFcTempVO.setCityId(resultRequest.getCityId());
            loadCityFcTempVO.setCaliberId(resultRequest.getCaliberId());
            loadCityFcTempVO.setDate(new java.sql.Date(resultRequest.getForecastList().getTargetDay().getTime()));
            if (StringUtils.isBlank(resultRequest.getForecastList().getAlgorithmId())) {
                loadCityFcTempVO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            } else {
                loadCityFcTempVO.setAlgorithmId(resultRequest.getForecastList().getAlgorithmId());
            }
            loadCityFcTempVO.setUserId(this.getLoginUserId());
            Map<String, BigDecimal> dataMap = ColumnUtil
                    .listToMap(resultRequest.getForecastList().getValue(), Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(loadCityFcTempVO, dataMap);
            loadCityFcTempService.doSaveOrUpdateLoadCityFcTempDO(loadCityFcTempVO);

        }
        return resp;
    }

    @ApiOperation("添加参照曲线")
    @RequestMapping(value = "/addReference", method = RequestMethod.GET)
    @OperateLog(operate = "添加参照曲线")
    public BaseResp<ReferenceResponse> getReferenceLoad(ReferenceRequest referenceRequest) throws Exception {
        if (StringUtils.isBlank(referenceRequest.getCaliberId())) {
            referenceRequest.setCaliberId(getCaliberId(referenceRequest.getCityId()));
        }
        if (StringUtils.isBlank(referenceRequest.getCityId())) {
            referenceRequest.setCityId(this.getLoginCityId());
        }

        String caliberId = referenceRequest.getCaliberId();
        String cityId = referenceRequest.getCityId();
        Date date = referenceRequest.getReferenceDate();
        // 根据曲线类型以及日期参照
        BaseResp baseResp = BaseResp.succResp();
        if (referenceRequest.getType() != null) {
            List<BigDecimal> load = loadCityHisService.findLoadCityHisDO(date, cityId, caliberId,
                    referenceRequest.getType());
            cityId = cityService.findWeatherCityId(cityId);
            List<WeatherNameDTO> weatherCityHisVOS = weatherCityHisService
                    .findAllByDateAndCityId(cityId, referenceRequest.getReferenceDate());
            if (CollectionUtils.isEmpty(weatherCityHisVOS) && CollectionUtils.isEmpty(load)) {
                return new BaseResp("T706");
            }
            ReferenceResponse response = new ReferenceResponse();
            response.setDate(referenceRequest.getReferenceDate());
            CityDO city = cityService.findCityById(referenceRequest.getCityId());
            String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            //response.setReferenceName(city.getCity() + "-" + dateStr);
            if ("1".equals(referenceRequest.getType())) {
                response.setReferenceName(dateStr + " 历史曲线");
            } else {
                response.setReferenceName(dateStr + " 上报曲线");
            }
            response.setLoad(load);
            response.setWeatherLoad(weatherCityHisVOS);
            baseResp.setData(response);
        } else {
            List<BigDecimal> loadCityFcDO = loadCityFcService.findLoadCityFcDO(date, cityId, caliberId,
                    referenceRequest.getAlgorithmId());
            ReferenceResponse response = new ReferenceResponse();
            response.setLoad(loadCityFcDO);
            baseResp.setData(response);
        }
        return baseResp;
    }


    /**
     * 查询暂存结果
     */
    @ApiOperation("查询暂存结果")
    @RequestMapping(value = "/auto/cache", method = RequestMethod.GET)
    public BaseResp<CacheResp> queryLoadCache(@ApiParam(value = "城市id") String cityId, @ApiParam(value = "口径id") String caliberId) throws Exception {
        if (StringUtils.isBlank(caliberId)) {
            caliberId = this.getCaliberId();
        }
        if (StringUtils.isBlank(cityId)) {
            cityId = this.getLoginCityId();
        }
        Date today = this.getSystemDate();
        BaseResp<CacheResp> resp = BaseResp.succResp("查询成功");
        CacheResp cacheResp = new CacheResp();
        LoadCityFcTempDO loadCityFcTempVO;
        try {
            loadCityFcTempVO = loadCityFcTempService.findLoadCityFcTempDO(cityId, caliberId, today);
        } catch (BusinessException e) {
            if (NULL_ERR.equals(e.getErrorCode())) {
                return resp;
            } else {
                throw e;
            }
        }
        if (loadCityFcTempVO == null || loadCityFcTempVO.getDate().before(today)) { // 如果暂存的结果是历史日期的，则不需要返回
            return new BaseResp("T706");
        }
        //暂存曲线
        ForecastDTO forecastDTO = new ForecastDTO();
        forecastDTO.setId(loadCityFcTempVO.getId());
        forecastDTO.setAlgorithmId(loadCityFcTempVO.getAlgorithmId());
        forecastDTO.setList(BasePeriodUtils
                .toList(loadCityFcTempVO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO));
        forecastDTO.setCaliberId(loadCityFcTempVO.getCaliberId());
        cacheResp.setForecast(forecastDTO);
        cacheResp.setTargetDay(loadCityFcTempVO.getDate());
        resp.setData(cacheResp);
        return resp;

    }

    /**
     * 功能描述: <br>
     * <p>
     * 获取预测信息详情  forecast_info_basic 预测信息表
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/7/31 16:25
     */
    @ApiOperation("获取预测信息详情")
    @RequestMapping(value = "/auto/detail", method = RequestMethod.GET)
    public BaseResp<ForecastInfoResp> queryForecastInfo(@ApiParam(value = "id") String id, @ApiParam(value = "开始时间") Timestamp startTime, @ApiParam(value = "口径id") String caliberId) throws Exception {
        if (StringUtils.isBlank(caliberId)) {
            caliberId = this.getCaliberId();
        }
        ForecastInfoDO forecastInfoVO = null;
        if (StringUtils.isNotBlank(id) || null != startTime) {
            Thread.sleep(8000);
            int i = 6;
            while (i-- > 0) {

                forecastInfoVO = forecastInfoService.findForecastInfoDOByPk(id);

                if (forecastInfoVO.getUpdatetime().after(startTime) || null != forecastInfoVO.getReportType()) {
                    break;
                }
                Thread.sleep(8000);
            }
        } else {
            forecastInfoVO = forecastInfoService.findLatestForecastInfoByCityId(this.getLoginCityId(), caliberId);
        }
        forecastInfoVO = forecastInfoService.findLatestForecastInfoByCityId(this.getLoginCityId(), caliberId);
        BaseResp<ForecastInfoResp> baseResp = BaseResp.succResp();
        ForecastInfoResp forecastInfoResp = new ForecastInfoResp();
        BeanUtils.copyProperties(forecastInfoVO, forecastInfoResp);
        forecastInfoResp.setAlgorithm(algorithmService.getAlgorithmCn(forecastInfoVO.getAlgorithmId()));
        baseResp.setData(forecastInfoResp);
        return baseResp;
    }


    @ApiOperation("获取预测设置详情")
    @RequestMapping(value = "/setting", method = RequestMethod.GET)
    public BaseResp<ForecastSettingResp> getForecastSetting() throws Exception {
        BaseResp<ForecastSettingResp> baseResp = BaseResp.succResp();
        ForecastSettingResp forecastSettingResp = new ForecastSettingResp();
        SystemData systemSetting = this.settingSystemService.getSystemSetting();
        if (this.getLoginCityId().equals(CityConstants.PROVINCE_ID)) {
            forecastSettingResp.setCustomizeAlgorithmId(systemSetting.getProvinceNormalAlgorithm());
            forecastSettingResp.setRecommendAlgorithmId(systemSetting.getProvinceNormalAlgorithm());
            forecastSettingResp
                    .setRecommendAlgorithm(algorithmService.getAlgorithmCn(systemSetting.getProvinceNormalAlgorithm()));
        } else {
            forecastSettingResp.setCustomizeAlgorithmId(systemSetting.getCityNormalAlgorithm());
            forecastSettingResp.setRecommendAlgorithmId(systemSetting.getCityNormalAlgorithm());
            forecastSettingResp
                    .setRecommendAlgorithm(algorithmService.getAlgorithmCn(systemSetting.getCityNormalAlgorithm()));
        }
        forecastSettingResp.setIsRecommend(true);
        forecastSettingResp.setStartTime("08:00");
        List<AlgorithmDO> algorithmVOS = algorithmService.getAllAlgorithms();
        List<AlgorithmDTO> algorithmDTOS = new ArrayList<AlgorithmDTO>(10);
        for (AlgorithmDO algorithmVO : algorithmVOS) {
            AlgorithmDTO algorithmDTO = new AlgorithmDTO();
            algorithmDTO.setId(algorithmVO.getId());
            algorithmDTO.setAlgorithm(algorithmVO.getAlgorithmCn());
            algorithmDTOS.add(algorithmDTO);
        }
        forecastSettingResp.setAlgorithms(algorithmDTOS);
        baseResp.setData(forecastSettingResp);
        return baseResp;
    }

    /**
     * 导入预测负荷
     */
    @ApiOperation("预测负荷导入")
    @RequestMapping(value = "/auto/import", method = RequestMethod.POST)
    @OperateLog(operate = "预测负荷导入")
    public BaseResp importForecastData(
            @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile, String cityId)
            throws Exception {
        String originalFilename = forecastFile.getOriginalFilename();
        String type = originalFilename.substring(originalFilename.length() - 3);
        if (type.equals("txt")) {
            String dateStr = "";
            List<String> collect = TxtTransferUtil.convertToBigDecimal(forecastFile);
            List<BigDecimal> result = new ArrayList<>(16);
            if (!CollectionUtils.isEmpty(collect)) {
                for (String s : collect) {
                    dateStr = s.split("\t")[0].substring(0, 4) + "-" + s.split("\t")[0].substring(4, 6) + "-" + s.split(
                            "\t")[0].substring(6, 8);
                    result.add(new BigDecimal(s.split("\t")[2]));
                }
            }
            Date date = DateUtil.getDate(dateStr, "yyyy-MM-dd");
            // 校验是否可以上报
            Boolean report = isReport(date, result, cityId);
            if (report) {
                return BaseResp.succResp("导入成功");
            } else {
                return BaseResp.succResp("超过上报截至时间，不允许导入");
            }
        } else {
            boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(forecastFile);
            if (!check) {
                return BaseResp.failResp("文件安全性存在问题");
            }
            EasyExcel.read(forecastFile.getInputStream(), ExcelMoreDayForecastData.class,
                            new ExcelMoreDayForecastListener(loadCityFcService, cityId, getLoginUserId(), getCaliberId()))
                    .sheet()
                    .headRowNumber(1).doRead();
            return BaseResp.succResp("导入成功");
        }
    }

    /**
     * 导入多天预测负荷
     */
    @ApiOperation("导入多天预测负荷")
    @RequestMapping(value = "/days/import", method = RequestMethod.POST)
    @OperateLog(operate = "多天预测负荷导入")
    public BaseResp importMoreDayForecastData(
            @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile, @ApiParam(value = "城市id") String cityId)
            throws Exception {
        boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(forecastFile);
        if (!check) {
            return BaseResp.failResp("文件安全性存在问题");
        }
        EasyExcel.read(forecastFile.getInputStream(), ExcelMoreDayForecastData.class,
                        new ExcelMoreDayForecastListener(loadCityFcService, cityId, getLoginUserId(), getCaliberId()))
                .sheet()
                .headRowNumber(1).doRead();
        return BaseResp.succResp("导入成功");
    }

    /**
     * 废弃 暂时保留by wangh
     *
     * @param forecastNormalRequest
     * @param result
     * @return
     * @throws Exception
     */
    @ApiOperation("高级预测正常日")
    @RequestMapping(value = "/normal", method = RequestMethod.POST)
    @OperateLog(operate = "高级预测正常日")
    @Deprecated
    public BaseResp<List<ForecastNormalDTO>> forecastNormal(@Validated @RequestBody ForecastNormalRequest forecastNormalRequest,
                                                            BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp resp = BaseResp.succResp();
        if (StringUtils.isBlank(forecastNormalRequest.getCityId())) {
            forecastNormalRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(forecastNormalRequest.getCaliberId())) {
            forecastNormalRequest.setCaliberId(this.getCaliberId());
        }
        if (forecastNormalRequest.getTargetDay() == null) {
            forecastNormalRequest.setTargetDay(this.getSystemDate());
        }
        if (null != forecastNormalRequest.getFirst() && forecastNormalRequest.getFirst()) {
            List<ForecastNormalDTO> forecastNormalDTOS =
                    forecastService.getForecastNormal(forecastNormalRequest.getCityId(), this.getCaliberId(),
                            forecastNormalRequest.getTargetDay(), forecastNormalRequest.getTargetDay());
            resp.setData(forecastNormalDTOS);

        } else {
            SkipHolidayDTO skipHolidayDTO = holidayService.findSkipHolidays(forecastNormalRequest.getTargetDay(),
                    DateUtil.getMoveDay(forecastNormalRequest.getTargetDay(),
                            forecastNormalRequest.getForecastDay() - 1));
            if (null != skipHolidayDTO.getHolidays() && skipHolidayDTO.getHolidays().size() > 0) {
                StringBuilder msg = new StringBuilder("预测日中包含节假日(");
                for (Date holiday : skipHolidayDTO.getHolidays()) {
                    msg.append(DateUtils.date2String(holiday, DateFormatType.SIMPLE_DATE_FORMAT_STR) + "、");
                }
                msg.replace(msg.length() - 2, msg.length() - 1, ")");
                msg.append(",已为您自动跳过并完成预测！");
                resp.setRetMsg(msg.toString());
            } else {
                resp.setRetMsg("预测成功！");
            }
            List<ForecastNormalDTO> forecastNormalDTOS = null;
            forecastNormalDTOS =
                    forecastService.doAdvancedForecastNormal(forecastNormalRequest.getCityId(),
                            forecastNormalRequest.getCaliberId(), skipHolidayDTO.getNormalDays(),
                            super.getNormalAlgorithmEnums());
            resp.setData(forecastNormalDTOS);
        }
        return resp;
    }


    @ApiOperation("高级预测节假日")
    @RequestMapping(value = "/holiday", method = RequestMethod.POST)
    @OperateLog(operate = "高级预测节假日")
    @Deprecated
    public BaseResp<List<ForecastNormalDTO>> forecastHoliday(@Validated ForecastHolidayRequest forecastHolidayRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp resp = BaseResp.succResp("预测成功！");
        if (StringUtils.isBlank(forecastHolidayRequest.getCityId())) {
            forecastHolidayRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(forecastHolidayRequest.getCaliberId())) {
            forecastHolidayRequest.setCaliberId(this.getCaliberId());
        }
        HolidayDO holidayVO = holidayService.findHolidayVOByPk(forecastHolidayRequest.getHolidayId());
        Date startDate = holidayVO.getStartDate();
        Date endDate = holidayVO.getEndDate();
        List<ForecastNormalDTO> forecastNormalDTOS = forecastService.doForecastHoliday(getUid(),
                forecastHolidayRequest.getCityId(), forecastHolidayRequest.getCaliberId(), startDate, endDate);
        if (forecastNormalDTOS == null || forecastNormalDTOS.size() < 1) {
            throw newBusinessException("T706");
        }
        resp.setData(forecastNormalDTOS);
        return resp;
    }


    @ApiOperation("获取相似日")
    @RequestMapping(value = "/get/similarDays", method = RequestMethod.GET)
    public BaseResp<SimilardayResp> getSimilarDays(@ApiParam() String cityId, String caliberId, Date date) throws Exception {
        if (StringUtils.isBlank(cityId)) {
            cityId = this.getLoginCityId();
        }
        if (StringUtils.isBlank(caliberId)) {
            caliberId = this.getCaliberId();
        }
        if (date == null) {//系统日期后一天
            date = DateUtils.addDays(this.getSystemDate(), 1);
        }
        Date start = DateUtils.addDays(date, -70);
        Date end = DateUtils.addDays(date, -1);
        List<SimilarDateBean> similarDateBeans = null;
        try {
            similarDateBeans = similarDayService.listSimilarDayWithFcWeather(cityId, caliberId,
                    date, start, end, 5, super.getLoginUserId());
        } catch (Exception e) {
            return new BaseResp("T706");
        }
        if (CollectionUtils.isEmpty(similarDateBeans)) {
            return new BaseResp("T706");
        }
        List<SimilarDayDO> similarDayVOS = new ArrayList<>();
        for (SimilarDateBean similarDateBean : similarDateBeans) {
            SimilarDayDO similarDayVO = new SimilarDayDO();
            similarDayVO.setSimilarDay(new java.sql.Date(similarDateBean.getDate().getTime()));
            similarDayVO.setDegree(similarDateBean.getDegree());
            similarDayVOS.add(similarDayVO);
        }
        SimilardayResp similardayResp = new SimilardayResp();
        similardayResp.setTargetDate(date);
        similardayResp.setSimilarDayVOList(similarDayVOS);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(similardayResp);
        return baseResp;
    }


    @ApiOperation("获取上报状态")
    @RequestMapping(value = "/get/status", method = RequestMethod.GET)
    public BaseResp<SimilardayResp> getReportStatu(String cityId, String caliberId, Date date) throws Exception {
        SimilardayResp similardayResp = new SimilardayResp();
        LoadCityFcDO loadCityFcVO = loadCityFcService.getReport(cityId, getCaliberId(cityId), date);
        if (loadCityFcVO != null) {
            Date reportTime = loadCityFcVO.getReportTime();
            if (reportTime == null) {
                if (loadCityFcVO.getUpdatetime() != null) {
                    reportTime = loadCityFcVO.getUpdatetime();
                } else {
                    reportTime = loadCityFcVO.getCreatetime();
                }
            }
            if (reportTime != null) {
                boolean report = settingSystemService.findIsLater(reportTime, loadCityFcVO);
                if (report) {
                    similardayResp.setReportStatus(3);
                } else {
                    similardayResp.setReportStatus(1);
                }
                similardayResp.setTodayReportTime(DateUtils.date2String(reportTime, DateFormatType.DATE_FORMAT_STR));
            }
        } else {
            similardayResp.setReportStatus(2);
        }


        Integer cityType = cityService.findCityById(cityId).getType();

        //子网的上报时间要查询系统设置
        SystemData systemSetting = settingSystemService.getSystemSetting();
        if (CityConstants.PROVINCE_TYPE.equals(cityType)) {
            String endReportTime = systemSetting.getProvinceEndReportTime();
            similardayResp.setReportTime(endReportTime);
        } else {
            //上报截止时间
            similardayResp.setReportTime(systemSetting.getEndReportTime());
        }


        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(similardayResp);
        return baseResp;
    }

    @ApiOperation("导出")
    @RequestMapping("/export")
    @OperateLog(operate = "导出预测曲线")
    public BaseResp importForecast(ForecastRequest forecastRequest, HttpServletResponse response,
                                   HttpServletRequest request) throws Exception {

        //查询预测曲线
        ForecastDTO forecastDTO = loadCityFcService
                .findForecastDTO(forecastRequest.getTargetDay(), forecastRequest.getCityId(),
                        forecastRequest.getAlgorithmId(),
                        forecastRequest.getCaliberId());

        if (forecastDTO == null || forecastDTO.getList() == null) {
            throw newBusinessException("T706");
        }

        // 创建工作簿对象
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        XSSFSheet sheet = xssfWorkbook.createSheet();

        XSSFRow firstRow = sheet.createRow(0);
        XSSFRow secondRow = sheet.createRow(1);
        XSSFRow thirdRow = sheet.createRow(2);

        XSSFCell secondRowFirstCell = secondRow.createCell(0);
        secondRowFirstCell.setCellValue(DateUtils.date2String(forecastRequest.getTargetDay(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR));

        XSSFCell firstRowFirstCell = firstRow.createCell(0);
        firstRowFirstCell.setCellValue("预测负荷上报信息");

        XSSFCell thirdRowFirstCell = thirdRow.createCell(0);
        XSSFCell thirdRowSecondCell = thirdRow.createCell(1);
        thirdRowFirstCell.setCellValue("时间");
        thirdRowSecondCell.setCellValue("预测负荷");

        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, 0, 1);
        CellRangeAddress cellRangeAddress2 = new CellRangeAddress(1, 1, 0, 1);
        sheet.addMergedRegion(cellRangeAddress);
        sheet.addMergedRegion(cellRangeAddress2);

        for (int i = 3; i < forecastDTO.getList().size() + 3; i++) {
            int a = i - 3;
            String s = (a / 4 < 10 ? ("0" + a / 4) : (a / 4 + "")) + (":") + (a % 4 == 0 ? "00" : a % 4 * 15);
            XSSFRow row = sheet.createRow(i);
            XSSFCell firstCell = row.createCell(0);
            XSSFCell secondCell = row.createCell(1);
            firstCell.setCellValue(s);
            secondCell.setCellValue(forecastDTO.getList().get(a).doubleValue());
        }
        if (xssfWorkbook != null) {
            try {
                response.setContentType("text/html;charset=UTF-8");
                request.setCharacterEncoding("UTF-8");
                String dateStr = DateUtils.date2String(forecastRequest.getTargetDay(),
                        DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                AlgorithmDO algorithmVOById = algorithmService.getAlgorithmDOById(forecastRequest.getAlgorithmId());
                String fileName = dateStr + "负荷预测数据-" + algorithmVOById.getAlgorithmCn() + ".xlsx";

                String header = request.getHeader("User-Agent").toUpperCase();
                if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
                } else if (header.contains("MSIE") || header.contains("TRIDENT") || header.contains("EDGE")) {
                    fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
                } else if (request.getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
                }
                OutputStream out = response.getOutputStream();
                response.setHeader("Content-disposition",
                        "attachment; filename=" + fileName);
                xssfWorkbook.write(out);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("导出成功");
        return baseResp;
    }

    private String getCaliberId(String cityId) {
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)) {
            return Constants.CALIBER_ID_BG_QW;
        } else {
            return Constants.CALIBER_ID_BG_DS;
        }
    }

    private Boolean isReport(Date date, List<BigDecimal> result, String cityId) throws Exception {
        Date now = new Date();
        List<ReportSystemInitDO> short_report_time = reportSystemService.findReportSystemConfig(cityId,
                "short_report_time", null);
        if (!CollectionUtils.isEmpty(short_report_time)) {
            ReportSystemInitDO reportSystemInitDO = short_report_time.get(0);
            Boolean status = reportSystemInitDO.getStatus();
            if (status != null && status) {
                // 有限制
                String endTime = reportSystemInitDO.getValue().split("-")[1];
                String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
                Date endDate = DateUtil.getDate(dateStr + " " + endTime, "yyyy-MM-dd HH:mm");
                if (endDate.compareTo(now) > 0) {
                    // 可以导入
                    LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
                    Map<String, BigDecimal> dataMap = ColumnUtil
                            .listToMap(result, Constants.LOAD_CURVE_START_WITH_ZERO);
                    BasePeriodUtils.setAllFiled(loadCityFcDO, dataMap);
                    loadCityFcDO.setCityId(cityId);
                    loadCityFcDO.setReport(true);
                    loadCityFcDO.setCaliberId(getCaliberId(cityId));
                    loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
                    loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                    loadCityFcDO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
                    loadCityFcService.doReport(loadCityFcDO);
                } else {
                    return false;
                }
            } else {
                LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
                Map<String, BigDecimal> dataMap = ColumnUtil
                        .listToMap(result, Constants.LOAD_CURVE_START_WITH_ZERO);
                BasePeriodUtils.setAllFiled(loadCityFcDO, dataMap);
                loadCityFcDO.setCityId(cityId);
                loadCityFcDO.setReport(true);
                loadCityFcDO.setCaliberId(getCaliberId(cityId));
                loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
                loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
                loadCityFcService.doReport(loadCityFcDO);
            }
        } else {
            LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
            Map<String, BigDecimal> dataMap = ColumnUtil
                    .listToMap(result, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(loadCityFcDO, dataMap);
            loadCityFcDO.setCityId(cityId);
            loadCityFcDO.setReport(true);
            loadCityFcDO.setCaliberId(getCaliberId(cityId));
            loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
            loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadCityFcDO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            loadCityFcService.doReport(loadCityFcDO);
        }
        return true;
    }

    @Autowired
    private EvalucationController evalucationController;

    /**
     *  获取ai 智能推荐算法_历史版本
     * @param cityId
     * @param caliberId
     * @param forecastDate
     * @return
     */
    @GetMapping("/getAiAlgorithmRecommend_old")
    public BaseResp getAiAlgorithmRecommendOld(String cityId,String caliberId,Date forecastDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();


        String key = cityId + "-" + caliberId + "-" + forecastDate.getTime();
        //先在redis中判断是否存在
        String result = RedisCacheUtils.get(key, String.class);
        if (Objects.isNull(result)){
            result = getAiResult(cityId, caliberId, forecastDate);
            //暂定保存10天
            RedisCacheUtils.put(key,result,864000L);
        }
        try {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            JSONObject dataText = (JSONObject) jsonObject.get("data");
            JSONObject outputs = (JSONObject) dataText.get("outputs");
            Object text = outputs.get("text");
            baseResp.setData(text.toString());
            System.out.println("1111\n1111");
            return  baseResp;
        }catch (Exception e){
            return BaseResp.failResp("智能信息获取有误");
        }
    }

    /**
     *  获取ai 智能推荐算法_历史版本
     * @param cityId
     * @param caliberId
     * @param forecastDate
     * @return
     */
    @GetMapping("/getAiAlgorithmRecommend")
    public BaseResp getAiAlgorithmRecommend(String cityId,String caliberId,Date forecastDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        try {
            String result = forecastService.getAiAlgorithmRecommendContent(cityId, caliberId, forecastDate);
//            forecastService.getAiAlgorithmRecommendHandler(cityId, caliberId, forecastDate);
            baseResp.setData(result);
            return  baseResp;
        }catch (Exception e){
            return BaseResp.failResp("智能信息获取有误");
        }
    }

    @GetMapping("/getForecastSuggestion")
    public BaseResp getForecastSuggestion(String cityId, Date systemDate) throws Exception {
        WeatherSourceFcAccuracyCompareDTO forecastSuggestion = forecastService.getForecastSuggestion(cityId, systemDate);
        String suggestion = buidSuggestion(forecastSuggestion);
        return BaseRespBuilder.success().setData(suggestion, String.class).build();
    }

    private String getAiResult(String cityId, String caliberId, Date forecastDate) throws Exception {

        String result = null;

//        getExcel(cityId,caliberId,forecastDate);

        String date = DateUtil.formateDate(forecastDate);

        String url = "http://192.168.3.101:2333/v1/workflows/run";
        String jsonBody = "{\n" +
                "    \"inputs\": {\"input_text\": "+"\"" + date +"\""+"},\n" +
                "    \"response_mode\": \"blocking\",\n" +
                "    \"user\": \"user_01\"\n" +
                "}";

        return sendPostRequest(url, jsonBody);
    }

    public static String sendPostRequest(String url, String jsonBody) {
        HttpURLConnection conn = null;
        BufferedReader reader = null;
        try {
            // 1. 创建连接并配置
            URL apiUrl = new URL(url);
            conn = (HttpURLConnection) apiUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(5000); // 连接超时5秒
            conn.setReadTimeout(10*60*1000);    // 读取超时5分钟

            // 2. 设置Headers
            conn.setRequestProperty("Authorization", "Bearer app-hlZKDfbuSqHJwiOcP9xfLQmn");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setDoOutput(true);

            // 3. 发送请求体
            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 4. 获取响应状态码
            int status = conn.getResponseCode();
            System.out.println("HTTP Status Code: " + status);

            // 5. 读取响应内容
            StringBuilder response = new StringBuilder();
            if (status >= 200 && status < 300) {
                reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            } else {
                reader = new BufferedReader(new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8));
            }
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            System.out.println("Response Body: " + response);
            return response.toString();

        } catch (Exception e) {
            System.err.println("Request failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 6. 关闭资源
            try {
                if (reader != null) reader.close();
                if (conn != null) conn.disconnect();
            } catch (IOException e) {
                System.err.println("Error closing resources: " + e.getMessage());
            }
        }
        return null;
    }

    void getExcel(String cityId, String caliberId, Date forecastDate) throws Exception {

        Date endDate = DateUtil.getMoveDay(forecastDate, -1);
        Date startDate = DateUtil.getMoveDay(endDate, -14);
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);

        AccuracyRequest accuracyRequest = new AccuracyRequest(cityId, caliberId,startDate,endDate);
        accuracyRequest.setStartPeriod("0015");
        accuracyRequest.setEndPeriod("2400");
        accuracyRequest.setAccuracyType("1");
        accuracyRequest.setIsHoliday("1");
        BaseResp<CommonResp<CityAccuracyDTO>> methodsPrecision = evalucationController.getMethodsPrecision(accuracyRequest, null);
        CommonResp<CityAccuracyDTO> data = methodsPrecision.getData();
        List<CityAccuracyDTO> dataList = data.getDataList();
        Map<Date, CityAccuracyDTO> accuracyDTOMap = dataList.stream().collect(Collectors.toMap(CityAccuracyDTO::getDate, Function.identity()));


        //人工修正
        List<LoadCityFcDO> reportLoadFc = loadCityFcService.findFcByAlgorithmId(cityId, caliberId, AlgorithmEnum.FORECAST_MODIFY.getId(),startDate, endDate);
        Map<java.sql.Date, List<BigDecimal>> fcMap = reportLoadFc.stream().collect(Collectors.toMap(LoadCityFcDO::getDate, LoadCityFcDO::getloadList));

        //实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        Map<java.sql.Date, List<BigDecimal>> hisMap = loadCityHisDOS.stream().collect(Collectors.toMap(LoadCityHisDO::getDate, LoadCityHisDO::getloadList));

        List<WeatherCityHisDO> hisTemp = weatherCityHisService.findWeatherCityHisDOs(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<java.sql.Date, List<BigDecimal>> hisTempMap = hisTemp.stream().collect(Collectors.toMap(WeatherCityHisDO::getDate, WeatherCityHisDO::getWeatherList));


        List<WeatherCityHisDO> hisHum = weatherCityHisService.findWeatherCityHisDOs(cityId, WeatherEnum.HUMIDITY.getType(), startDate, endDate);
        Map<java.sql.Date, List<BigDecimal>> hisHumMap = hisHum.stream().collect(Collectors.toMap(WeatherCityHisDO::getDate, WeatherCityHisDO::getWeatherList));


        List<WeatherCityFcDO> fcTemp = weatherCityFcService.findWeatherCityFcDOs(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<java.sql.Date, List<BigDecimal>> fcTempMap = fcTemp.stream().collect(Collectors.toMap(WeatherCityFcDO::getDate, WeatherCityFcDO::getWeatherList));

        List<WeatherCityFcDO> fcHum = weatherCityFcService.findWeatherCityFcDOs(cityId, WeatherEnum.HUMIDITY.getType(), startDate, endDate);
        Map<java.sql.Date, List<BigDecimal>> fcHumMap = fcHum.stream().collect(Collectors.toMap(WeatherCityFcDO::getDate, WeatherCityFcDO::getWeatherList));


        List<HolidayDO> holidayVOS = holidayService.findHolidayVOS(startDate, endDate);

        //调休日
        List<Date> allOffDates = holidayService.getAllOffDates();

        File file =  new File("/Users/<USER>/Desktop/aiExcel/"+DateUtil.getYearByDate(forecastDate)+"年"+DateUtil.getMonthDate(forecastDate) +"月份负荷预测数据.xlsx");
        try (Workbook workbook = new HSSFWorkbook()) {
            //模型预测准确率sheet页
            Sheet accSheet = getAccSheet(workbook, dataList);

            //人工负荷预测sheet页
            Sheet modifySheet = get96PointSheet(workbook,"2人工负荷预测值");

            //人工负荷预测sheet页
            Sheet hisLoadSheet = get96PointSheet(workbook,"3负荷实际值");

            //温度预测
            Sheet fcTempSheet = get96PointSheet(workbook,"4温度预测值");

            //温度实际
            Sheet hisTempSheet = get96PointSheet(workbook,"5温度实际值");

            //湿度预测值
            Sheet fcHumSheet = get96PointSheet(workbook,"6湿度预测值");

            //湿度实际值
            Sheet hisHumSheet = get96PointSheet(workbook,"7湿度实际值");

            Sheet holidaySheet = getHolidaySheet(workbook);

            //遍历日期
            for (int i = 0; i < dates.size(); i++) {
                Date date = dates.get(i);
                //准确率
                mergeAccWork(accSheet, i, date, accuracyDTOMap);

                //人工修正
                merge96PointWork(modifySheet,i,date,fcMap);

                //实际负荷
                merge96PointWork(hisLoadSheet,i,date,hisMap);

                //预测温度
                merge96PointWork(fcTempSheet,i,date,fcTempMap);

                //实际温度
                merge96PointWork(hisTempSheet,i,date,hisTempMap);

                //预测湿度
                merge96PointWork(fcHumSheet,i,date,fcHumMap);

                //实际湿度
                merge96PointWork(hisHumSheet,i,date,hisHumMap);

                //节假日
                mergeHolidayWork(holidaySheet,i,date,holidayVOS,allOffDates);
            }


            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                workbook.write(outputStream);
            }

            System.out.println("Excel file has been created successfully.");

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static Sheet getAccSheet(Workbook workbook, List<CityAccuracyDTO> dataList) {
        Sheet accSheet = workbook.createSheet("1各模型预测精度");
        Row row1 = accSheet.createRow(0);
        row1.createCell(0).setCellValue("日期");
        List<String> algoName = dataList.get(0).getAlgorithmDetail().stream().map(AlgorithmAccuracyDTO::getName).collect(Collectors.toList());
        for (int i = 0; i < algoName.size(); i++) {
            row1.createCell(i+1).setCellValue(algoName.get(i));
        }
        return accSheet;
    }

    /**
     * 96点sheet
     * @param workbook
     * @param sheetName
     * @return
     */
    private Sheet get96PointSheet(Workbook workbook,String sheetName) {
        Sheet sheet = workbook.createSheet(sheetName);
        Row row1 = sheet.createRow(0);
        row1.createCell(0).setCellValue("日期");
        List<String> times = generateTimeList();

        for (int i = 0; i < times.size(); i++) {
            row1.createCell(i+1).setCellValue(times.get(i));
        }
        return sheet;
    }

    private Sheet getHolidaySheet(Workbook workbook) {
        Sheet sheet = workbook.createSheet("8节假日信息");
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("日期");
        row.createCell(1).setCellValue("星期");
        row.createCell(2).setCellValue("是否属于工作日");
        row.createCell(3).setCellValue("属于何种法定假日");
        return sheet;
    }


    private static void mergeAccWork(Sheet accSheet, int i, Date date, Map<Date, CityAccuracyDTO> accuracyDTOMap) {
        Row row = accSheet.createRow(i + 1);
        row.createCell(0).setCellValue(DateUtil.getDateToStrFORMAT(date,"yyyy/MM/dd"));
        CityAccuracyDTO cityAccuracyDTO = accuracyDTOMap.get(date);
        if (Objects.isNull(cityAccuracyDTO)){
            return;
        }
        List<AlgorithmAccuracyDTO> algorithmDetail = cityAccuracyDTO.getAlgorithmDetail();
        for (int j = 0; j < algorithmDetail.size(); j++) {
            row.createCell(j+1).setCellValue(String.valueOf(Objects.nonNull(algorithmDetail.get(j).getAccuracy())?algorithmDetail.get(j).getAccuracy():""));
        }
    }

    private void merge96PointWork(Sheet sheet, int i, Date date, Map<java.sql.Date, List<BigDecimal>> fcMap) {
        Row row = sheet.createRow(i + 1);
        row.createCell(0).setCellValue(DateUtil.getDateToStrFORMAT(date,"yyyy/MM/dd"));
        List<BigDecimal> loads = fcMap.get(new java.sql.Date(date.getTime()));
        if (Objects.isNull(loads)){
            return;
        }
        for (int j = 0; j < loads.size(); j++) {
            row.createCell(j+1).setCellValue(String.valueOf(Objects.nonNull(loads.get(j))?loads.get(j):""));
        }
    }

    private void mergeHolidayWork(Sheet holidaySheet, int i, Date date, List<HolidayDO> holidayVOS,List<Date> allOffDates) {
        Row row = holidaySheet.createRow(i + 1);
        row.createCell(0).setCellValue(DateUtil.getDateToStrFORMAT(date,"yyyy/MM/dd"));
        row.createCell(1).setCellValue(DateUtil.getWeek(date));
        List<Date> holidayDates = new ArrayList<>();
        for (HolidayDO holidayVO : holidayVOS) {
            holidayDates.addAll(DateUtil.getListBetweenDay(holidayVO.getStartDate(),holidayVO.getEndDate()));
        }
        boolean holiday = holidayDates.contains(date);
        String work = "1";
        if (holiday){
            work = "0";
        }else{
            //在节假日或者，周末并且调休日不包含改日期的情况下为休息日
            boolean weekend = DateUtil.isWeekend(date);
            if (weekend && !allOffDates.contains(date)){
                work = "0";
            }
        }
        row.createCell(2).setCellValue(work);
        String holidayValue = "0";
        if (holiday){
            List<HolidayDO> holidayDOList = holidayVOS.stream().filter(t -> DateUtil.getListBetweenDay(t.getStartDate(), t.getEndDate()).contains(date)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(holidayDOList)){
                holidayValue = holidayDOList.get(0).getHoliday();
            }
        }
        row.createCell(3).setCellValue(holidayValue);
    }

    public static List<String> generateTimeList() {
        List<String> times = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime time = LocalTime.of(0, 15); // 起始时间 00:15

        while (!time.equals(LocalTime.MIDNIGHT)) { // 直到 24:00（Java 中表示为 00:00）
            times.add(time.format(formatter));
            time = time.plusMinutes(15);
        }

        // 手动添加 24:00（因为 LocalTime.MIDNIGHT 是 00:00）
        times.add("24:00");
        return times;
    }

    private String buidSuggestion(WeatherSourceFcAccuracyCompareDTO forecastSuggestion) {
        Map<String, Object> finalValueMap = BeanUtil.beanToMap(forecastSuggestion);
        finalValueMap.put("accuracy", BigDecimalFunctions.multiply(forecastSuggestion.getAccuracy(), BigDecimal.valueOf(100)).setScale(2, BigDecimalFunctions.ROUNDING_MODE));
        finalValueMap.put("maxTemperatureDiffStatus", forecastSuggestion.getMaxTemperatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");
        finalValueMap.put("maxTemperatureDiff", forecastSuggestion.getMinTemperatureDiff().abs());
        finalValueMap.put("avgTemperatureDiffStatus", forecastSuggestion.getAvgTemperatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");
        finalValueMap.put("avgTemperatureDiff", forecastSuggestion.getAvgTemperatureDiff().abs());
        finalValueMap.put("minTemperatureDiffStatus", forecastSuggestion.getMinTemperatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");
        finalValueMap.put("minTemperatureDiff", forecastSuggestion.getMinTemperatureDiff().abs());

        finalValueMap.put("baogongAvgTemperatureDiff", forecastSuggestion.getBaogongAvgTemperatureDiff().abs());
        finalValueMap.put("baogongAvgTemperatureDiffStatus", forecastSuggestion.getBaogongAvgTemperatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");
        finalValueMap.put("noonAvgTemperatureDiff", forecastSuggestion.getNoonAvgTemperatureDiff().abs());
        finalValueMap.put("noonAvgTemperatureDiffStatus", forecastSuggestion.getNoonAvgTemperatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");
        finalValueMap.put("nightAvgTemperatureDiff", forecastSuggestion.getNightAvgTemperatureDiff().abs());
        finalValueMap.put("nightAvgTemperatureDiffStatus", forecastSuggestion.getNightAvgTemperatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");

        finalValueMap.put("avgHumidityFeatureDiff", forecastSuggestion.getAvgHumidityFeatureDiff().abs());
        finalValueMap.put("avgHumidityFeatureDiffStatus", forecastSuggestion.getAvgHumidityFeatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");

        finalValueMap.put("avgEffectiveTemperatureDiff", forecastSuggestion.getAvgEffectiveTemperatureDiff().abs());
        finalValueMap.put("avgEffectiveTemperatureDiffStatus", forecastSuggestion.getAvgEffectiveTemperatureDiff().compareTo(BigDecimal.ZERO) > 0 ? "上升" : "下降");
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariables(finalValueMap);
        ExpressionParser parser = new SpelExpressionParser();
        String systemContent = readTemplateFile("ForecastSuggestionResultTemplate.txt");
        return parser.parseExpression(systemContent, new TemplateParserContext()).getValue(context, String.class);
    }

    private static String readTemplateFile(String templateName) {
        try (InputStream inputStream = ClassUtils.getDefaultClassLoader().getResourceAsStream(templateName)) {
            if (inputStream == null) {
                throw new RuntimeException("模板文件未找到: ");
            }

            // 使用 ByteArrayOutputStream 读取字节流
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }

            // 转换为 UTF-8 字符串
            return result.toString(StandardCharsets.UTF_8.name());
        } catch (IOException e) {
            throw new RuntimeException("模板文件读取失败", e);
        }
    }
}

