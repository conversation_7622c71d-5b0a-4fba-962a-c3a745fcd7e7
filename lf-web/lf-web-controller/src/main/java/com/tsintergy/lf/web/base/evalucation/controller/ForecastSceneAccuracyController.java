package com.tsintergy.lf.web.base.evalucation.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DailyForecastSceneService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAlgorithmAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.ForecastDeviationDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.ForecastSceneAccracyDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.evalucation.request.ForecastSceneAccuracyQueryRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/forecastSceneAccuracy")
public class ForecastSceneAccuracyController extends BaseController {

    @Autowired
    private DailyForecastSceneService dailyForecastSceneService;

    @PostMapping("/getCityAvgAccuracyList")
    public BaseResp getCityAvgAccuracy(@RequestBody ForecastSceneAccuracyQueryRequest queryRequest) {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = queryRequest.getCaliberId();
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        ForecastSceneAccracyDTO cityAccuracy = dailyForecastSceneService
                .getCityAvgAccuracyList(queryRequest.getCityId(), caliberId, queryRequest.getStartDate(), queryRequest.getEndDate(),
                        queryRequest.getWeatherSecene(), queryRequest.getDateType(), queryRequest.getAlgorithmId());
        baseResp.setData(cityAccuracy);
        return baseResp;
    }

    @PostMapping("/getCityAccuracyList")
    public BaseResp getCityAccuracyList(@RequestBody ForecastSceneAccuracyQueryRequest queryRequest) {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = queryRequest.getCaliberId();
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        List<String> algorithmIdList = queryRequest.getAlgorithmId();
        String algorithmId = null;
        if (CollectionUtil.isNotEmpty(algorithmIdList)) {
            algorithmId = algorithmIdList.get(0);
        }
        List<CityAccuracyDTO> cityAccuracy = dailyForecastSceneService
                .getCityAccuracyList(queryRequest.getCityId(), caliberId, queryRequest.getStartDate(), queryRequest.getEndDate(),
                        queryRequest.getWeatherSecene(), queryRequest.getDateType(), algorithmId, queryRequest.getAccuracyName());
        baseResp.setData(cityAccuracy);
        return baseResp;
    }

    @PostMapping("/getCityAlgorithmAccuracyList")
    public BaseResp getCityAccuracyAlgorithmList(@RequestBody ForecastSceneAccuracyQueryRequest queryRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = queryRequest.getCaliberId();
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        List<CityAlgorithmAccuracyDTO> cityAccuracy = dailyForecastSceneService
                .getCityAlgorithmAccuracyList(queryRequest.getCityId(), caliberId, queryRequest.getStartDate(), queryRequest.getEndDate(),
                        queryRequest.getWeatherSecene(), queryRequest.getDateType(), queryRequest.getAlgorithmId(), queryRequest.getAccuracyName());
        baseResp.setData(cityAccuracy);
        return baseResp;
    }

    @PostMapping("/getForecastDeviation")
    public BaseResp getForecastDeviation(@RequestBody ForecastSceneAccuracyQueryRequest queryRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = queryRequest.getCaliberId();
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        List<String> algorithmIdList = queryRequest.getAlgorithmId();
        String algorithmId = null;
        if (CollectionUtil.isNotEmpty(algorithmIdList)) {
            algorithmId = algorithmIdList.get(0);
        }
        List<ForecastDeviationDTO> cityAccuracy = dailyForecastSceneService
                .getForecastDeviationList(queryRequest.getCityId(), caliberId, queryRequest.getStartDate(), queryRequest.getEndDate(),
                        queryRequest.getWeatherSecene(), queryRequest.getDateType(), algorithmId, queryRequest.getAccuracyName());
        baseResp.setData(cityAccuracy);
        return baseResp;
    }

    @GetMapping("/doStatAccuracyComposite")
    public BaseResp doStatAccuracyComposite(String cityId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        dailyForecastSceneService.doStatDailyForecastScene(cityId, null);
        return baseResp;
    }
}
