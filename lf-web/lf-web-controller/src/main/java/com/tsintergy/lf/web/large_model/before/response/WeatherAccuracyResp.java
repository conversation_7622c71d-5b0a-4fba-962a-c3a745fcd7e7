/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/17 18:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.large_model.before.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @description: 气象准确率
 * <AUTHOR>
 * @date 2025/07/05 11:36
 * @version: 1.0
 */
@ApiModel(description = "气象准确率")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeatherAccuracyResp implements Serializable {


    @ApiModelProperty(value = "气象源")
    private String weatherSource;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "准确率")
    private BigDecimal weatherAccuracy;


}
