/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/17 18:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.large_model.before.response;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @description: 负荷对照
 * <AUTHOR>
 * @date 2025/07/05 11:36
 * @version: 1.0
 */
@ApiModel(description = "负荷对照")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndustryLoadContrastPackageResp implements DTO {


    @ApiModelProperty(value = "负荷对照")
    private List<IndustryLoadContrastResp> dataList;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "占比")
    private BigDecimal rate;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "偏差")
    private BigDecimal deviationLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "相似度")
    private BigDecimal similarity;

}
