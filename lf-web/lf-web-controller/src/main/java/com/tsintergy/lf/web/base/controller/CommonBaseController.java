/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/2/6 13:58 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.controller;

import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

import com.alibaba.fastjson.JSONObject;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.cloud.security.web.common.web.controller.BaseController;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.i18n.MessageSourceFormatter;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.*;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CommonSetting;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/2/6
 * @since 1.0.0
 */
public class CommonBaseController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CommonBaseController.class);

    private static final String NULL_CODE = "T706";


    @Autowired
    private HttpServletRequest request;

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private SettingSystemService settingSystemService;

    private static final String EMPTY = " ";

    private static final String timeStr = ":00";


    protected String getUid() {
        String uid = DateTime.now().toString("yyyyMMddHHmmssSSS");
        return uid;
    }

    protected void validateFormValue(BindingResult result) {
        if (result.hasErrors()) {
            Map<String, String> map = new HashMap();
            List<FieldError> list = result.getFieldErrors();
            StringBuffer sb = new StringBuffer("'{");
            boolean start = false;
            Iterator var6 = list.iterator();

            while (var6.hasNext()) {
                FieldError error = (FieldError) var6.next();
                if (start) {
                    sb.append(",");
                } else {
                    start = true;
                }

                sb.append("''").append(error.getField()).append("''").append(":").append("''")
                    .append(error.getDefaultMessage()).append("''");
                map.put(error.getField(), error.getDefaultMessage());
            }

            String str = JSONObject.toJSONString(map);
            sb.append("'}");
            throw newBusinessException(sb.toString());
        }
    }

    protected HttpServletRequest getServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    protected TsieUserVO getCurrnTsieUserVO() {
        return getLoginUser();
    }

    protected BaseResp getFailResp(String code) {
        BaseResp resp = BaseResp.failResp();
        resp.setRetCode(code);
        resp.setRetMsg(MessageSourceFormatter.format(code, null));
        return resp;
    }

    /**
     * get the caliberId in session
     *
     * @return
     */
    protected String getCaliberId() {
        String caliberId = (String) request.getSession().getAttribute(CacheConstants.CALIBER_ID);
        caliberId = caliberId != null ? caliberId : caliberService.findSystemDefalutCaliberDO().getId();
        if (null == caliberId) {
            logger.error("获取不到登陆口径id");
            throw newBusinessException("T814", "获取不到登陆口径id");
        }
        return caliberId;
//        return "1";
    }

    protected String getLoginCityId() {
        String cityId = (String) getServletRequest().getSession().getAttribute(CacheConstants.CITY_ID);
        if (null == cityId) {
            logger.error("获取不到登陆用户的城市id");
            throw newBusinessException("T814");
        }
        return cityId;
        // return "3";
    }

    /**
     * @param obj 结果集
     * @return baseResp
     * <AUTHOR>
     */
    public BaseResp baseResp(Object obj) {
        BaseResp resp = BaseResp.succResp();
        if (obj == null || (obj instanceof List<?> && ((List) obj).size() == 0)) {
            resp.setData(new ArrayList<>());
            resp.setRetCode(NULL_CODE);
            resp.setRetMsg("查询数据为空");
            return resp;
        } else {
            resp.setData(obj);
            return resp;
        }
    }

    /**
     * get the System's time in session
     *
     * @return
     */
    protected Date getSystemDate() {
//        Object attribute = request.getSession().getAttribute(CacheConstants.SYSTEM_DATE);
//
//        if (attribute != null && attribute instanceof String) {
//            Date system = DateUtils.string2Date(attribute.toString(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
//            return system;
//        }
//        Date attributeDate = (Date) attribute;
//        if (null == attributeDate) {
//            logger.error("获取不到系统时间");
//            throw TsieExceptionUtils.newBusinessException("T814");
//        }
//        String date = DateUtils.date2String(attributeDate, DateFormatType.DATE_FORMAT_STR);
        String date = "2025-07-02 21:48:42";
        Date system = DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        return system;
    }

    protected Date getSystemDateTime() {
        Date systemDate = this.getSystemDate();
        String date = DateUtils.date2String(systemDate, DateFormatType.DATE_FORMAT_STR);
        if (null == date) {
            logger.error("获取不到系统时间");
            throw newBusinessException("T814");
        }
        String substring = date.substring(0, 11);
        //如果获取的系统时间没有时间
        Date date1 = new Date();
        String time = DateUtils.date2String(date1, DateFormatType.HOUR_MINUTE_SECOND);
        Date system = DateUtils.string2Date(substring + " " + time, DateFormatType.DATE_FORMAT_STR);
        return system;
    }

    /**
     * get the login userId in session
     *
     * @return
     */
    protected String getLoginUserId() {
        String userId = (String) getServletRequest().getSession()
            .getAttribute(CacheConstants.USER_ID);
        if (null == userId) {
            logger.error("获取不到登陆用户id");
            throw newBusinessException("T814");
        }
        return userId;
    }

    protected HttpServletResponse getResponse() {
        return this.response;
    }


    /**
     * 校验上报的时间
     */
    protected BaseResp checkReportDeadlineTime(String cityId, Integer settingType, Date startDate, Date endDate,
        String startPeriod) throws Exception {
        Integer endSwitch;
        String endTime;
        //查询设置
        String[] split;
        Integer type = cityService.findCityById(cityId).getType();
        if(CityConstants.PROVINCE_TYPE.equals(type)){
            split = settingSystemService.findByFieldId(SystemConstant.PROVINCE_END_REPORT_TIME).getValue().split(Constants.SEPARATOR_PUNCTUATION);
        }else {
            split = settingSystemService.findByFieldId(SystemConstant.END_REPORT_TIME).getValue()
                .split(Constants.SEPARATOR_PUNCTUATION);
        }
        endSwitch = Integer.valueOf(split[0]);
        endTime = split[1];
        //如果上报截止开启
        if (CommonSetting.END_SWITCH.equals(endSwitch)) {
            if (endDate != null) {
                if (startDate.after(endDate)) {
                    return BaseResp.failResp("开始日期必须大于结束日期");
                }
            }
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            Date systemDate = new Date();
            Date system = sf.parse(sf.format(systemDate));
            Date tomorrow = DateUtils.addDays(system, 1);
            Date afterDay = DateUtils.addDays(system, 2);
            StringBuilder builder = new StringBuilder(
                DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR))
                .append(EMPTY);
            if (startPeriod == null) {
                startPeriod = "00:00";
            }
            builder.append(startPeriod).append(timeStr);
            Date start = DateUtils.string2Date(builder.toString(), DateFormatType.DATE_FORMAT_STR);
            String currentTime = new SimpleDateFormat("HHmmss").format(new Date());
            String reportDeadlineTime = endTime.replace(":", "") + "00";
            if (currentTime.compareTo(reportDeadlineTime) >= 1) {
                //如果时间已经大于11点 则开始日期必须是大于T+2的00:00
                if (start.before(afterDay)) {
                    return BaseResp.failResp("上报截止时间为：" + endTime + "。到期后，开始日期必须大于T+2");
                }
            } else if (currentTime.compareTo(reportDeadlineTime) <= -1) {
                //如果还没到上报截止时间可上报  限制是开始日期必须大于T+1的开始时间
                if (start.before(tomorrow)) {
                    return BaseResp.failResp("上报开始日期必须大于T+1");
                }
            }
        }
        return BaseResp.succResp();
    }

    @Autowired
    AlgorithmService algorithmService;


    @Autowired
    CityService cityService;


    /**
     * 获取每日预测跑批的算法列表
     * @return 算法列表
     * <AUTHOR>
     */
    public List<AlgorithmEnum> getNormalAlgorithmEnums() throws Exception{
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
            t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                .equals(t.getType()))
            .collect(Collectors.toList());

        List<AlgorithmDO> viewAlgorithms = null;
        CityDO cityDO = cityService.findCityById(this.getLoginCityId());
        if (CityConstants.PROVINCE_TYPE.equals(cityDO.getType())) {
            viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getProvinceView() == true)
                .collect(Collectors.toList());
        } else if (CityConstants.CITY_TYPE.equals(cityDO.getType())) {
            viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true)
                .collect(Collectors.toList());
        }
        List<String> collect = viewAlgorithms.stream().map(AlgorithmDO::getCode).collect(Collectors.toList());
        List<AlgorithmEnum> enums = new ArrayList<>();
        AlgorithmEnum[] values = AlgorithmEnum.values();
        for(AlgorithmEnum algorithmEnum:values){
            if(collect.contains(algorithmEnum.getType())){
                enums.add(algorithmEnum);
            }
        }
        return enums;
    }
}

