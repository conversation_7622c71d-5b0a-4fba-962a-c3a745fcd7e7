/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/17 18:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.large_model.before.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @description: 负荷曲线和特性
 * <AUTHOR>
 * @date 2025/07/05 11:36
 * @version: 1.0
 */
@ApiModel
@Data
public class LoadFeatureResp implements Serializable {

    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "负荷曲线")
    List<BigDecimal> loadList;
    
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "午间最大负荷")
    private BigDecimal noontimePeak;

    @ApiModelProperty(value = "午间最大负荷出现时刻")
    private String noontimePeakTime;

    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "晚间最大负荷")
    private BigDecimal eveningPeak;
    
    @ApiModelProperty(value = "晚间最大负荷出现时刻")
    private String eveningPeakTime;

    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "最小负荷")
    private BigDecimal minLoad;

    @ApiModelProperty(value = "最小负荷出现时刻")
    private String minTime;
}
