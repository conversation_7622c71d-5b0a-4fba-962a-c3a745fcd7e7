package com.tsintergy.lf.web.base.weather.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class WeatherSourceCurveRequest implements Serializable {

    private String cityId;

    private Date date;

    private String hour;

    private List<String> source;

    private String weatherType;

    private String type;

    private String days;

}
