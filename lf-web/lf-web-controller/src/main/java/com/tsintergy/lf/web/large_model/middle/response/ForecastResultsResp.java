package com.tsintergy.lf.web.large_model.middle.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/7 14:16
 **/
@Data
public class ForecastResultsResp implements Serializable {

    @ApiModelProperty("算法名称")
    private String algorithmName;

    private List<BigDecimal> load;
}
