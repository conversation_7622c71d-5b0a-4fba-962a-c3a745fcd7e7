package com.tsintergy.lf.web.base.evalucation.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class AccuracyAlgorithmIdsRequest implements Serializable {

    private String cityId;

    private Date startDate;

    private Date endDate;

    private List<String> algorithmIds;

    private String days;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<String> getAlgorithmIds() {
        return algorithmIds;
    }

    public void setAlgorithmIds(List<String> algorithmIds) {
        this.algorithmIds = algorithmIds;
    }

    public String getDays() {
        return days;
    }

    public void setDays(String days) {
        this.days = days;
    }
}
