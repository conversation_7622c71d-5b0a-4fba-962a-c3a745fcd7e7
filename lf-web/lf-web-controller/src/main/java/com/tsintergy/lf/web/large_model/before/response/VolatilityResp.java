/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/17 18:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.large_model.before.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @description: 指标分析
 * <AUTHOR>
 * @date 2025/07/05 11:36
 * @version: 1.0
 */
@ApiModel
@Data
public class VolatilityResp implements Serializable {

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷波动性")
    private BigDecimal maxLoadVolatility;

    @BigdecimalJsonFormat(scale =2)
    @ApiModelProperty(value = "最小负荷波动性")
    private BigDecimal minLoadVolatility;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均负荷波动性")
    private BigDecimal avgLoadVolatility;


}
