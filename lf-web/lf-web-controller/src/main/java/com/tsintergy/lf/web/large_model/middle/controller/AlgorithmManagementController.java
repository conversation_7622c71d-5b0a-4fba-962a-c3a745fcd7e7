package com.tsintergy.lf.web.large_model.middle.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.component.cache.service.RedisCacheUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.large_model.api.*;
import com.tsintergy.lf.serviceapi.large_model.pojo.*;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.large_model.before.response.LoadFeatureResp;
import com.tsintergy.lf.web.large_model.middle.response.AiAlgorithmLibraryResp;
import com.tsintergy.lf.web.large_model.middle.response.Day15Resp;
import com.tsintergy.lf.web.large_model.middle.response.DayAlgoAccResp;
import com.tsintergy.lf.web.large_model.middle.response.DayResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * 【事中】-算法管理
 * @Date 2025/7/6 09:45
 **/
@RequestMapping("/middle/algorithmManagement")
@RestController
@Api(tags = "【事中】-算法管理")
public class AlgorithmManagementController extends BaseController {

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    private LmStatisticsCityNear15DayFcService lmStatisticsCityNear15DayFcService;

    @Autowired
    private LmStatisticsCityDayFcService lmStatisticsCityDayFcService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LmAlgorithmLibrarySettingService lmAlgorithmLibrarySettingService;

    @Autowired
    private LmModelDetailsService lmModelDetailsService;

    @Autowired
    private LmParameterDetailsService lmParameterDetailsService;

    @Autowired
    LmLargeModelTextSettingService lmLargeModelTextSettingService;



    @ApiOperation("近15天准确率查询")
    @RequestMapping(value = "/getDay15Accuracy", method = RequestMethod.GET)
    public BaseResp<List<Day15Resp>> getDay15Accuracy(Integer type)  {
        List<Day15Resp> result = new ArrayList<>();
        List<LmStatisticsCityNear15DayFcDO> lmStatisticsCityNear15DayFcDOS = lmStatisticsCityNear15DayFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, this.getSystemDate(), type);
        lmStatisticsCityNear15DayFcDOS.forEach(t->{
            Day15Resp day15Resp = new Day15Resp();
            day15Resp.setAlgorithmId(t.getAlgorithmId());
            day15Resp.setAlgorithmName(t.getAlgorithmName());
            day15Resp.setAccuracy(t.getAccuracy());
            day15Resp.setPass(t.getPass());
            result.add(day15Resp);
        });
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("人工智能算法库")
    @RequestMapping(value = "/getAiAlgorithmLibrary", method = RequestMethod.GET)
    public BaseResp<List<AiAlgorithmLibraryResp>> getAiAlgorithmLibrary()  {
        List<LmAlgorithmLibrarySettingDO> all = lmAlgorithmLibrarySettingService.findAll();
        Date moveDay = DateUtil.getMoveDay(this.getSystemDate(), -1);
        List<LmStatisticsCityDayFcDO> lmStatisticsCityDayFcDOS = lmStatisticsCityDayFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, moveDay, moveDay,null);
        Map<String, LmStatisticsCityDayFcDO> accMap = lmStatisticsCityDayFcDOS.stream().collect(Collectors.toMap(LmStatisticsCityDayFcDO::getAlgorithmName, Function.identity()));
        List<AiAlgorithmLibraryResp> result = new ArrayList<>();
        for (LmAlgorithmLibrarySettingDO lmAlgorithmLibrarySettingDO : all) {
            AiAlgorithmLibraryResp aiAlgorithmLibraryResp = new AiAlgorithmLibraryResp();
            result.add(aiAlgorithmLibraryResp);
            aiAlgorithmLibraryResp.setRouteName(lmAlgorithmLibrarySettingDO.getRouteName());
            String[] split = lmAlgorithmLibrarySettingDO.getAlgorithmNames().split(",");
            List<DayAlgoAccResp> algorithms = new ArrayList<>();
            aiAlgorithmLibraryResp.setAlgorithm(algorithms);
            for (String algorithmName : split) {
                String[] strings = algorithmName.split("_");
                LmStatisticsCityDayFcDO lmStatisticsCityDayFcDO = accMap.get(strings[0]);
                if(Objects.isNull(lmStatisticsCityDayFcDO)) {continue;}
                DayAlgoAccResp day15Resp = new DayAlgoAccResp();
                day15Resp.setAlgorithmName(strings[0]);
                day15Resp.setAccuracy(lmStatisticsCityDayFcDO.getAccuracy());
                day15Resp.setSelected(strings[1].equals("1"));
                algorithms.add(day15Resp);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }


    @ApiOperation("单算法准确率查询")
    @RequestMapping(value = "/getSingleAccuracy", method = RequestMethod.GET)
    public BaseResp<List<DayResp>> getSingleAccuracy(String algorithmName) {
        Date systemDate = this.getSystemDate();
        Date startDate = DateUtil.getMoveDay(systemDate, -15);
        List<LmStatisticsCityDayFcDO> lmStatisticsCityDayFcDOS = lmStatisticsCityDayFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, startDate, systemDate,algorithmName);
        List<DayResp> result = new ArrayList<>();
        for (LmStatisticsCityDayFcDO lmStatisticsCityDayFcDO : lmStatisticsCityDayFcDOS) {
            DayResp dayResp = new DayResp();
            dayResp.setAccuracy(lmStatisticsCityDayFcDO.getAccuracy());
            dayResp.setDate(DateUtil.formateDate(lmStatisticsCityDayFcDO.getDate()));
            dayResp.setPass(lmStatisticsCityDayFcDO.getPass());
            result.add(dayResp);
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result.stream().sorted(Comparator.comparing(DayResp::getDate)).collect(Collectors.toList()));
        return baseResp;
    }





    @ApiOperation("算法详情")
    @RequestMapping(value = "/getAlgorithmDetails", method = RequestMethod.GET)
    public BaseResp<LmModelDetailsDO> getAlgorithmDetails(String algorithmName) {
        LmModelDetailsDO result = lmModelDetailsService.findByAlgorithm(algorithmName);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }



    @ApiOperation("参数详情")
    @RequestMapping(value = "/getParamDetails", method = RequestMethod.GET)
    public BaseResp<List<LmParameterDetailsDO>> getParamDetails(String algorithmName) {
        Date systemDate = this.getSystemDate();
        List<LmParameterDetailsDO> lmParameterDetailsDOS = lmParameterDetailsService.findByAlgorithm(algorithmName);
        for (LmParameterDetailsDO lmParameterDetailsDO : lmParameterDetailsDOS) {
            if (lmParameterDetailsDO.getParamName().equals("BaseDay（基准日）")){
                lmParameterDetailsDO.setParamValue(DateUtil.getDateToStrFORMAT(DateUtil.getMoveDay(systemDate,-1),"yyyyMMdd"));
            }
            if (lmParameterDetailsDO.getParamName().equals("forecastBeginDay")){
                lmParameterDetailsDO.setParamValue(DateUtil.getDateToStrFORMAT(DateUtil.getMoveDay(systemDate,1),"yyyyMMdd"));
            }
            if (lmParameterDetailsDO.getParamName().equals("forecastEndDay")){
                lmParameterDetailsDO.setParamValue(DateUtil.getDateToStrFORMAT(DateUtil.getMoveDay(systemDate,10),"yyyyMMdd"));
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(lmParameterDetailsDOS);
        return baseResp;
    }

    @ApiOperation("编辑保存参数详情")
    @RequestMapping(value = "/editParamDetails", method = RequestMethod.POST)
    public BaseResp<List<LmParameterDetailsDO>> editParamDetails(@RequestBody List<LmParameterDetailsDO> request) {
       lmParameterDetailsService.saveOrUpdateBatch(request);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }


    @ApiOperation("算法管理ai文本")
    @RequestMapping(value = "/getAiText", method = RequestMethod.GET)
    public BaseResp<String> getAiText() {
        BaseResp<String> baseResp = BaseResp.succResp();
        Date systemDate = this.getSystemDate();
        Date tomorrow = DateUtil.getMoveDay(systemDate, 1); // 预测日为明天

        try {
            // 1. 获取算法库设置信息，统计各路线模型数量
            List<LmAlgorithmLibrarySettingDO> algorithmLibrarySettings = lmAlgorithmLibrarySettingService.findAll();
            int totalModels = 0;
            int largeModelCount = 0;
            int neuralNetworkCount = 0;
            int decisionTreeCount = 0;

            for (LmAlgorithmLibrarySettingDO setting : algorithmLibrarySettings) {
                String[] algorithms = setting.getAlgorithmNames().split(",");
                totalModels += algorithms.length;

                String routeName = setting.getRouteName();
                if (routeName.contains("大模型")) {
                    largeModelCount += algorithms.length;
                } else if (routeName.contains("神经网络")) {
                    neuralNetworkCount += algorithms.length;
                } else if (routeName.contains("决策树")) {
                    decisionTreeCount += algorithms.length;
                }
            }

            // 2. 计算近3天平均预测精度
            Date threeDaysAgo = DateUtil.getMoveDay(systemDate, -3);
            List<LmStatisticsCityDayFcDO> recent3DaysData = lmStatisticsCityDayFcService.find(
                Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, threeDaysAgo, systemDate, null);

            BigDecimal avg3DaysAccuracy = calculateAverageAccuracy(recent3DaysData);

            // 3. 计算近30天平均预测精度
            Date thirtyDaysAgo = DateUtil.getMoveDay(systemDate, -30);
            List<LmStatisticsCityDayFcDO> recent30DaysData = lmStatisticsCityDayFcService.find(
                Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, thirtyDaysAgo, systemDate, null);

            BigDecimal avg30DaysAccuracy = calculateAverageAccuracy(recent30DaysData);

            // 4. 判断预测日的日类型
            String dayType = getDayType(tomorrow);

            // 5. 判断预测日的气候类型
            String weatherType = getWeatherType(tomorrow);

            RedisCacheUtils.put("weatherType",weatherType,7*24*60L);

            // 6. 获取推荐算法（取准确率最高的前5个）
            List<String> recommendedAlgorithms = getRecommendedAlgorithms(recent30DaysData, 5);

            // 7. 构建AI文本
            String text = buildAiText(totalModels, largeModelCount, neuralNetworkCount, decisionTreeCount,
                    avg3DaysAccuracy, avg30DaysAccuracy, dayType, weatherType, recommendedAlgorithms);

            RedisCacheUtils.put("ai_text",text,7*24*60L);
            baseResp.setData(text);

        } catch (Exception e) {
            e.printStackTrace();
            baseResp.setData("生成AI文本时发生错误：" + e.getMessage());
        }

        return baseResp;
    }

    @ApiOperation("算法详情ai文本")
    @RequestMapping(value = "/algorithmDetail/getAiText", method = RequestMethod.GET)
    public BaseResp<List<LmParameterDetailsDO>> getAlgorithmDetailAiText() {
        BaseResp baseResp = BaseResp.succResp();

        String weatherType = RedisCacheUtils.get("weatherType", String.class);
        String aiText = RedisCacheUtils.get("ai_text", String.class);

        String text = "二、算法参数寻优\n\n" +
                "完成算法参数寻优，针对本次预测需求累计优化1项预测模型参数，考虑预测日为"+weatherType+"，增强模型学习率，强化气象特征学习\n" +
                "请确认是否应用配置并进行下一步预测？";
        String result = aiText + "\n\n  \n\n  \n\n......\n\n  \n\n  \n\n" + text;
        baseResp.setData(result);
        return baseResp;
    }





    /**
     * 计算平均准确率
     */
    private BigDecimal calculateAverageAccuracy(List<LmStatisticsCityDayFcDO> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<BigDecimal> accuracyList = dataList.stream()
                .map(LmStatisticsCityDayFcDO::getAccuracy)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return BigDecimalUtils.avgList(accuracyList, 4, false).multiply(new BigDecimal("100"));
    }

    /**
     * 判断日类型：常规日/休息日/节假日
     */
    private String getDayType(Date date) {
        try {
            // 先判断是否为节假日
            List<Date> holidayByYear = holidayService.findHolidayByYear(DateUtil.getYearByDate(date));
            boolean isHoliday = holidayByYear.contains(date);
            if (isHoliday) {
                return "节假日";
            }

            // 再判断是否为周末（休息日）
            if (DateUtil.isWeekend(date)) {
                return "休息日";
            }

            return "常规日";
        } catch (Exception e) {
            e.printStackTrace();
            return "常规日"; // 默认返回常规日
        }
    }

    /**
     * 判断气候类型：高温/累计高温/高温高湿/正常天气
     */
    private String getWeatherType(Date date) {
        try {
            // 获取当日天气数据
            WeatherFeatureCityDayHisDO weatherData = weatherFeatureCityDayHisService.findWeatherFeatureCityHisVOByDate(Constants.PROVINCE_ID, date);
            if (weatherData == null) {
                return "正常天气";
            }

            BigDecimal highestTemp = weatherData.getHighestTemperature();
            BigDecimal rainfall = weatherData.getRainfall();

            if (highestTemp == null) {
                return "正常天气";
            }

            // 判断是否超过35度
            boolean isHighTemp = highestTemp.compareTo(new BigDecimal(35)) > 0;
            boolean hasRain = rainfall != null && rainfall.compareTo(BigDecimal.ZERO) > 0;

            if (isHighTemp) {
                // 判断是否为累计高温（连续三天超过35度）
                if (isConsecutiveHighTemp(date)) {
                    return "累计高温";
                }

                // 判断是否为高温高湿
                if (hasRain) {
                    return "高温高湿";
                }

                return "高温";
            }

            return "正常天气";
        } catch (Exception e) {
            e.printStackTrace();
            return "正常天气";
        }
    }

    /**
     * 判断是否为连续三天高温
     */
    private boolean isConsecutiveHighTemp(Date date) {
        try {
            Date dayBeforeYesterday = DateUtil.getMoveDay(date, -2);

            List<WeatherFeatureCityDayHisDO> weatherDataList = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
                    Constants.PROVINCE_ID, dayBeforeYesterday, date);

            if (weatherDataList.size() < 3) {
                return false;
            }

            // 检查连续三天是否都超过35度
            for (WeatherFeatureCityDayHisDO weatherData : weatherDataList) {
                BigDecimal highestTemp = weatherData.getHighestTemperature();
                if (highestTemp == null || highestTemp.compareTo(new BigDecimal(35)) <= 0) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取推荐算法（按准确率排序，取前几个）
     */
    private List<String> getRecommendedAlgorithms(List<LmStatisticsCityDayFcDO> dataList, int topN) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        // 按算法名称分组，计算平均准确率
        Map<String, List<LmStatisticsCityDayFcDO>> algorithmMap = dataList.stream()
                .filter(data -> data.getAlgorithmName() != null && data.getAccuracy() != null)
                .collect(Collectors.groupingBy(LmStatisticsCityDayFcDO::getAlgorithmName));

        List<Map.Entry<String, BigDecimal>> algorithmAccuracyList = new ArrayList<>();

        for (Map.Entry<String, List<LmStatisticsCityDayFcDO>> entry : algorithmMap.entrySet()) {
            String algorithmName = entry.getKey();
            List<BigDecimal> accuracyList = entry.getValue().stream()
                    .map(LmStatisticsCityDayFcDO::getAccuracy)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            BigDecimal avgAccuracy = BigDecimalUtils.avgList(accuracyList, 2, false);
            algorithmAccuracyList.add(new AbstractMap.SimpleEntry<>(algorithmName, avgAccuracy));
        }

        // 按准确率降序排列，取前n个
        return algorithmAccuracyList.stream()
                .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                .limit(topN)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 构建AI文本
     */
    private String buildAiText(int totalModels, int largeModelCount, int neuralNetworkCount, int decisionTreeCount,
                              BigDecimal avg3DaysAccuracy, BigDecimal avg30DaysAccuracy,
                              String dayType, String weatherType, List<String> recommendedAlgorithms) {

        StringBuilder text = new StringBuilder();
        text.append("## 启动算法预测程序\n\n" +
                " ### 一、应用算法筛选\n\n");

        // 模型数量信息
        text.append("模型总数").append(totalModels).append("个，其中");
        if (largeModelCount > 0) {
            text.append("大模型路线").append(largeModelCount).append("个、");
        }
        if (neuralNetworkCount > 0) {
            text.append("神经网络路线").append(neuralNetworkCount).append("个、");
        }
        if (decisionTreeCount > 0) {
            text.append("决策树路线").append(decisionTreeCount).append("个、");
        }

        // 移除最后一个逗号
        if (text.toString().endsWith("、")) {
            text.setLength(text.length() - 1);
        }

        // 准确率信息
        text.append("，近3天平均预测精度");
        if (avg3DaysAccuracy != null) {
            text.append(avg3DaysAccuracy.setScale(2, RoundingMode.HALF_UP)).append("%");
        } else {
            text.append("--");
        }

        text.append("，近30天平均预测精度");
        if (avg30DaysAccuracy != null) {
            text.append(avg30DaysAccuracy.setScale(2, RoundingMode.HALF_UP)).append("%");
        } else {
            text.append("--");
        }
        text.append("\n");

        // 预测日信息
        text.append("预测日为").append(dayType);
        text.append("+").append(weatherType).append("叠加场景");
        text.append("，根据历史准确率进行综合评估，");

        // 推荐算法
        if (!recommendedAlgorithms.isEmpty()) {
            for (int i = 0; i < recommendedAlgorithms.size(); i++) {
                text.append(recommendedAlgorithms.get(i)).append("模型");
                if (i < recommendedAlgorithms.size() - 1) {
                    text.append("、");
                }
            }
            text.append("应用效果较好，预测精度相对稳定，推荐应用以上算法进行预测。`");
        } else {
            text.append("暂无特别推荐的算法，建议根据实际情况选择合适的算法进行预测。`");
        }

        return text.toString();
    }



    @ApiOperation("单日准确率数据同步")
    @RequestMapping(value = "/doSingleSync", method = RequestMethod.GET)
    public BaseResp doSync(Date startDate, Date endDate) throws Exception {

        List<StatisticsCityDayFcDO> statisticsByDate = statisticsCityDayFcService.findStatisticsByDate(Constants.PROVINCE_ID, null, Constants.CALIBER_ID_BG_QW, startDate, endDate, null);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        Map<String, String> algorothmMap = allAlgorithmsNotCache.stream().collect(Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        List<LmStatisticsCityDayFcDO> statisticsCityDayFcDOList = new ArrayList<>();
        for (StatisticsCityDayFcDO statisticsCityDayFcDO : statisticsByDate) {
            LmStatisticsCityDayFcDO lmStatisticsCityDayFcDO = new LmStatisticsCityDayFcDO();
            BeanUtils.copyProperties(statisticsCityDayFcDO, lmStatisticsCityDayFcDO,"id");
            lmStatisticsCityDayFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            lmStatisticsCityDayFcDO.setAlgorithmName(algorothmMap.get(statisticsCityDayFcDO.getAlgorithmId()));
            statisticsCityDayFcDOList.add(lmStatisticsCityDayFcDO);
        }
        lmStatisticsCityDayFcService.saveOrUpdateBatch(statisticsCityDayFcDOList);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @ApiOperation("15日准确率数据同步")
    @RequestMapping(value = "/do15leSync", method = RequestMethod.GET)
    public BaseResp do15leSync(Date startDate, Date endDate) throws Exception {
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        Date start = DateUtil.getMoveDay(startDate, -15);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        Map<String, String> algorothmMap = allAlgorithmsNotCache.stream().collect(Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        List<StatisticsCityDayFcDO> statisticsByDate = statisticsCityDayFcService.findStatisticsByDate(Constants.PROVINCE_ID, null, Constants.CALIBER_ID_BG_QW, start, endDate, null);
        List<Date> allHolidays = holidayService.getAllHolidays();
        //极端天气日期
        List<java.sql.Date> dates = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(Constants.PROVINCE_ID, start, endDate).stream()
                .filter(t -> Objects.nonNull(t.getHighestTemperature()) && t.getHighestTemperature().compareTo(new BigDecimal(35)) > 0)
                .map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<LmStatisticsCityNear15DayFcDO> saveList = new ArrayList<>();
        for (Date date : listBetweenDay) {
            Date moveDay = DateUtil.getMoveDay(date, -15);
            //正常日
            List<StatisticsCityDayFcDO> statisticsCityDayFcDOList = statisticsByDate.stream().filter(t -> t.getDate().compareTo(moveDay) > 0 && t.getDate().compareTo(date) <= 0).collect(Collectors.toList());
            extracted(date, statisticsCityDayFcDOList, 1, saveList, algorothmMap);

            //休息日
            List<StatisticsCityDayFcDO> weekAcList = statisticsCityDayFcDOList.stream().filter(t -> DateUtil.isWeekend(t.getDate())).collect(Collectors.toList());
            extracted(date, weekAcList, 2, saveList, algorothmMap);

            //节假日
            List<StatisticsCityDayFcDO> holidayAcList = statisticsCityDayFcDOList.stream().filter(t -> allHolidays.contains(t.getDate())).collect(Collectors.toList());
            extracted(date, holidayAcList, 3, saveList, algorothmMap);


            List<StatisticsCityDayFcDO> jdAcList = statisticsCityDayFcDOList.stream().filter(t -> dates.contains(t.getDate())).collect(Collectors.toList());
            extracted(date, jdAcList, 4, saveList, algorothmMap);
        }
        lmStatisticsCityNear15DayFcService.saveOrUpdateBatch(saveList);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    private static void extracted(Date date, List<StatisticsCityDayFcDO> statisticsCityDayFcDOList,
                                  int type, List<LmStatisticsCityNear15DayFcDO> saveList, Map<String, String> algorothmMap) {
        Map<String, List<StatisticsCityDayFcDO>> algorithMap = statisticsCityDayFcDOList.stream().collect(Collectors.groupingBy(StatisticsCityDayFcDO::getAlgorithmId));
        for (Map.Entry<String, List<StatisticsCityDayFcDO>> entry : algorithMap.entrySet()) {
            String algorithmId = entry.getKey();
            List<StatisticsCityDayFcDO> value = entry.getValue();
            List<BigDecimal> accuracyList = value.stream().map(StatisticsCityDayFcDO::getAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
            List<BigDecimal> passList = value.stream().map(StatisticsCityDayFcDO::getPass).filter(Objects::nonNull).collect(Collectors.toList());
            LmStatisticsCityNear15DayFcDO lmStatisticsCityNear15DayFcDO = new LmStatisticsCityNear15DayFcDO();
            lmStatisticsCityNear15DayFcDO.setType(type);
            lmStatisticsCityNear15DayFcDO.setCityId(Constants.PROVINCE_ID);
            lmStatisticsCityNear15DayFcDO.setCaliberId(Constants.CALIBER_ID_BG_QW);
            lmStatisticsCityNear15DayFcDO.setDate(new java.sql.Date(date.getTime()));
            lmStatisticsCityNear15DayFcDO.setAccuracy(BigDecimalUtils.avgList(accuracyList, 4, false));
            lmStatisticsCityNear15DayFcDO.setPass(BigDecimalUtils.avgList(passList, 4, true));
            lmStatisticsCityNear15DayFcDO.setAlgorithmId(algorithmId);
            lmStatisticsCityNear15DayFcDO.setAlgorithmName(algorothmMap.get(algorithmId));
            saveList.add(lmStatisticsCityNear15DayFcDO);
        }
    }
}
