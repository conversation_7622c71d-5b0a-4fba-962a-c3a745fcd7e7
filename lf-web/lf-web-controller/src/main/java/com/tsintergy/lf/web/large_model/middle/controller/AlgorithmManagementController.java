package com.tsintergy.lf.web.large_model.middle.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.large_model.api.*;
import com.tsintergy.lf.serviceapi.large_model.pojo.*;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.large_model.before.response.LoadFeatureResp;
import com.tsintergy.lf.web.large_model.middle.response.AiAlgorithmLibraryResp;
import com.tsintergy.lf.web.large_model.middle.response.Day15Resp;
import com.tsintergy.lf.web.large_model.middle.response.DayResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * 【事中】-算法管理
 * @Date 2025/7/6 09:45
 **/
@RequestMapping("/middle/algorithmManagement")
@RestController
@Api(tags = "【事中】-算法管理")
public class AlgorithmManagementController extends BaseController {

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    private LmStatisticsCityNear15DayFcService lmStatisticsCityNear15DayFcService;

    @Autowired
    private LmStatisticsCityDayFcService lmStatisticsCityDayFcService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LmAlgorithmLibrarySettingService lmAlgorithmLibrarySettingService;

    @Autowired
    private LmModelDetailsService lmModelDetailsService;

    @Autowired
    private LmParameterDetailsService lmParameterDetailsService;

    @Autowired
    LmLargeModelTextSettingService lmLargeModelTextSettingService;



    @ApiOperation("近15天准确率查询")
    @RequestMapping(value = "/getDay15Accuracy", method = RequestMethod.GET)
    public BaseResp<List<Day15Resp>> getDay15Accuracy(Integer type)  {
        List<Day15Resp> result = new ArrayList<>();
        List<LmStatisticsCityNear15DayFcDO> lmStatisticsCityNear15DayFcDOS = lmStatisticsCityNear15DayFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, this.getSystemDate(), type);
        lmStatisticsCityNear15DayFcDOS.forEach(t->{
            Day15Resp day15Resp = new Day15Resp();
            day15Resp.setAlgorithmId(t.getAlgorithmId());
            day15Resp.setAlgorithmName(t.getAlgorithmName());
            day15Resp.setAccuracy(t.getAccuracy());
            day15Resp.setPass(t.getPass());
            result.add(day15Resp);
        });
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("人工智能算法库")
    @RequestMapping(value = "/getAiAlgorithmLibrary", method = RequestMethod.GET)
    public BaseResp<List<AiAlgorithmLibraryResp>> getAiAlgorithmLibrary()  {
        List<LmAlgorithmLibrarySettingDO> all = lmAlgorithmLibrarySettingService.findAll();
        Date moveDay = DateUtil.getMoveDay(this.getSystemDate(), -1);
        List<LmStatisticsCityDayFcDO> lmStatisticsCityDayFcDOS = lmStatisticsCityDayFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, moveDay, moveDay,null);
        Map<String, LmStatisticsCityDayFcDO> accMap = lmStatisticsCityDayFcDOS.stream().collect(Collectors.toMap(LmStatisticsCityDayFcDO::getAlgorithmName, Function.identity()));
        List<AiAlgorithmLibraryResp> result = new ArrayList<>();
        for (LmAlgorithmLibrarySettingDO lmAlgorithmLibrarySettingDO : all) {
            AiAlgorithmLibraryResp aiAlgorithmLibraryResp = new AiAlgorithmLibraryResp();
            aiAlgorithmLibraryResp.setRouteName(lmAlgorithmLibrarySettingDO.getRouteName());
            String[] split = lmAlgorithmLibrarySettingDO.getAlgorithmNames().split(",");
            List<Day15Resp> algorithms = new ArrayList<>();
            aiAlgorithmLibraryResp.setAlgorithm(algorithms);
            for (String algorithmName : split) {
                LmStatisticsCityDayFcDO lmStatisticsCityDayFcDO = accMap.get(algorithmName);
                if(Objects.isNull(lmStatisticsCityDayFcDO)) {continue;}
                Day15Resp day15Resp = new Day15Resp();
                day15Resp.setAlgorithmName(algorithmName);
                day15Resp.setAccuracy(lmStatisticsCityDayFcDO.getAccuracy());
                algorithms.add(day15Resp);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }


    @ApiOperation("单算法准确率查询")
    @RequestMapping(value = "/getSingleAccuracy", method = RequestMethod.GET)
    public BaseResp<List<DayResp>> getSingleAccuracy(String algorithmName) {
        Date systemDate = this.getSystemDate();
        Date startDate = DateUtil.getMoveDay(systemDate, -15);
        List<LmStatisticsCityDayFcDO> lmStatisticsCityDayFcDOS = lmStatisticsCityDayFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, startDate, systemDate,algorithmName);
        List<DayResp> result = new ArrayList<>();
        for (LmStatisticsCityDayFcDO lmStatisticsCityDayFcDO : lmStatisticsCityDayFcDOS) {
            DayResp dayResp = new DayResp();
            dayResp.setAccuracy(lmStatisticsCityDayFcDO.getAccuracy());
            dayResp.setDate(DateUtil.formateDate(lmStatisticsCityDayFcDO.getDate()));
            dayResp.setPass(lmStatisticsCityDayFcDO.getPass());
            result.add(dayResp);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }





    @ApiOperation("算法详情")
    @RequestMapping(value = "/getAlgorithmDetails", method = RequestMethod.GET)
    public BaseResp<LmModelDetailsDO> getAlgorithmDetails(String algorithmName) {
        LmModelDetailsDO result = lmModelDetailsService.findByAlgorithm(algorithmName);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }



    @ApiOperation("参数详情")
    @RequestMapping(value = "/getParamDetails", method = RequestMethod.GET)
    public BaseResp<List<LmParameterDetailsDO>> getParamDetails(String algorithmName) {
        List<LmParameterDetailsDO> lmParameterDetailsDOS = lmParameterDetailsService.findByAlgorithm(algorithmName);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(lmParameterDetailsDOS);
        return baseResp;
    }

    @ApiOperation("编辑保存参数详情")
    @RequestMapping(value = "/editParamDetails", method = RequestMethod.POST)
    public BaseResp<List<LmParameterDetailsDO>> editParamDetails(@RequestBody List<LmParameterDetailsDO> request) {
       lmParameterDetailsService.saveOrUpdateBatch(request);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }


    @ApiOperation("算法管理ai文本")
    @RequestMapping(value = "/aiText", method = RequestMethod.GET)
    public BaseResp<List<LmParameterDetailsDO>> aiText() {
        BaseResp baseResp = BaseResp.succResp();
        String text = "模型总数n个，其中大模型路线n个、神经网络路线n个，决策树路线n个，近3天平均预测精度xx%，近30天平均预测精度xx%\n" +
                "预测日为常规日+高温天气叠加场景，根据历史准确率进行综合评估，xx模型、xx模型、xx模型应用效果较好，预测精度相对稳定，推荐应用以上算法进行预测。";
        Date systemDate = this.getSystemDate();

        =lmStatisticsCityDayFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, DateUtil.getMoveDay(systemDate,-3),systemDate, null);

        LmLargeModelTextSettingDO byField = lmLargeModelTextSettingService.getByField();
        return baseResp;
    }

    @ApiOperation("算法详情ai文本")
    @RequestMapping(value = "/algorithmDetail/aiText", method = RequestMethod.GET)
    public BaseResp<List<LmParameterDetailsDO>> algorithmDetailAiText() {
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }



    @ApiOperation("单日准确率数据同步")
    @RequestMapping(value = "/doSingleSync", method = RequestMethod.GET)
    public BaseResp doSync(Date startDate, Date endDate) throws Exception {

        List<StatisticsCityDayFcDO> statisticsByDate = statisticsCityDayFcService.findStatisticsByDate(Constants.PROVINCE_ID, null, Constants.CALIBER_ID_BG_QW, startDate, endDate, null);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        Map<String, String> algorothmMap = allAlgorithmsNotCache.stream().collect(Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        List<LmStatisticsCityDayFcDO> statisticsCityDayFcDOList = new ArrayList<>();
        for (StatisticsCityDayFcDO statisticsCityDayFcDO : statisticsByDate) {
            LmStatisticsCityDayFcDO lmStatisticsCityDayFcDO = new LmStatisticsCityDayFcDO();
            BeanUtils.copyProperties(statisticsCityDayFcDO, lmStatisticsCityDayFcDO);
            lmStatisticsCityDayFcDO.setAlgorithmName(algorothmMap.get(statisticsCityDayFcDO.getAlgorithmId()));
            statisticsCityDayFcDOList.add(lmStatisticsCityDayFcDO);
        }
        lmStatisticsCityDayFcService.saveOrUpdateBatch(statisticsCityDayFcDOList);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @ApiOperation("15日准确率数据同步")
    @RequestMapping(value = "/do15leSync", method = RequestMethod.GET)
    public BaseResp do15leSync(Date startDate, Date endDate) throws Exception {
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        Date start = DateUtil.getMoveDay(startDate, -15);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        Map<String, String> algorothmMap = allAlgorithmsNotCache.stream().collect(Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        List<StatisticsCityDayFcDO> statisticsByDate = statisticsCityDayFcService.findStatisticsByDate(Constants.PROVINCE_ID, null, Constants.CALIBER_ID_BG_QW, start, endDate, null);
        List<Date> allHolidays = holidayService.getAllHolidays();
        //极端天气日期
        List<java.sql.Date> dates = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(Constants.PROVINCE_ID, start, endDate).stream()
                .filter(t -> Objects.nonNull(t.getHighestTemperature()) && t.getHighestTemperature().compareTo(new BigDecimal(35)) > 0)
                .map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<LmStatisticsCityNear15DayFcDO> saveList = new ArrayList<>();
        for (Date date : listBetweenDay) {
            Date moveDay = DateUtil.getMoveDay(date, -15);
            //正常日
            List<StatisticsCityDayFcDO> statisticsCityDayFcDOList = statisticsByDate.stream().filter(t -> t.getDate().compareTo(moveDay) > 0 && t.getDate().compareTo(date) <= 0).collect(Collectors.toList());
            extracted(date, statisticsCityDayFcDOList, 1, saveList, algorothmMap);

            //休息日
            List<StatisticsCityDayFcDO> weekAcList = statisticsCityDayFcDOList.stream().filter(t -> DateUtil.isWeekend(t.getDate())).collect(Collectors.toList());
            extracted(date, weekAcList, 2, saveList, algorothmMap);

            //节假日
            List<StatisticsCityDayFcDO> holidayAcList = statisticsCityDayFcDOList.stream().filter(t -> allHolidays.contains(t.getDate())).collect(Collectors.toList());
            extracted(date, holidayAcList, 3, saveList, algorothmMap);


            List<StatisticsCityDayFcDO> jdAcList = statisticsCityDayFcDOList.stream().filter(t -> dates.contains(t.getDate())).collect(Collectors.toList());
            extracted(date, jdAcList, 4, saveList, algorothmMap);
        }
        lmStatisticsCityNear15DayFcService.saveOrUpdateBatch(saveList);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    private static void extracted(Date date, List<StatisticsCityDayFcDO> statisticsCityDayFcDOList,
                                  int type, List<LmStatisticsCityNear15DayFcDO> saveList, Map<String, String> algorothmMap) {
        Map<String, List<StatisticsCityDayFcDO>> algorithMap = statisticsCityDayFcDOList.stream().collect(Collectors.groupingBy(StatisticsCityDayFcDO::getAlgorithmId));
        for (Map.Entry<String, List<StatisticsCityDayFcDO>> entry : algorithMap.entrySet()) {
            String algorithmId = entry.getKey();
            List<StatisticsCityDayFcDO> value = entry.getValue();
            List<BigDecimal> accuracyList = value.stream().map(StatisticsCityDayFcDO::getAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
            List<BigDecimal> passList = value.stream().map(StatisticsCityDayFcDO::getPass).filter(Objects::nonNull).collect(Collectors.toList());
            LmStatisticsCityNear15DayFcDO lmStatisticsCityNear15DayFcDO = new LmStatisticsCityNear15DayFcDO();
            lmStatisticsCityNear15DayFcDO.setType(type);
            lmStatisticsCityNear15DayFcDO.setCityId(Constants.PROVINCE_ID);
            lmStatisticsCityNear15DayFcDO.setCaliberId(Constants.CALIBER_ID_BG_QW);
            lmStatisticsCityNear15DayFcDO.setDate(new java.sql.Date(date.getTime()));
            lmStatisticsCityNear15DayFcDO.setAccuracy(BigDecimalUtils.avgList(accuracyList, 4, false));
            lmStatisticsCityNear15DayFcDO.setPass(BigDecimalUtils.avgList(passList, 4, false));
            lmStatisticsCityNear15DayFcDO.setAlgorithmId(algorithmId);
            lmStatisticsCityNear15DayFcDO.setAlgorithmName(algorothmMap.get(algorithmId));
            saveList.add(lmStatisticsCityNear15DayFcDO);
        }
    }

}
