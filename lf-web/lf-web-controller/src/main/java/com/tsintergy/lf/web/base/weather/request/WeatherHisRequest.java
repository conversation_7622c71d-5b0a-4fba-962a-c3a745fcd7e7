package com.tsintergy.lf.web.base.weather.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;

/**
 * @date: 3/1/18 2:26 PM
 * @author: angel
 **/
public class WeatherHisRequest{

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @JsonFormat(pattern = "yyyy-MM-dd" ,timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd" ,timezone = "GMT+8")
    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    /**
     * 气象类型 1=湿度,2=温度,3=降雨量,4=风速
     */
    @NotNull(message = "气象类型不能为空")
    @ApiModelProperty(value = "类型")
    private Integer type;

    private String weatherSource;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getWeatherSource() {
        return weatherSource;
    }

    public void setWeatherSource(String weatherSource) {
        this.weatherSource = weatherSource;
    }
}
