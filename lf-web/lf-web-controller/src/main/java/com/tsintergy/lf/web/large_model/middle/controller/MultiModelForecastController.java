package com.tsintergy.lf.web.large_model.middle.controller;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsieframework.core.component.cache.service.RedisCacheUtils;
import com.tsieframework.core.component.cache.service.RedisService;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.large_model.api.LmLoadCityFcService;
import com.tsintergy.lf.serviceapi.large_model.api.LmLoadFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLoadCityFcDO;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLoadFeatureCityDayFcServiceDO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.large_model.middle.response.FeatureResp;
import com.tsintergy.lf.web.large_model.middle.response.ForecastResultsResp;
import com.tsintergy.lf.web.large_model.middle.response.MultiModelForeResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/7 13:28
 **/
@RequestMapping("/middle/multiModelForecast")
@RestController
@Api(tags = "【事中】-多模型预测")
public class MultiModelForecastController extends BaseController {


    @Autowired
    private LmLoadCityFcService lmLoadCityFcService;

    @Autowired
    private LmLoadFeatureCityDayFcService lmLoadFeatureCityDayFcService;

    private static final String cityId = Constants.PROVINCE_ID;

    private static final String caliberId = Constants.CALIBER_ID_BG_QW;


    @PostMapping("/getForecastResults")
    @ApiOperation("获取预测结果")
    public BaseResp<MultiModelForeResp> getForecastResults(@RequestBody List<String> algorithmNames) {
        Date systemDate = this.getSystemDate();
        Date foreDate = DateUtil.getMoveDay(systemDate, 1);
        MultiModelForeResp multiModelForeResp = new MultiModelForeResp();
        List<String> algorithms = Arrays.asList("子网累加", "清软决策树");
        algorithmNames.addAll(algorithms);
        List<LmLoadCityFcDO> lmLoadCityFcDOS = lmLoadCityFcService.find(cityId, caliberId, null, foreDate, foreDate);

        List<LmLoadFeatureCityDayFcServiceDO> lmLoadFeatureCityDayFcServiceDOS = lmLoadFeatureCityDayFcService.find(null, foreDate, foreDate);
        Map<String, LmLoadFeatureCityDayFcServiceDO> featureMap = lmLoadFeatureCityDayFcServiceDOS.stream().collect(Collectors.toMap(LmLoadFeatureCityDayFcServiceDO::getAlgorithmId, Function.identity()));
        List<ForecastResultsResp> forecastResultsResps = new ArrayList<>();
        List<FeatureResp> featureResps = new ArrayList<>();
        multiModelForeResp.setForecast(forecastResultsResps);
        multiModelForeResp.setFeature(featureResps);
        for (LmLoadCityFcDO lmLoadCityFcDO : lmLoadCityFcDOS) {
            String algorithmId = lmLoadCityFcDO.getAlgorithmId();
            if (algorithmNames.contains(algorithmId)) {
                ForecastResultsResp forecastResultsResp = new ForecastResultsResp();
                forecastResultsResp.setAlgorithmName(algorithmId);
                forecastResultsResp.setLoad(lmLoadCityFcDO.getloadList());
                forecastResultsResps.add(forecastResultsResp);
                FeatureResp featureResp = new FeatureResp();
                featureResps.add(featureResp);
                featureResp.setAlgorithmName(algorithmId);
                featureResp.setDate(lmLoadCityFcDO.getDate());
                LmLoadFeatureCityDayFcServiceDO lmLoadFeatureCityDayFcServiceDO = featureMap.get(algorithmId);
                if (Objects.nonNull(lmLoadFeatureCityDayFcServiceDO)) {
                    featureResp.setMaxLoad(lmLoadFeatureCityDayFcServiceDO.getMaxLoad());
                    featureResp.setBgAveLoad(lmLoadFeatureCityDayFcServiceDO.getBgAveLoad());
                    featureResp.setNoonMin(lmLoadFeatureCityDayFcServiceDO.getNoonMin());
                    featureResp.setEveningMin(lmLoadFeatureCityDayFcServiceDO.getEveningMin());
                    featureResp.setBgRate(lmLoadFeatureCityDayFcServiceDO.getBgRate());
                    featureResp.setMaxLoadIndex(lmLoadFeatureCityDayFcServiceDO.getMaxIndex());
                }
            }
        }
        RedisCacheUtils.put("forecastResult",  multiModelForeResp,600L);
        return BaseRespBuilder.success().setData(multiModelForeResp, MultiModelForeResp.class).build();
    }

    @GetMapping("/getAiText")
    @ApiOperation("获取ai文本")
    public BaseResp<String> getAiText() {
        MultiModelForeResp multiModelForeResp = RedisCacheUtils.get("forecastResult", MultiModelForeResp.class);
        List<FeatureResp> feature = multiModelForeResp.getFeature();

        int bg = 0;
        int evening = 0;
        int morning = 0;
        int noon = 0;
        List<Integer> indexList = feature.stream().map(FeatureResp::getMaxLoadIndex).collect(Collectors.toList());
        for (Integer i : indexList) {
            if (i>=DateUtil.getTimePoint("1745", 96)-1 && i<=DateUtil.getTimePoint("2115", 96)-1){
                bg++;
            }
            if (i>=DateUtil.getTimePoint("0015", 96)-1 && i<=DateUtil.getTimePoint("0600", 96)-1){
                evening++;
            }
            if (i>=DateUtil.getTimePoint("1015", 96)-1 && i<=DateUtil.getTimePoint("1600", 96)-1){
                noon++;
            }
            if (i>=DateUtil.getTimePoint("0615", 96)-1 && i<=DateUtil.getTimePoint("1015", 96)-1){
                morning++;
            }
        }
        Map<String, Integer> timeMap = new HashMap<>();
        timeMap.put("保供时段", bg);
        timeMap.put("晚间", evening);
        timeMap.put("早间", morning);
        timeMap.put("午间", noon);
        Optional<Map.Entry<String, Integer>> min = timeMap.entrySet().stream().min(Map.Entry.comparingByValue(Comparator.reverseOrder()));

        List<BigDecimal> maxLoad = feature.stream().map(FeatureResp::getMaxLoad).collect(Collectors.toList());
        String text = getAiText(maxLoad, min);
        return BaseRespBuilder.success().setData(text, String.class).build();
    }


    private static String getAiText(List<BigDecimal> maxLoad, Optional<Map.Entry<String, Integer>> min) {
        StringBuilder maxLoadStr = new StringBuilder();
        for (int i = 0; i < maxLoad.size(); i++) {
            maxLoadStr.append(maxLoad.get(i));
            if (i != maxLoad.size()-1){
                maxLoadStr.append("、");
            }
        }

        return "预测完成，累计用时2分钟30秒，预测最大负荷分别为"+maxLoadStr+"\n\n" +
                "多模型预测结果显示最大负荷大概率发生在"+(min.isPresent()? min.get().getKey():"--")+"，" +
                "负荷曲线呈双峰形态，自6:00快速攀升，"+(min.map(stringIntegerEntry -> (stringIntegerEntry.getKey().equals("保供时段") ?
                "午间形成波峰后波动下降，保供时段再次增长至顶峰后持续下滑，夜间降至谷底\n\n" : "午间达到顶峰后波动下降，保供时段再次短暂增长后持续下滑，夜间降至谷底\n\n")).orElse("--")) +
                "下一步：点击下一步后进入下一个页面，ai文本显示“开展模型融合方案配置”";
    }

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    AlgorithmService algorithmService;

    @GetMapping("/doFcLoadSync")
    public BaseResp doFcLoadSync(Date startDate, Date endDate) throws Exception {
        List<LoadCityFcDO> fcByAlgorithmId = loadCityFcService.findFcByAlgorithmId(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, null, startDate, endDate);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<LmLoadCityFcDO> list = new ArrayList<>();
        Map<String, String> algorothmMap = allAlgorithmsNotCache.stream().collect(Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        for (LoadCityFcDO loadCityFcDO : fcByAlgorithmId) {
            LmLoadCityFcDO lmLoadCityFcDO = new LmLoadCityFcDO();
            BeanUtils.copyProperties(loadCityFcDO, lmLoadCityFcDO);
            lmLoadCityFcDO.setId(null);
            lmLoadCityFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            if ("23".equals(loadCityFcDO.getAlgorithmId())) {
                lmLoadCityFcDO.setAlgorithmId("清软决策树");
            } else if ("17".equals(loadCityFcDO.getAlgorithmId())) {
                lmLoadCityFcDO.setAlgorithmId("子网累加");
            } else {
                lmLoadCityFcDO.setAlgorithmId(algorothmMap.get(loadCityFcDO.getAlgorithmId()));
            }
            list.add(lmLoadCityFcDO);
        }
        lmLoadCityFcService.saveOrUpdateList(list);
        return BaseResp.succResp();
    }

    @GetMapping("/doFcFeatureSync")
    public BaseResp doFcFeatureSync(Date startDate, Date endDate) {
        List<LmLoadCityFcDO> fcByAlgorithmId = lmLoadCityFcService.find(Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, null, startDate, endDate);
        List<LmLoadFeatureCityDayFcServiceDO> list = new ArrayList<>();
        //保供时段17:45-21:15. 午间10:15 - 16:00 夜间0015-0600
        for (LmLoadCityFcDO loadCityFcDO : fcByAlgorithmId) {
            LmLoadFeatureCityDayFcServiceDO lmLoadFeatureCityDayFcServiceDO = new LmLoadFeatureCityDayFcServiceDO();
            list.add(lmLoadFeatureCityDayFcServiceDO);
            lmLoadFeatureCityDayFcServiceDO.setDate(loadCityFcDO.getDate());
            lmLoadFeatureCityDayFcServiceDO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
            List<BigDecimal> loadList = loadCityFcDO.getloadList();

            clcaMaxMin(loadCityFcDO, loadList, lmLoadFeatureCityDayFcServiceDO);

            List<BigDecimal> noonLoad = loadList.subList(DateUtil.getTimePoint("1015", 96) - 1, DateUtil.getTimePoint("1600", 96));
            noonLoad.stream().min(BigDecimal::compareTo).ifPresent(lmLoadFeatureCityDayFcServiceDO::setNoonMin);

            List<BigDecimal> eveningLoad = loadList.subList(DateUtil.getTimePoint("0015", 96) - 1, DateUtil.getTimePoint("0600", 96));
            eveningLoad.stream().min(BigDecimal::compareTo).ifPresent(lmLoadFeatureCityDayFcServiceDO::setEveningMin);

            //计算保供时段特性
            clcaBg(loadList, lmLoadFeatureCityDayFcServiceDO);

        }
        lmLoadFeatureCityDayFcService.saveOrUpdateBatch(list);
        return BaseResp.succResp();
    }

    private static void clcaBg(List<BigDecimal> loadList, LmLoadFeatureCityDayFcServiceDO lmLoadFeatureCityDayFcServiceDO) {
        List<BigDecimal> bgLoad = loadList.subList(DateUtil.getTimePoint("1745", 96) - 1, DateUtil.getTimePoint("2115", 96));
        lmLoadFeatureCityDayFcServiceDO.setBgAveLoad(BigDecimalUtils.listAvg(bgLoad));
        //计算保供时段爬坡率
        List<BigDecimal> climbing = new ArrayList<>();
        Integer bgMaxIndex = null;
        BigDecimal zero = BigDecimal.ZERO;
        for (int i = 1; i < bgLoad.size(); i++) {
            BigDecimal val1 = bgLoad.get(i - 1);
            BigDecimal val2 = bgLoad.get(i);
            if (Objects.nonNull(val1) && Objects.nonNull(val2)) {
                BigDecimal divide = (val2.subtract(val1)).divide(val1, RoundingMode.HALF_UP);
                if (divide.compareTo(zero) > 0) {
                    zero = divide;
                    bgMaxIndex = i + DateUtil.getTimePoint("1745", 96);
                }
                climbing.add(divide);
            }
        }
        lmLoadFeatureCityDayFcServiceDO.setBgMaxIndex(bgMaxIndex);
        List<BigDecimal> decimals = climbing.stream().filter(t -> t.compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(decimals)) {
            lmLoadFeatureCityDayFcServiceDO.setBgRate(BigDecimalUtils.listAvg(decimals));
        }
    }

    private static void clcaMaxMin(LmLoadCityFcDO loadCityFcDO, List<BigDecimal> loadList, LmLoadFeatureCityDayFcServiceDO lmLoadFeatureCityDayFcServiceDO) {
        Map<String, BigDecimal> loadMap = BasePeriodUtils.toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);
        String maxTime = null; // 最大负荷发生时刻
        String minTime = null; // 最小负荷发生时刻
        Integer maxIndex = null;
        Integer minIndex = null;
        List<String> sortList = loadMap.keySet().stream().sorted().collect(Collectors.toList());
        for (int i = 0; i < sortList.size(); i++) {
            String column = sortList.get(i);
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                    maxIndex = i;
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                    minIndex = i;
                }
            }
        }
        lmLoadFeatureCityDayFcServiceDO.setMaxLoad(maxMixAvg.get("max"));
        lmLoadFeatureCityDayFcServiceDO.setMinLoad(maxMixAvg.get("min"));
        lmLoadFeatureCityDayFcServiceDO.setMaxTime(maxTime);
        lmLoadFeatureCityDayFcServiceDO.setMinTime(minTime);
        lmLoadFeatureCityDayFcServiceDO.setMaxIndex(maxIndex);
        lmLoadFeatureCityDayFcServiceDO.setMinIndex(minIndex);
        if (Objects.nonNull(lmLoadFeatureCityDayFcServiceDO.getMaxLoad()) && Objects.nonNull(lmLoadFeatureCityDayFcServiceDO.getMinLoad())) {
            lmLoadFeatureCityDayFcServiceDO.setDifferent(lmLoadFeatureCityDayFcServiceDO.getMaxLoad().subtract(lmLoadFeatureCityDayFcServiceDO.getMinLoad()));
        }
    }
}
