package com.tsintergy.lf.web.large_model.before.controller;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.FeatureRowEnum;
import com.tsintergy.lf.core.enums.LoadFeatureEnum;
import com.tsintergy.lf.core.enums.WeatherColumnEnum;
import com.tsintergy.lf.core.enums.WeatherFeatureEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.typhoon.api.ExtremeWeatherService;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.MaxMinCorrelationDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonCorrelationDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonScatterDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonScatterRequestv2DTO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceapi.large_model.api.LmLargeModelTextSettingService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLargeModelTextSettingDO;
import com.tsintergy.lf.serviceimpl.common.util.WeatherCalcUtil;
import com.tsintergy.lf.web.base.load.controller.BaseLoadFeatureController;
import com.tsintergy.lf.web.base.load.request.LoadAnalyseRequest;
import com.tsintergy.lf.web.base.load.response.FeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.FeatureStatisticsResp;
import com.tsintergy.lf.web.base.load.response.LoadWeatherHisResp;
import com.tsintergy.lf.web.base.typhoon.request.TyphoonScatterRequest;
import com.tsintergy.lf.web.base.typhoon.request.TyphoonScatterRequestv2;
import com.tsintergy.lf.web.base.typhoon.response.CorrelationDTO;
import com.tsintergy.lf.web.large_model.before.response.LoadFeatureResp;
import com.tsintergy.lf.web.large_model.before.response.VolatilityResp;
import com.tsintergy.lf.web.large_model.before.response.WeatherAccuracyResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *
 * @description: 【事前】-时序负荷
 * <AUTHOR>
 * @date 2025/07/05 11:14
 * @version: 1.0
 */ 
@RequestMapping("/before/day")
@RestController
@Api(tags = "日度事前控制器")
public class BeforeDayController extends BaseLoadFeatureController {

    private Logger logger = LogManager.getLogger(BeforeDayController.class);

    @Autowired
    private LoadCityHisService loadCityHisService;
    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;
    @Autowired
    private WeatherCityHisService weatherCityHisService;
    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;
    @Autowired
    CityService cityService;
    @Autowired
    LmLargeModelTextSettingService lmLargeModelTextSettingService;
    @Autowired
    WeatherCityFcService weatherCityFcService;
    @Autowired
    WeatherCityFcMeteoService weatherCityFcMeteoService;
    @Autowired
    WeatherCityFcBmService weatherCityFcBmService;
    @Autowired
    private ExtremeWeatherService extremeWeatherService;

    /**
     * what: 特性曲线
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("日历史曲线特性")
    @RequestMapping(value = "/hisLoadFeature", method = RequestMethod.GET)
    public BaseResp<LoadFeatureResp> getHisPowerAndRain() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        Date date  = DateUtils.addDays(systemDate, -1);
        List<BigDecimal> powerList = loadCityHisService
                .findLoadCityHisDO(cityId, date, date, caliberId);
        LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(cityId, date, date, caliberId).get(0);
        LoadFeatureResp result = new LoadFeatureResp();
        BeanUtils.copyProperties(loadFeatureCityDayHisDO, result);
        result.setLoadList(powerList);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }


    /**
     * what: 负荷k线图
     *
     * <AUTHOR>
     * @date 2025/07/05 11:16
     */
    @RequestMapping(value = "/power_K", method = RequestMethod.GET)
    @ApiOperation("负荷k线图")
    public BaseResp<List<FeatureAnalyseResp>> getDayLoadKChart() throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        Date endDate  = DateUtils.addDays(systemDate, -1);
        Date startDate = DateUtils.addDays(endDate, -15);
        String caliberId = this.getCaliberId();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        //日特性
        List<LoadFeatureCityDayHisDO> powerFeatureCityDayHisStatDOS = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
            .listWeatherFeatureCityDayHisDO(weatherCityId, startDate, endDate);
        if (CollectionUtils.isEmpty(powerFeatureCityDayHisStatDOS)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //将气象的日期和降雨量放到map中
        Map<String, BigDecimal> rainfallMap = new HashMap<>();
        //将气象的日期和最高温放到map中
        Map<String, BigDecimal> highestTemperatureMap = new HashMap<>();
        if (weatherFeatureCityDayHisStatDOS != null && weatherFeatureCityDayHisStatDOS.size() > 0) {
            Map<Date, BigDecimal> weatherFeature = weatherFeatureCityDayHisStatDOS.stream().collect(
                Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, weather -> weather.getRainfall() == null ? BigDecimal.ZERO : weather.getRainfall()));
            weatherFeature.forEach((k, v) -> {
                rainfallMap.put(DateUtils.date2String(k, DateFormatType.DATE_FORMAT_STR), v);
            });
            Map<Date, BigDecimal> highestTemperatureDateMap = weatherFeatureCityDayHisStatDOS.stream().collect(
                Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, weatherFeatureCityDayHisDO -> weatherFeatureCityDayHisDO.getHighestTemperature()== null ? new BigDecimal(0):weatherFeatureCityDayHisDO.getHighestTemperature()));
            highestTemperatureDateMap.forEach((k, v) -> {
                highestTemperatureMap.put(DateUtils.date2String(k, DateFormatType.DATE_FORMAT_STR), v);
            });
        }

        Map<Date, List<LoadFeatureCityDayHisDO>> mapByDate = powerFeatureCityDayHisStatDOS.stream()
            .collect(Collectors.groupingBy(dp -> new Date(dp.getDate().getTime())));

        //构建vo
        List<FeatureAnalyseResp> resultList = new ArrayList<>();
        if (powerFeatureCityDayHisStatDOS != null && powerFeatureCityDayHisStatDOS.size() > 0) {
            powerFeatureCityDayHisStatDOS.forEach(powerFeatureCityDayHisStatDO -> {
                FeatureAnalyseResp resp = new FeatureAnalyseResp();
                BeanUtils.copyProperties(powerFeatureCityDayHisStatDO, resp);
                //获取前三天的出力数据
                List<LoadFeatureCityDayHisDO> firstData = mapByDate.get(resp.getDate());
                List<LoadFeatureCityDayHisDO> secondData = mapByDate.get(DateUtil.getMoveDay(resp.getDate(), -1));
                List<LoadFeatureCityDayHisDO> thirdData = mapByDate.get(DateUtil.getMoveDay(resp.getDate(), -2));
                if (firstData != null && secondData != null && thirdData != null) {
                    BigDecimal add = BigDecimalUtils
                        .add(firstData.get(0).getAveLoad(), secondData.get(0).getAveLoad());
                    BigDecimal add1 = BigDecimalUtils
                        .add(add, thirdData.get(0).getAveLoad());
                    BigDecimal divide = BigDecimalUtils
                        .divide(add1, new BigDecimal(3), 4);
                    resp.setAvgPeriod(divide);
                }
                resp.setAvgLoad(powerFeatureCityDayHisStatDO.getAveLoad());
                resp.setRain(rainfallMap.get(
                    DateUtils.date2String(powerFeatureCityDayHisStatDO.getDate(), DateFormatType.DATE_FORMAT_STR)));
                resp.setHighestTemperature(highestTemperatureMap.get(
                    DateUtils.date2String(powerFeatureCityDayHisStatDO.getDate(), DateFormatType.DATE_FORMAT_STR)));

                resultList.add(resp);
            });
        }

        if (resultList.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resultList);
        return baseResp;
    }



    /**
     * what: 指标分析
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("指标分析")
    @RequestMapping(value = "/volatility", method = RequestMethod.GET)
    public BaseResp<VolatilityResp> volatility() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        VolatilityResp result = new VolatilityResp();
        result.setMaxLoadVolatility(this.generateRandomBigDecimal(0.5));
        result.setMinLoadVolatility(this.generateRandomBigDecimal(0.5));
        result.setAvgLoadVolatility(this.generateRandomBigDecimal(0.2));
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    public  BigDecimal generateRandomBigDecimal(double bound) {
        // 生成[0, 0.5)之间的随机double
        double randomValue = ThreadLocalRandom.current().nextDouble(0.0, bound);
        // 转换为BigDecimal并保留15位小数（可根据需求调整）
        return BigDecimal.valueOf(randomValue).setScale(2, RoundingMode.HALF_UP);
    }


    /**
     * what: 调度预测AI大模型
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("调度预测AI大模型")
    @RequestMapping(value = "/loadText", method = RequestMethod.GET)
    public BaseResp<String> loadText() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        LmLargeModelTextSettingDO beforeLoad = lmLargeModelTextSettingService.getByField("before_load");
        String s = "1.对历进行识别，剔除X年X月X日、X年X月X日异常限电日期，选用2020年1月1日至2025年6月30日负荷作为历史数据集。\n\n" +
                "2、昨日最大负荷xxMW、出现于xx时刻，最小负荷xxMW，出现于xx时刻，负荷预测建议重点近期数据变化，在近期负荷变化的基础上叠加其他重要指标进行预测；\n\n" +
                "3、近15日最大负荷波动性较强，达到xx%，其中最大负荷出现于午间xx-xx时刻，建议预测重点考虑该时段负荷拐点信息，并充分考虑近期负荷波动，叠加波动趋势进行预测。";
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(s);
        return baseResp;
    }

    /**
     * what: 气象筛选
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("气象筛选")
    @RequestMapping(value = "/weather/accuracy", method = RequestMethod.GET)
    public BaseResp<List<WeatherAccuracyResp>> getWeatherAccuracy() throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        Date endDate  = DateUtils.addDays(systemDate, -1);
        Date startDate = DateUtils.addDays(endDate, -30);
        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService.findWeatherCityFcDOs(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<String, List<BigDecimal>> weatherCityFcDOMap = weatherCityFcDOs.stream().collect(Collectors.toMap(x -> com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(x.getDate(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR), x -> x.getWeatherList()));
        List<WeatherCityFcMeteoDO> weatherCityFcMeteoDOS = weatherCityFcMeteoService.getListByCondition(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<String, List<BigDecimal>> weatherCityFcMeteoDOMap = weatherCityFcMeteoDOS.stream().collect(Collectors.toMap(x -> com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(x.getDate(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR), x -> x.getWeatherList()));
        List<WeatherCityFcBmDO> weatherCityFcBmDOS = weatherCityFcBmService.getListByCondition(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<String, List<BigDecimal>> weatherCityFcBmDOMap = weatherCityFcBmDOS.stream().collect(Collectors.toMap(x -> com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(x.getDate(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR), x -> x.getWeatherList()));
        List<BigDecimal> accuracy = new ArrayList<>();
        List<BigDecimal> accuracyMeteo = new ArrayList<>();
        List<BigDecimal> accuracyBm = new ArrayList<>();
        for (WeatherCityHisDO weatherCityHisDO : weatherCityHisDOs) {
            java.sql.Date date = weatherCityHisDO.getDate();
            String dateStr = com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            List<BigDecimal> hisWeatherList = weatherCityHisDO.getWeatherList();
            List<BigDecimal> fcWeatherList = weatherCityFcDOMap.get(dateStr);
            List<BigDecimal> fcMeteoWeatherList = weatherCityFcMeteoDOMap.get(dateStr);
            List<BigDecimal> fcBmWeatherList = weatherCityFcBmDOMap.get(dateStr);
            List<BigDecimal> accuracyList = WeatherCalcUtil.calcAccuracy(hisWeatherList, fcWeatherList);
            List<BigDecimal> accuracyMeteoList = WeatherCalcUtil.calcAccuracy(hisWeatherList, fcMeteoWeatherList);
            List<BigDecimal> accuracyBmList = WeatherCalcUtil.calcAccuracy(hisWeatherList, fcBmWeatherList);
            accuracy.add( CollectionUtils.isEmpty(accuracyList) ? null : BigDecimalFunctions.listAvg(accuracyList));
            accuracyMeteo.add( CollectionUtils.isEmpty(accuracyMeteoList) ? null : BigDecimalFunctions.listAvg(accuracyMeteoList));
            accuracyBm.add(CollectionUtils.isEmpty(accuracyBmList) ? null : BigDecimalFunctions.listAvg(accuracyBmList));
        }
        BigDecimal accuracyAvg = BigDecimalFunctions.listAvg(accuracy);
        BigDecimal accuracyMeteoAvg = BigDecimalFunctions.listAvg(accuracyMeteo);
        BigDecimal accuracyBmAvg = BigDecimalFunctions.listAvg(accuracyBm);

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(this.getSortedWeatherAccuracy(accuracyAvg, accuracyMeteoAvg, accuracyBmAvg));
        return baseResp;
    }

    public List<WeatherAccuracyResp> getSortedWeatherAccuracy(
            BigDecimal accuracyAvg,       // 气象局
            BigDecimal accuracyMeteoAvg,  // EC
            BigDecimal accuracyBmAvg) {   // BM

        // 1. 创建气象源数据列表
        List<WeatherAccuracyResp> accuracyList = new ArrayList<>();
        accuracyList.add(new WeatherAccuracyResp("气象局", accuracyAvg));
        accuracyList.add(new WeatherAccuracyResp("EC", accuracyMeteoAvg));
        accuracyList.add(new WeatherAccuracyResp("BM", accuracyBmAvg));

        // 2. 按准确率降序排序（高到低）
        accuracyList.sort(Comparator.comparing(WeatherAccuracyResp::getWeatherAccuracy).reversed());

        return accuracyList;
    }

    /**
     * what: 气象曲线
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("气象曲线")
    @RequestMapping(value = "/weather/list", method = RequestMethod.GET)
    public BaseResp<List<BigDecimal>> weatherList(@ApiParam(value = "类型：1-历史 2-预测")Integer type) throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        Date hisDate  = DateUtils.addDays(systemDate, -1);
        Date fcDate = DateUtils.addDays(systemDate, 1);
        List<BigDecimal> weatherCityValueList = null;
        if (type==1){
            weatherCityValueList = weatherCityHisService.findWeatherCityHisValueList(cityId, hisDate, WeatherEnum.TEMPERATURE.getType());
        }else if (type==2){
            weatherCityValueList = weatherCityFcService.find96WeatherCityFcValue(fcDate,cityId, WeatherEnum.TEMPERATURE.getType());
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(weatherCityValueList);
        return baseResp;
    }

    @RequestMapping(value = "/weather/correlation", method = RequestMethod.GET)
    @ApiOperation("相关系数分析")
    public BaseResp<CorrelationDTO> getTyphoonCorrelationV2() throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        String caliberId = Constants.CALIBER_ID_BG_QW;
        Date startDate  = DateUtils.addDays(systemDate, -30);
        Date endDate = DateUtils.addDays(systemDate, -1);
        BaseResp resp = BaseResp.succResp();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisService.findWeatherFeatureBySearchData(weatherCityId, startDate, endDate,null);
        if(CollectionUtils.isEmpty(weatherFeatureCityDayHisVOS)){
            resp.setData(null);
            resp.setRetCode("T706");
            resp.setRetMsg("查询气象特性数据为空");
            return resp;
        }
        List<Date> dateList = weatherFeatureCityDayHisVOS.stream().map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<TyphoonCorrelationDTO> result = extremeWeatherService.getTyphoonCorrelation(dateList, cityId, caliberId);
        List<Integer> columns = Arrays.asList(0,1,2,3,7);
        List<Integer> rows = Arrays.asList(0,1,2);

        result = result.stream().filter(t -> columns.contains(t.getY()) && rows.contains(t.getX())).collect(Collectors.toList());

        CorrelationDTO correlationDTO = new CorrelationDTO();
        correlationDTO.setResult(result);
        result.sort(Comparator.comparing(TyphoonCorrelationDTO::getV, Comparator.nullsFirst(Comparator.reverseOrder())));
        MaxMinCorrelationDTO maxMinCorrelationDTO = new MaxMinCorrelationDTO();
        if(CollectionUtils.isNotEmpty(result)){
            TyphoonCorrelationDTO typhoonCorrelationDTO = result.get(0);
            maxMinCorrelationDTO.setPositiveMax(LoadFeatureEnum.findNameByType(typhoonCorrelationDTO.getX()) + "-" + WeatherFeatureEnum.findNameByType(typhoonCorrelationDTO.getY()));
            maxMinCorrelationDTO.setMaxCoefficient(typhoonCorrelationDTO.getV());
        }

        //最大负相关
        List<TyphoonCorrelationDTO> maxMIn = result.stream().filter(t -> t.getV().compareTo(new BigDecimal(0)) <0).collect(Collectors.toList());
        maxMIn.sort(Comparator.comparing(TyphoonCorrelationDTO::getV, Comparator.nullsFirst(Comparator.naturalOrder())));
        if(CollectionUtils.isNotEmpty(maxMIn)){
            TyphoonCorrelationDTO correlationDTO1 = maxMIn.get(0);
            maxMinCorrelationDTO.setPositiveMinus(LoadFeatureEnum.findNameByType(correlationDTO1.getX()) + "-" + WeatherFeatureEnum.findNameByType(correlationDTO1.getY()));
            maxMinCorrelationDTO.setMinCoefficient(correlationDTO1.getV());
        }
        correlationDTO.setCorrelationDTO(maxMinCorrelationDTO);
        resp.setData(correlationDTO);
        return resp;
    }

    @ApiOperation("气象散点分析")
    @RequestMapping(value = "/weather/weatherScatter", method = RequestMethod.GET)
    public BaseResp<List<TyphoonScatterDTO>> getTyphoonWeatherScatter() throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        String caliberId = Constants.CALIBER_ID_BG_QW;
        Date startDate  = DateUtils.addDays(systemDate, -30);
        Date endDate = DateUtils.addDays(systemDate, -1);
        String columnName = WeatherColumnEnum.HIGHESTTEMPERATURE.getValue();
        String rowName = FeatureRowEnum.MAXLOAD.getValue();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisService.findWeatherFeatureBySearchData(weatherCityId, startDate,endDate, null);
        List<Date> dateList = weatherFeatureCityDayHisVOS.stream().map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<TyphoonScatterDTO> results = extremeWeatherService.getTyphoonWeatherScatter(dateList,
                rowName,
                columnName,
                cityId,
                caliberId);
        BaseResp resp = BaseResp.succResp();
        resp.setData(results);
        return resp;
    }

    @ApiOperation("调度预测AI大模型-二、气象数据")
    @RequestMapping(value = "/WeatherText", method = RequestMethod.GET)
    public BaseResp<String> weatherText() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        LmLargeModelTextSettingDO weatherLoad = lmLargeModelTextSettingService.getByField("weather_load");
        BaseResp baseResp = BaseResp.succResp();
        String s ="1、评估近期不同气象源预报准确率，并进行分析，近7天BM预报效果较好，建议选用BM气象源作为模型输入。\n\n" +
                "2、对多类气象指标与负荷进行关联分析，其中最高气温、平均气温、湿度等指标与负荷变化关联性强，需要在预测模型中重点选用此类气象指标。\n\n" +
                "3、昨日最高温度xx℃，预测日预报最高温度为xx℃，且近3日气温已连续超过35℃，建议重点考虑连续高温气候影响，选用特征捕捉更为复杂的的GT、ANN等模型。\n\n" +
                "4.预测日预报气温为36℃，较前一日升高2摄氏度。体感温度为42度，较前一日升高7度。体感温度上升幅度源大于气温，建议增加体感温度作为模型输入。\n\n" +
                "5、根据回归拟合分析，最高温度由xx℃升至xx℃时，最大负荷平均增长xxMW，建议考虑该增长情况进行预测。";
        baseResp.setData(s);
        return baseResp;
    }

}
