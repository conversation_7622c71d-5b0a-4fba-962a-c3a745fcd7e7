package com.tsintergy.lf.web.large_model.before.controller;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.FeatureRowEnum;
import com.tsintergy.lf.core.enums.LoadFeatureEnum;
import com.tsintergy.lf.core.enums.WeatherColumnEnum;
import com.tsintergy.lf.core.enums.WeatherFeatureEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.typhoon.api.ExtremeWeatherService;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.MaxMinCorrelationDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonCorrelationDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonScatterDTO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceapi.large_model.api.LmLargeModelTextSettingService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLargeModelTextSettingDO;
import com.tsintergy.lf.serviceimpl.common.util.WeatherCalcUtil;
import com.tsintergy.lf.web.base.load.controller.BaseLoadFeatureController;
import com.tsintergy.lf.web.base.load.response.FeatureAnalyseResp;
import com.tsintergy.lf.web.base.typhoon.response.CorrelationDTO;
import com.tsintergy.lf.web.large_model.before.response.LoadFeatureResp;
import com.tsintergy.lf.web.large_model.before.response.VolatilityResp;
import com.tsintergy.lf.web.large_model.before.response.WeatherAccuracyResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


/**
 *
 * @description: 【事前】-时序负荷
 * <AUTHOR>
 * @date 2025/07/05 11:14
 * @version: 1.0
 */ 
@RequestMapping("/before/day")
@RestController
@Api(tags = "日度事前控制器")
public class BeforeDayController extends BaseLoadFeatureController {

    private Logger logger = LogManager.getLogger(BeforeDayController.class);

    @Autowired
    private LoadCityHisService loadCityHisService;
    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;
    @Autowired
    private WeatherCityHisService weatherCityHisService;
    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;
    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;
    @Autowired
    CityService cityService;
    @Autowired
    LmLargeModelTextSettingService lmLargeModelTextSettingService;
    @Autowired
    WeatherCityFcService weatherCityFcService;
    @Autowired
    WeatherCityFcMeteoService weatherCityFcMeteoService;
    @Autowired
    WeatherCityFcBmService weatherCityFcBmService;
    @Autowired
    private ExtremeWeatherService extremeWeatherService;

    /**
     * what: 特性曲线
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("日历史曲线特性")
    @RequestMapping(value = "/hisLoadFeature", method = RequestMethod.GET)
    public BaseResp<LoadFeatureResp> getHisPowerAndRain() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        Date date  = DateUtils.addDays(systemDate, -1);
        List<BigDecimal> powerList = loadCityHisService
                .findLoadCityHisDO(cityId, date, date, caliberId);
        LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(cityId, date, date, caliberId).get(0);
        LoadFeatureResp result = new LoadFeatureResp();
        BeanUtils.copyProperties(loadFeatureCityDayHisDO, result);
        result.setLoadList(powerList);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }


    /**
     * what: 负荷k线图
     *
     * <AUTHOR>
     * @date 2025/07/05 11:16
     */
    @RequestMapping(value = "/power_K", method = RequestMethod.GET)
    @ApiOperation("负荷k线图")
    public BaseResp<List<FeatureAnalyseResp>> getDayLoadKChart() throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        Date endDate  = DateUtils.addDays(systemDate, -1);
        Date startDate = DateUtils.addDays(endDate, -15);
        String caliberId = this.getCaliberId();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        //日特性
        List<LoadFeatureCityDayHisDO> powerFeatureCityDayHisStatDOS = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
            .listWeatherFeatureCityDayHisDO(weatherCityId, startDate, endDate);
        if (CollectionUtils.isEmpty(powerFeatureCityDayHisStatDOS)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //将气象的日期和降雨量放到map中
        Map<String, BigDecimal> rainfallMap = new HashMap<>();
        //将气象的日期和最高温放到map中
        Map<String, BigDecimal> highestTemperatureMap = new HashMap<>();
        if (weatherFeatureCityDayHisStatDOS != null && weatherFeatureCityDayHisStatDOS.size() > 0) {
            Map<Date, BigDecimal> weatherFeature = weatherFeatureCityDayHisStatDOS.stream().collect(
                Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, weather -> weather.getRainfall() == null ? BigDecimal.ZERO : weather.getRainfall()));
            weatherFeature.forEach((k, v) -> {
                rainfallMap.put(DateUtils.date2String(k, DateFormatType.DATE_FORMAT_STR), v);
            });
            Map<Date, BigDecimal> highestTemperatureDateMap = weatherFeatureCityDayHisStatDOS.stream().collect(
                Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, weatherFeatureCityDayHisDO -> weatherFeatureCityDayHisDO.getHighestTemperature()== null ? new BigDecimal(0):weatherFeatureCityDayHisDO.getHighestTemperature()));
            highestTemperatureDateMap.forEach((k, v) -> {
                highestTemperatureMap.put(DateUtils.date2String(k, DateFormatType.DATE_FORMAT_STR), v);
            });
        }

        Map<Date, List<LoadFeatureCityDayHisDO>> mapByDate = powerFeatureCityDayHisStatDOS.stream()
            .collect(Collectors.groupingBy(dp -> new Date(dp.getDate().getTime())));

        //构建vo
        List<FeatureAnalyseResp> resultList = new ArrayList<>();
        if (powerFeatureCityDayHisStatDOS != null && powerFeatureCityDayHisStatDOS.size() > 0) {
            powerFeatureCityDayHisStatDOS.forEach(powerFeatureCityDayHisStatDO -> {
                FeatureAnalyseResp resp = new FeatureAnalyseResp();
                BeanUtils.copyProperties(powerFeatureCityDayHisStatDO, resp);
                //获取前三天的出力数据
                List<LoadFeatureCityDayHisDO> firstData = mapByDate.get(resp.getDate());
                List<LoadFeatureCityDayHisDO> secondData = mapByDate.get(DateUtil.getMoveDay(resp.getDate(), -1));
                List<LoadFeatureCityDayHisDO> thirdData = mapByDate.get(DateUtil.getMoveDay(resp.getDate(), -2));
                if (firstData != null && secondData != null && thirdData != null) {
                    BigDecimal add = BigDecimalUtils
                        .add(firstData.get(0).getAveLoad(), secondData.get(0).getAveLoad());
                    BigDecimal add1 = BigDecimalUtils
                        .add(add, thirdData.get(0).getAveLoad());
                    BigDecimal divide = BigDecimalUtils
                        .divide(add1, new BigDecimal(3), 4);
                    resp.setAvgPeriod(divide);
                }
                resp.setAvgLoad(powerFeatureCityDayHisStatDO.getAveLoad());
                resp.setRain(rainfallMap.get(
                    DateUtils.date2String(powerFeatureCityDayHisStatDO.getDate(), DateFormatType.DATE_FORMAT_STR)));
                resp.setHighestTemperature(highestTemperatureMap.get(
                    DateUtils.date2String(powerFeatureCityDayHisStatDO.getDate(), DateFormatType.DATE_FORMAT_STR)));

                resultList.add(resp);
            });
        }

        if (resultList.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resultList);
        return baseResp;
    }



    /**
     * what: 指标分析
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("指标分析")
    @RequestMapping(value = "/volatility", method = RequestMethod.GET)
    public BaseResp<VolatilityResp> volatility() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        VolatilityResp result = new VolatilityResp();
        result.setMaxLoadVolatility(this.generateRandomBigDecimal(0.5));
        result.setMinLoadVolatility(this.generateRandomBigDecimal(0.5));
        result.setAvgLoadVolatility(this.generateRandomBigDecimal(0.2));
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    public  BigDecimal generateRandomBigDecimal(double bound) {
        // 生成[0, 0.5)之间的随机double
        double randomValue = ThreadLocalRandom.current().nextDouble(0.0, bound);
        // 转换为BigDecimal并保留15位小数（可根据需求调整）
        return BigDecimal.valueOf(randomValue).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
    }


    /**
     * what: 调度预测AI大模型
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    //TODO guodq 波动性计算
    @ApiOperation("调度预测AI大模型-一、负荷数据")
    @GetMapping(value = "/loadText")
    public  BaseResp<String>  loadText() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        Date date  = DateUtils.addDays(systemDate, -1);
        String dateStr = com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String year = dateStr.substring(0,4);
        String month = dateStr.substring(5,7);
        String day = dateStr.substring(8,10);
        LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(cityId, date, date, caliberId).get(0);
        //波动性
        VolatilityResp volatility = this.volatility().getData();
        List<WeatherAccuracyResp> sortedLoadVolatility = this.getSortedLoadVolatility(volatility.getMaxLoadVolatility(), volatility.getMinLoadVolatility(),
                volatility.getAvgLoadVolatility());
        String loadSource = sortedLoadVolatility.get(0).getWeatherSource();
        LmLargeModelTextSettingDO beforeLoad = lmLargeModelTextSettingService.getByField("before_load");
        String l = "以及晚间17:30-20:30时刻，";
        //要替换的变量
        Map<String, Object> params = new HashMap<>();
        params.put("a", year);
        params.put("b", month);
        params.put("c", day);
        params.put("d", loadFeatureCityDayHisDO.getMaxLoad());
        params.put("e", loadFeatureCityDayHisDO.getMaxTime());
        params.put("f",loadFeatureCityDayHisDO.getMinLoad() );
        params.put("g",loadFeatureCityDayHisDO.getMinTime() );
        params.put("h",loadSource);
        params.put("i",sortedLoadVolatility.get(0).getWeatherAccuracy());
        params.put("j","10:30");
        params.put("k","14:00");
        params.put("l",l);
        String result = StringSubstitutor.replace(beforeLoad.getValue(), params);
        result =  result.replace("\\n", "\n")    // 处理字面量 \n → 真实换行
                .replace("\\\\n", "\n");
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    public List<WeatherAccuracyResp> getSortedLoadVolatility(
            BigDecimal maxLoad,
            BigDecimal minLoad,
            BigDecimal avgLoad) {

        // 1. 创建气象源数据列表
        List<WeatherAccuracyResp> accuracyList = new ArrayList<>();
        accuracyList.add(new WeatherAccuracyResp("最大负荷", maxLoad));
        accuracyList.add(new WeatherAccuracyResp("最小负荷", minLoad));
        accuracyList.add(new WeatherAccuracyResp("平均负荷", avgLoad));

        // 2. 按准确率降序排序（高到低）
        accuracyList.sort(Comparator.comparing(WeatherAccuracyResp::getWeatherAccuracy).reversed());

        return accuracyList;
    }

    /**
     * what: 气象筛选
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("气象筛选")
    @RequestMapping(value = "/weather/accuracy", method = RequestMethod.GET)
    public BaseResp<List<WeatherAccuracyResp>> getWeatherAccuracy(Integer days) throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        Date endDate  = DateUtils.addDays(systemDate, -1);
        Date startDate = DateUtils.addDays(endDate, -30);
        if (days !=null) {
            startDate = DateUtils.addDays(endDate, -days);
        }

        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService.findWeatherCityFcDOs(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<String, List<BigDecimal>> weatherCityFcDOMap = weatherCityFcDOs.stream().collect(Collectors.toMap(x -> com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(x.getDate(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR), x -> x.getWeatherList()));
        List<WeatherCityFcMeteoDO> weatherCityFcMeteoDOS = weatherCityFcMeteoService.getListByCondition(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<String, List<BigDecimal>> weatherCityFcMeteoDOMap = weatherCityFcMeteoDOS.stream().collect(Collectors.toMap(x -> com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(x.getDate(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR), x -> x.getWeatherList()));
        List<WeatherCityFcBmDO> weatherCityFcBmDOS = weatherCityFcBmService.getListByCondition(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        Map<String, List<BigDecimal>> weatherCityFcBmDOMap = weatherCityFcBmDOS.stream().collect(Collectors.toMap(x -> com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(x.getDate(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR), x -> x.getWeatherList()));
        List<BigDecimal> accuracy = new ArrayList<>();
        List<BigDecimal> accuracyMeteo = new ArrayList<>();
        List<BigDecimal> accuracyBm = new ArrayList<>();
        for (WeatherCityHisDO weatherCityHisDO : weatherCityHisDOs) {
            java.sql.Date date = weatherCityHisDO.getDate();
            String dateStr = com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            List<BigDecimal> hisWeatherList = weatherCityHisDO.getWeatherList();
            List<BigDecimal> fcWeatherList = weatherCityFcDOMap.get(dateStr);
            List<BigDecimal> fcMeteoWeatherList = weatherCityFcMeteoDOMap.get(dateStr);
            List<BigDecimal> fcBmWeatherList = weatherCityFcBmDOMap.get(dateStr);
            List<BigDecimal> accuracyList = WeatherCalcUtil.calcAccuracy(hisWeatherList, fcWeatherList);
            List<BigDecimal> accuracyMeteoList = WeatherCalcUtil.calcAccuracy(hisWeatherList, fcMeteoWeatherList);
            List<BigDecimal> accuracyBmList = WeatherCalcUtil.calcAccuracy(hisWeatherList, fcBmWeatherList);
            accuracy.add( CollectionUtils.isEmpty(accuracyList) ? null : BigDecimalFunctions.listAvg(accuracyList));
            accuracyMeteo.add( CollectionUtils.isEmpty(accuracyMeteoList) ? null : BigDecimalFunctions.listAvg(accuracyMeteoList));
            accuracyBm.add(CollectionUtils.isEmpty(accuracyBmList) ? null : BigDecimalFunctions.listAvg(accuracyBmList));
        }
        BigDecimal accuracyAvg = BigDecimalFunctions.listAvg(accuracy);
        BigDecimal accuracyMeteoAvg = BigDecimalFunctions.listAvg(accuracyMeteo);
        BigDecimal accuracyBmAvg = BigDecimalFunctions.listAvg(accuracyBm);

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(this.getSortedWeatherAccuracy(accuracyAvg, accuracyMeteoAvg, accuracyBmAvg));
        return baseResp;
    }

    public List<WeatherAccuracyResp> getSortedWeatherAccuracy(
            BigDecimal accuracyAvg,       // 气象局
            BigDecimal accuracyMeteoAvg,  // EC
            BigDecimal accuracyBmAvg) {   // BM

        // 1. 创建气象源数据列表
        List<WeatherAccuracyResp> accuracyList = new ArrayList<>();
        accuracyList.add(new WeatherAccuracyResp("气象局", accuracyAvg));
        accuracyList.add(new WeatherAccuracyResp("EC", accuracyMeteoAvg));
        accuracyList.add(new WeatherAccuracyResp("BM", accuracyBmAvg));

        // 2. 按准确率降序排序（高到低）
        accuracyList.sort(Comparator.comparing(WeatherAccuracyResp::getWeatherAccuracy).reversed());

        return accuracyList;
    }

    /**
     * what: 气象曲线
     *
     * <AUTHOR>
     * @date 2025/07/05 11:31
     */
    @ApiOperation("气象曲线")
    @RequestMapping(value = "/weather/list", method = RequestMethod.GET)
    public BaseResp<List<BigDecimal>> weatherList(@ApiParam(value = "类型：1-历史 2-预测")Integer type) throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        Date hisDate  = DateUtils.addDays(systemDate, -1);
        Date fcDate = DateUtils.addDays(systemDate, 1);
        List<BigDecimal> weatherCityValueList = null;
        if (type==1){
            weatherCityValueList = weatherCityHisService.findWeatherCityHisValueList(cityId, hisDate, WeatherEnum.TEMPERATURE.getType());
        }else if (type==2){
            weatherCityValueList = weatherCityFcService.find96WeatherCityFcValue(fcDate,cityId, WeatherEnum.TEMPERATURE.getType());
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(weatherCityValueList);
        return baseResp;
    }

    @RequestMapping(value = "/weather/correlation", method = RequestMethod.GET)
    @ApiOperation("相关系数分析")
    public BaseResp<CorrelationDTO> getTyphoonCorrelationV2(List<Integer> rowList) throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        String caliberId = Constants.CALIBER_ID_BG_QW;
        Date startDate  = DateUtils.addDays(systemDate, -30);
        Date endDate = DateUtils.addDays(systemDate, -1);
        BaseResp resp = BaseResp.succResp();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisService.findWeatherFeatureBySearchData(weatherCityId, startDate, endDate,null);
        if(CollectionUtils.isEmpty(weatherFeatureCityDayHisVOS)){
            resp.setData(null);
            resp.setRetCode("T706");
            resp.setRetMsg("查询气象特性数据为空");
            return resp;
        }
        List<Date> dateList = weatherFeatureCityDayHisVOS.stream().map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<TyphoonCorrelationDTO> result = extremeWeatherService.getTyphoonCorrelation(dateList, cityId, caliberId);
        List<Integer> columns = Arrays.asList(0,1,2,3,7);
        List<Integer> rows = Arrays.asList(0,1,2);
        if (CollectionUtils.isNotEmpty(rowList)) {
            rows = rowList;
        }
        List<Integer> finalRows = rows;
        result = result.stream().filter(t -> columns.contains(t.getY()) && finalRows.contains(t.getX())).collect(Collectors.toList());

        CorrelationDTO correlationDTO = new CorrelationDTO();
        correlationDTO.setResult(result);
        result.sort(Comparator.comparing(TyphoonCorrelationDTO::getV, Comparator.nullsFirst(Comparator.reverseOrder())));
        MaxMinCorrelationDTO maxMinCorrelationDTO = new MaxMinCorrelationDTO();
        if(CollectionUtils.isNotEmpty(result)){
            TyphoonCorrelationDTO typhoonCorrelationDTO = result.get(0);
            maxMinCorrelationDTO.setPositiveMax(LoadFeatureEnum.findNameByType(typhoonCorrelationDTO.getX()) + "-" + WeatherFeatureEnum.findNameByType(typhoonCorrelationDTO.getY()));
            maxMinCorrelationDTO.setMaxCoefficient(typhoonCorrelationDTO.getV());
        }

        //最大负相关
        List<TyphoonCorrelationDTO> maxMIn = result.stream().filter(t -> t.getV().compareTo(new BigDecimal(0)) <0).collect(Collectors.toList());
        maxMIn.sort(Comparator.comparing(TyphoonCorrelationDTO::getV, Comparator.nullsFirst(Comparator.naturalOrder())));
        if(CollectionUtils.isNotEmpty(maxMIn)){
            TyphoonCorrelationDTO correlationDTO1 = maxMIn.get(0);
            maxMinCorrelationDTO.setPositiveMinus(LoadFeatureEnum.findNameByType(correlationDTO1.getX()) + "-" + WeatherFeatureEnum.findNameByType(correlationDTO1.getY()));
            maxMinCorrelationDTO.setMinCoefficient(correlationDTO1.getV());
        }
        correlationDTO.setCorrelationDTO(maxMinCorrelationDTO);
        resp.setData(correlationDTO);
        return resp;
    }

    @ApiOperation("气象散点分析")
    @RequestMapping(value = "/weather/weatherScatter", method = RequestMethod.GET)
    public BaseResp<List<TyphoonScatterDTO>> getTyphoonWeatherScatter() throws Exception {
        Date systemDate = this.getSystemDate();
        String cityId = Constants.PROVINCE_ID;
        String caliberId = Constants.CALIBER_ID_BG_QW;
        Date startDate  = DateUtils.addDays(systemDate, -30);
        Date endDate = DateUtils.addDays(systemDate, -1);
        String columnName = WeatherColumnEnum.HIGHESTTEMPERATURE.getValue();
        String rowName = FeatureRowEnum.MAXLOAD.getValue();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisService.findWeatherFeatureBySearchData(weatherCityId, startDate,endDate, null);
        List<Date> dateList = weatherFeatureCityDayHisVOS.stream().map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<TyphoonScatterDTO> results = extremeWeatherService.getTyphoonWeatherScatter(dateList,
                rowName,
                columnName,
                cityId,
                caliberId);
        BaseResp resp = BaseResp.succResp();
        resp.setData(results);
        return resp;
    }

    @ApiOperation("调度预测AI大模型-二、气象数据")
    @RequestMapping(value = "/weatherText", method = RequestMethod.GET)
    public BaseResp<String> weatherText() throws Exception {
        Date systemDate = this.getSystemDate();
        String caliberId = Constants.CALIBER_ID_BG_QW;
        String cityId = Constants.PROVINCE_ID;
        Date date  = DateUtils.addDays(systemDate, -1);
        Date fcDate  = DateUtils.addDays(systemDate, 1);
        Date startDate  = DateUtils.addDays(systemDate, -3);
        LmLargeModelTextSettingDO beforeWeather = lmLargeModelTextSettingService.getByField("before_weather");
        WeatherAccuracyResp weatherAccuracyResp = this.getWeatherAccuracy(7).getData().get(0);
        List<TyphoonCorrelationDTO> typhoonCorrelationDTOList = this.getTyphoonCorrelationV2(Arrays.asList(0)).getData().getResult();

        String b = WeatherFeatureEnum.findNameByType(typhoonCorrelationDTOList.get(0).getY()) + ","
                + WeatherFeatureEnum.findNameByType(typhoonCorrelationDTOList.get(1).getY()) + ","
                + WeatherFeatureEnum.findNameByType(typhoonCorrelationDTOList.get(2).getY());

        BigDecimal hisTemMax = BigDecimalFunctions.listMax(this.weatherList(1).getData());
        BigDecimal fcTemMax = BigDecimalFunctions.listMax(this.weatherList(2).getData());

        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisService.findWeatherFeatureBySearchData(cityId, startDate, date,null);
        long countHignTemp = weatherFeatureCityDayHisVOS.stream().filter(x -> BigDecimal.valueOf(35).compareTo(x.getHighestTemperature()) < 0).count();
        long countRainfall = weatherFeatureCityDayHisVOS.stream().filter(x -> x.getRainfall() != null && x.getRainfall().compareTo(BigDecimal.ZERO) > 0).count();
        BigDecimal subtract = BigDecimalFunctions.subtract(fcTemMax, hisTemMax);

        WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = weatherFeatureCityDayHisVOS.stream().sorted(Comparator.comparing(WeatherFeatureCityDayHisDO::getDate).reversed()).collect(Collectors.toList()).get(0);
        BigDecimal hisEffectiveTemperature = weatherFeatureCityDayHisDO.getHighestEffectiveTemperature();
        BigDecimal fcEffectiveTemperature = weatherFeatureCityDayFcService.findWeatherFeatureCityFcVOByDate(cityId, fcDate).getHighestEffectiveTemperature();
        BigDecimal i = BigDecimalFunctions.subtract(fcEffectiveTemperature, hisEffectiveTemperature);
        BigDecimal fcEffTempHisSub = BigDecimalFunctions.subtract(i.abs(), subtract.abs());
        String f = "升高";
        if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
            f = "降低";
        }
        String e = "未发生复杂气象耦合影响，推荐选用BP、GT等算法模型进行预测";
        if (countHignTemp >= 3) {
            e = "近3天最高温度已连续超过35℃，建议重点考虑连续高温气候影响，选用特征捕捉更为复杂的的GT、ANN等模型";
        }else if (countRainfall >= 3) {
            e = "伴随降雨影响，湿度有所增长，建议考虑体感温度变化，选用体感温度算法、ann等模型";
        }
        String j = "体感温度未出现差异变化，建议直接使用气象温度预测";
        if ( fcEffTempHisSub.compareTo(BigDecimal.valueOf(2.5)) >= 0){
            if (subtract.compareTo(BigDecimal.ZERO) >= 0) {
                j = "体感温度上升幅度远大于气温，建议增加体感温度作为模型输入";
            }else {
                j= "体感温度下降幅度远大于气温，建议增加体感温度作为模型输入";
            }
        }
        //要替换的变量
        Map<String, Object> params = new HashMap<>();
        params.put("a", weatherAccuracyResp.getWeatherSource());
        params.put("b", b);
        params.put("c", hisTemMax);
        params.put("d", fcTemMax);
        params.put("e", e);
        params.put("f", f);
        params.put("g", subtract);
        params.put("h", fcEffectiveTemperature);
        params.put("i", i);
        params.put("j", j);
        params.put("k", beforeWeather.getDescription());
        String result = StringSubstitutor.replace(beforeWeather.getValue(), params);
        result =  result.replace("\\n", "\n")    // 处理字面量 \n → 真实换行
                .replace("\\\\n", "\n");
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

}
