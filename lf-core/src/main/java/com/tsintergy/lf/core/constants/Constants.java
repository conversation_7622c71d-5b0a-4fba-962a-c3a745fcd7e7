/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 17:47 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.constants;

import com.tsintergy.lf.core.util.ColumnUtil;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * Description:  算法常量类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class Constants {



    public static final String HIS_TYPE = "1";

    public static final String REPORT_TYPE = "2";


    public static final Integer TIME_SPAN_MINUTE_5 = 5;

    public static final Integer NUM_288 = 288;
    public static final Integer NUM_96 = 96;

    /**
     * 人工决策算法ID
     */
    public static final String MD_ALGORITHM_ID = "0";

    /**
     * 超短期算法训练
     */
    public static final String TRAIN = "T";


    public static final Double β = 1.67;


    /**
     * 超短期算法预测
     */
    public static final String FORECAST = "P";

    public static final String START_WITH_ZERO = "SUBSYSTEM_START_WITH_ZERO";

    /**
     * 超短期预测目标点数；15分钟代表提前15分钟点 5分钟代表提前5分钟；其他点数来自UltraMultipointFifteenEnum or  UltraMultipointFiveEnum
     */
    public final static Integer ULTRA_FORECAST_1 = 1;


    public final static String EMPTY = " ";

    /**
     * 系统通用分隔符
     */
    public static final String SEPARATOR_PUNCTUATION = ",";

    /**
     * 分隔符英文折线
     */
    public static final String SEPARATOR_BROKEN_LINE = "-";

    /**
     * 时间范围分隔符
     */
    public static final String DATE_SEPARATOR_PUNCTUATION = "~";
    //查询条件为全部的常量
    public static final String ALL = "all";
    /*
     *
     * 省会城市Type
     * */
    public static final Integer PROVINCE_TYPE=1;

    public static final String PROVINCE_ID = "1";

    /*
     * 地市城市Type
     * */
    public static final  Integer CITY_TYPE = 2;

    public static final Integer AREA_TYPE = 9;

    /**
     * 计算中Bigdecimal保留的小数位
     */
    public static final int SCALE = 4;


    /**
     * 负荷曲线是否从0点开始,true是从00:00到23:45；false是从00:15到24:00；null表示从00:00到24:00，共97个点
     */
    public static final Boolean LOAD_CURVE_START_WITH_ZERO = false;

    /**
     * 日负荷曲线中的负荷点数
     */
    public static final int LOAD_CURVE_POINT_NUM = 96;

    /**
     * 气象曲线是否从0点开始,true是从00:00到23:45；false是从00:15到24:00；null表示从00:00到24:00，共97个点
     */
    public static final Boolean WEATHER_CURVE_START_WITH_ZERO = false;

    /**
     * 气象曲线中的负荷点数
     */
    public static final int WEATHER_CURVE_POINT_NUM = 96;

    /**
     * 日负荷曲线中的负荷点数
     */
    public static final int LOAD_CURVE_POINT_NUM_24 = 24;

    /**
     * 气象曲线中的负荷点数
     */
    public static final int WEATHER_CURVE_POINT_NUM_24 = 24;

    /**
     * 负荷曲线时段列表
     */
    public static final List<String> DAY_LOAD_CURVE_COLUMNS = ColumnUtil
        .getColumns(LOAD_CURVE_POINT_NUM, LOAD_CURVE_START_WITH_ZERO, true);

    /**
     * 网供口径
     */
    public static final String CALIBER_WANGGONG = "1";


    /**
     * 网损自动计算开关
     */
    public static final String NETWORK_LOSS_SWITCH = "1";


    /**
     * 默认 网损
     */
    public static final String  NETWORK_LOSS = "0.9088285714";


    /**
     * 预测模式 默认地市上报累加
     */
    public static final Integer  FORECAST_MODEL = 2;

    /**
     * 保供-地市口径
     */
    public static final String CALIBER_ID_BG_DS = "2";

    /**
     * 湖北统调
     */
    public static final String CALIBER_ID_TD = "12";

    /**
     * 地区网供
     */
    public static final String CALIBER_ID_WG = "1";

    /**
     * 保供-全网口径
     */
    public static final String CALIBER_ID_BG_QW = "4";

    /**
     * 月度
     */
    public static final Integer TYPE_MONTH = 1;

    /**
     * 度夏
     */
    public static final Integer TYPE_SUMMER = 2;

    /**
     * 度冬
     */
    public static final Integer TYPE_WINTER = 3;

    /**
     * 度冬月份
     */
    public static final String[] WINTER_MONTHS = {"11", "12", "01", "02"};

    /**
     * 度夏月份
     */
    public static final String[] SUMMER_MONTHS = {"06", "07", "08","09"};

    /**
     * 气象温度阈值
     */
    public static final BigDecimal normalTem = BigDecimal.valueOf(22);

    /**
     * 气象温度阈值差
     */
    public static final BigDecimal extremeTem = BigDecimal.valueOf(1);

    public static final String MONTH_DEFAULT = "month_default";

    public static final String SUMMER_DEFAULT = "summer_default";

    public static final String WINTER_DEFAULT = "winter_default";

    public static final String YEAR_DEFAULT = "year_default";

    /**
     * 秒间隔类型
     */
    public final static Integer SECOND = 1;

    /**
     * 分钟间隔类型
     */
    public final static Integer MINUTE = 2;

    /**
     * 最大值偏差
     */
    public final static String MAX_VALUE_DIFF = "maxDiff";

    /**
     * 最小值偏差
     */
    public final static String MIN_VALUE_DIFF = "minDiff";

    /**
     * 偏差最大值
     */
    public final static String MAX_DIFF = "diffMax";


    /**
     * 全社会行业负荷code
     */
    public static final String TOTAL_SOCIAL_TRADE_CODE = "AAAA";


    /**
     * 地市大用户计划填报预测算法ID（随州、鄂州）
     */
    public static final String USER_ALGORITHM_ID = "1010";

    /**
     * 地市大用户计划填报预测算法名称（随州、鄂州）
     */
    public static final String USER_ALGORITHM_NAME = "随州大用户";

    /**
     * 地市大用户计划填报预测算法集合（随州、鄂州）
     */
    public static final List<String> USER_CITY_ID = Arrays.asList("5", "14");

    /**
     * 第一批次
     */
    public static final String BATCH_ONE = "第一批次";

    /**
     * 第二批次
     */
    public static final String BATCH_TWO = "第二批次";
    public static final String ALGO_RECOMMEND_REDDIS_KEY = "ALGO-RECOMMEND-";


    public static final String CORRELATION_FEATURE_TYPE_REGION = "region";

    public static final String CORRELATION_FEATURE_TYPE_INDUSTRY = "industry";

    public static final String G_TRANSFORMER_ID = "813";
}
