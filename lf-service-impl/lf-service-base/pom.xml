<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lf-service-impl</artifactId>
        <groupId>com.tsintergy.lf</groupId>
        <version>${lf.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lf-service-base</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tsieframework.boot</groupId>
            <artifactId>tsie-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.junrar</groupId>
            <artifactId>junrar</artifactId>
            <version>0.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tsintergy.lf</groupId>
            <artifactId>lf-starter-service-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tsintergy.lf</groupId>
            <artifactId>lf-config</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tsieframework.cloud.security</groupId>
            <artifactId>tsie-cloud-security-service-api</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.tsieframework.cloud.security</groupId>
            <artifactId>tsie-cloud-security-service-base</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <artifactId>easyexcel</artifactId>
            <groupId>com.alibaba</groupId>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.tsintergy.lf</groupId>
            <artifactId>lf-service-api</artifactId>
        </dependency>
        <dependency>
            <artifactId>reflections</artifactId>
            <groupId>org.reflections</groupId>
            <scope>compile</scope>
            <version>0.9.11</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.7</version>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>org.jboss.slf4j</groupId>-->
<!--            <artifactId>slf4j-jboss-logging</artifactId>-->
<!--            <version>1.2.1.Final</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>package-springboot</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.1.6.RELEASE</version>
                        <configuration>
                            <excludes>
                                <exclude>
                                    <groupId>com.tsintergy.lf</groupId>
                                    <artifactId>lf-config</artifactId>
                                </exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>package-docker</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>