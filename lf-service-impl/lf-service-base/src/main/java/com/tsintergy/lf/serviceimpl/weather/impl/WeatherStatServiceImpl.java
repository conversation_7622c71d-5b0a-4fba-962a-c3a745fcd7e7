/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *Description:  <br>
 *
 *@Author: <EMAIL>
 *@Date: 2022/6/29 17:29
 *@Version: 1.0.0
 */
@Slf4j
@Service
public class WeatherStatServiceImpl implements WeatherStatService {

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityFcMeteoService weatherCityFcMeteoService;

    @Autowired
    private WeatherCityFcBmService weatherCityFcBmService;

    @Autowired
    private WeatherStatService weatherStatService;


    @Autowired
    CityService cityService;

    @Override
    public void statPorviceWeather(Date start, Date end) throws Exception {
        statPorviceHisWeather(start, end);
        statPorviceFcWeather(start, end);
        weatherCityFcMeteoService.statMeteoProvinceFcWeather(start, end);
        weatherCityFcBmService.statMeteoProvinceFcWeather(start, end);
    }


    @Override
    public void statPorviceHisWeather(Date start, Date end) throws Exception {
        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(null, null, start, end);
        List<WeatherCityHisDO> provinceWeathers = weatherStatService.calcProvinceWeather(weatherCityHisDOs, WeatherCityHisDO.class);
        if (!CollectionUtils.isEmpty(provinceWeathers)) {
            saveHisBatch(provinceWeathers);
        }
    }


    @Override
    public void statPorviceFcWeather(Date start, Date end) throws Exception {
        List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService.findWeatherCityFcDOs(null, null, start, end);
        List<WeatherCityFcDO> provinceWeathers = weatherStatService.calcProvinceWeather(weatherCityFcDOs, WeatherCityFcDO.class);
        if (!CollectionUtils.isEmpty(provinceWeathers)) {
            saveFcBatch(provinceWeathers);
        }
    }

    private void saveHisBatch(List<WeatherCityHisDO> values) {
        List<WeatherCityHisDO> updateD0List = new ArrayList<>();
        int size = 100;
        int loop = 1;
        for (int i = 0; i < values.size(); i++) {
            try {
                PeriodDataUtil.do24To96VO(values.get(i));
                updateD0List.add(values.get(i));
                if (i >= loop * size - 1) {
                    log.info("第{}次批量入库开始....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    weatherCityHisService.doSaveOrUpdateBatch(updateD0List);
                    log.info("第{}次批量入库完成....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    updateD0List.clear();
                    loop = loop + 1;
                }
            } catch (Exception e) {
                log.error("批量保存历史气象异常", e);
            }
        }
        if (!CollectionUtils.isEmpty(updateD0List)) {
            weatherCityHisService.doSaveOrUpdateBatch(updateD0List);
        }
        log.info("批量入库完成....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
    }

    private void saveFcBatch(List<WeatherCityFcDO> values) {
        List<WeatherCityFcDO> updateD0List = new ArrayList<>();
        int size = 100;
        int loop = 1;
        for (int i = 0; i < values.size(); i++) {
            try {
                PeriodDataUtil.do24To96VO(values.get(i));
                updateD0List.add(values.get(i));
                if (i >= loop * size - 1) {
                    log.info("预测气象第{}次批量入库开始....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    weatherCityFcService.doInsertOrUpdateBatch(updateD0List);
                    log.info("预测气象第{}次批量入库完成....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    updateD0List.clear();
                    loop = loop + 1;
                }
            } catch (Exception e) {
                log.error("批量保存预测气象异常", e);
            }
        }
        if (!CollectionUtils.isEmpty(updateD0List)) {
            weatherCityFcService.doInsertOrUpdateBatch(updateD0List);
        }
        log.info("批量入库预测气象完成....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
    }

    @SneakyThrows
    @Override
    public <T extends BaseWeatherDO> List<T> calcProvinceWeather(List<T> weatherDOS, Class<T> clazz) {
        if (org.springframework.util.CollectionUtils.isEmpty(weatherDOS)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<String, List<T>> dateWeatherMap = weatherDOS.stream()
                .collect(Collectors.groupingBy(t ->
                        DateUtil.formateDate(t.getDate()) + Constants.SEPARATOR_PUNCTUATION + t.getType()
                ));
        Map<String, String> cityMap = cityService.findAllCitys().stream()
                .collect(Collectors.toMap(CityDO::getCity, CityDO::getId));

        dateWeatherMap.forEach((key, weatherList) -> {
            if (org.springframework.util.CollectionUtils.isEmpty(weatherList)) {
                return;
            }

            try {
                List<BigDecimal> resultValues = new ArrayList<>();
                // 遍历城市数据并计算加权和
                for (T weatherDO : weatherList) {
                    String cityId = weatherDO.getCityId();
                    BigDecimal weight = getCityWeight(cityId, cityMap);
                    if (weight == null || weight.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }

                    List<BigDecimal> weatherValues = BasePeriodUtils.toList(
                            weatherDO, Constants.WEATHER_CURVE_POINT_NUM, true
                    );
                    weatherValues.add(weatherDO.getT2400());

                    List<BigDecimal> weightedValues = DataUtil.multiply(weatherValues, weight);
                    resultValues = DataUtil.listAdd(resultValues, weightedValues);
                }

                if (!org.springframework.util.CollectionUtils.isEmpty(resultValues)) {
                    T provinceWeatherDO = clazz.getDeclaredConstructor().newInstance();
                    provinceWeatherDO.setType(WeatherEnum.TEMPERATURE.getType());

                    String[] split = key.split(Constants.SEPARATOR_PUNCTUATION);
                    provinceWeatherDO.setType(Integer.valueOf(split[1]));
                    provinceWeatherDO.setDate(new java.sql.Date(DateUtil.getDate(split[0], "yyyy-MM-dd").getTime()));
                    provinceWeatherDO.setCityId(CityConstants.PROVINCE_ID);
                    Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(resultValues.subList(0, 96), true);
                    BasePeriodUtils.setAllFiled(provinceWeatherDO, decimalMap);
                    provinceWeatherDO.setT2400(resultValues.get(96));
                    result.add(provinceWeatherDO);
                }
            } catch (Exception e) {
                log.error("装配省调气象计算异常", e);
            }
        });

        return result;
    }

    /**
     * 根据城市ID获取权重（公共逻辑）
     */
    private BigDecimal getCityWeight(String cityId, Map<String, String> cityMap) {
        String cityName = cityMap.entrySet().stream()
                .filter(e -> e.getValue().equals(cityId))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);

        if (cityName == null) {
            return null;
        }

        switch (cityName) {
            case "武汉":
                return new BigDecimal("0.33333333");
            case "黄冈":
                return new BigDecimal("0.15896188");
            case "襄阳":
                return new BigDecimal("0.14220059");
            case "荆州":
                return new BigDecimal("0.14138956");
            case "孝感":
                return new BigDecimal("0.1154366");
            case "宜昌":
                return new BigDecimal("0.10867802");
            default:
                return BigDecimal.ZERO;
        }
    }
}