package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureSourceMonthFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureSourceMonthFcDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureSourceMonthFcDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("weatherFeatureSourceMonthFcService")
public class WeatherFeatureSourceMonthFcServiceImpl implements WeatherFeatureSourceMonthFcService {

    @Autowired
    WeatherFeatureSourceMonthFcDAO weatherFeatureSourceMonthFcDAO;

    @Override
    public List<WeatherFeatureSourceMonthFcDO> findByParam(String cityId, String startDate, String endDate, String sourceType) throws Exception {
        List<WeatherFeatureSourceMonthFcDO> all = weatherFeatureSourceMonthFcDAO.findAll(JpaWrappers.<WeatherFeatureSourceMonthFcDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), WeatherFeatureSourceMonthFcDO::getCityId, cityId)
                .eq( WeatherFeatureSourceMonthFcDO::getSourceType, sourceType)
                .ge(WeatherFeatureSourceMonthFcDO::getYm, startDate)
                .le(WeatherFeatureSourceMonthFcDO::getYm, endDate)
        );
        return all;
    }

    @Override
    public void saveOrUpdate(WeatherFeatureSourceMonthFcDO weatherFeatureSourceMonthFcDO) throws Exception {
        List<WeatherFeatureSourceMonthFcDO> all = weatherFeatureSourceMonthFcDAO.findAll(JpaWrappers.<WeatherFeatureSourceMonthFcDO>lambdaQuery()
                .eq(WeatherFeatureSourceMonthFcDO::getCityId, weatherFeatureSourceMonthFcDO.getCityId())
                .eq(WeatherFeatureSourceMonthFcDO::getSourceType, weatherFeatureSourceMonthFcDO.getSourceType())
                .eq(WeatherFeatureSourceMonthFcDO::getYm, weatherFeatureSourceMonthFcDO.getYm())
        );
        if (CollectionUtils.isNotEmpty(all)) {
            String id = all.get(0).getId();
            weatherFeatureSourceMonthFcDO.setId(id);
            weatherFeatureSourceMonthFcDAO.saveOrUpdateByTemplate(weatherFeatureSourceMonthFcDO);
        } else {
            weatherFeatureSourceMonthFcDAO.save(weatherFeatureSourceMonthFcDO);
        }
    }
}
