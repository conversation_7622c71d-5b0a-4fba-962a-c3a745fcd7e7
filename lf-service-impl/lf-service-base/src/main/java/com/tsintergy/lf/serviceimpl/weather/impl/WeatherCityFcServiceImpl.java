
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.core.util.*;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.annotation.FcWeatherDataSource;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.collect.WeatherFcCollectDTO;
import com.tsintergy.lf.serviceapi.base.weather.collect.WeatherFcCollectVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.weather.dao.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityFcServiceImpl.java, v 0.1 2018-01-31 10:59:53 tao Exp $$
 */

@Slf4j
@Service("weatherCityFcService")
@FcWeatherDataSource(source = "")
public class WeatherCityFcServiceImpl extends AbstractBaesWeatherServiceImpl implements WeatherCityFcService {

    @Autowired
    WeatherCityFcModifyService weatherCityFcModifyService;

    @Autowired
    CityService cityService;

    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    WeatherCityFcAdapterService weatherCityFcAdapterService;

    @Autowired
    WeatherCityHisDAO weatherCityHisDAO;

    @Autowired
    WeatherCityFcCopy1DAO weatherCityFcCopy1DAO;

    @Autowired
    WeatherCityFcCopy2DAO weatherCityFcCopy2DAO;

    @Autowired
    private StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;

    @Autowired
    WeatherCityFcMapper weatherCityFcMapper;

    @Override
    protected List<? extends BaseWeatherDO> findWeatherCityVO(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, startDate, endDate);
    }

    @Override
    protected List<? extends BaseWeatherDO> findStatisticsSynthesizeWeatherCityDayVO(String cityId, Integer type,
        Date startDate, Date endDate) throws Exception {
        return statisticsSynthesizeWeatherCityDayFcService
            .findStatisticsSynthesizeWeatherCityDayFcDO(cityId, type, startDate, endDate);
    }


    @Override
    public DataPackage queryWeatherCityFcDO(DBQueryParam param) throws Exception {
        try {
            return weatherCityFcDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public DataPackage queryWeatherCityFcDO1(DBQueryParam param) throws Exception {
        try {
            return weatherCityFcCopy1DAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public DataPackage queryWeatherCityFcDO2(DBQueryParam param) throws Exception {
        try {
            return weatherCityFcCopy2DAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }
    /**
     * 查找预测数据
     */
    @Override
    public List<WeatherDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            /*//查询全部
            if (StringUtils.isNotBlank(cityId) && cityId.equals("1")) {
                cityId = null;
            }*/
            cityId = cityService.findWeatherCityId(cityId);
            List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcDAO
                .findWeatherCityFcDO(cityId, type, startDate, endDate);
            if (weatherCityFcVOS.size() < 1) {
                return new ArrayList<>();
            }
            List<WeatherDTO> weatherDTOS = new ArrayList<WeatherDTO>(10);
            for (WeatherCityFcDO weatherCityHisVO : weatherCityFcVOS) {
                WeatherDTO weatherDTO = new WeatherDTO();
                weatherDTO.setId(weatherCityHisVO.getId());
                weatherDTO.setDate(weatherCityHisVO.getDate());
                weatherDTO.setWeek(DateUtil.getWeek(weatherCityHisVO.getDate()));
                weatherDTO.setCity(this.cityService.findCityById(weatherCityHisVO.getCityId()).getCity());
                weatherDTO.setData(BasePeriodUtils.toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM,
                    Constants.WEATHER_CURVE_START_WITH_ZERO));
                weatherDTOS.add(weatherDTO);
            }
            return weatherDTOS;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Autowired
    private WeatherCityFcMeteoService weatherCityFcMeteoService;

    @Autowired
    private WeatherCityFcBmService weatherCityFcBmService;

    @Override
    public List<WeatherDTO> findWeatherCityFcAllDTOs(String cityId, Integer type, Date startDate, Date endDate, String source) throws Exception {
        // 预测最新批次
        List<WeatherDTO> weatherCityFcDTOs = new ArrayList<>();
        List<WeatherCityFcMeteoDO> listEcByCondition = new ArrayList<>();
        List<WeatherCityFcBmDO> listBmByCondition = new ArrayList<>();
        if (WeatherSourceEnum.FC.getCode().equals(source)) {
            weatherCityFcDTOs = findWeatherCityFcDTOs(cityId, type, startDate, endDate);
        } else if (WeatherSourceEnum.METEO.getCode().equals(source)){
            listEcByCondition = weatherCityFcMeteoService.getListByCondition(cityId, type, startDate, endDate);
        } else if (WeatherSourceEnum.BM.getCode().equals(source)) {
            listBmByCondition = weatherCityFcBmService.getListByCondition(cityId, type, startDate, endDate);
        }
        if (!CollectionUtils.isEmpty(weatherCityFcDTOs) && weatherCityFcDTOs.size() > 0) {
            return weatherCityFcDTOs;
        }
        if (!CollectionUtils.isEmpty(listEcByCondition) && listEcByCondition.size() > 0) {
            for (WeatherCityFcMeteoDO weatherCityFcMeteoDO : listEcByCondition) {
                WeatherDTO weatherDTO = new WeatherDTO();
                weatherDTO.setId(UUID.randomUUID().toString().replace("-", ""));
                weatherDTO.setDate(weatherCityFcMeteoDO.getDate());
                weatherDTO.setWeek(DateUtil.getWeek(weatherCityFcMeteoDO.getDate()));
                try {
                    weatherDTO.setCity(this.cityService.findCityById(weatherCityFcMeteoDO.getCityId()).getCity());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                weatherDTO.setData(weatherCityFcMeteoDO.getWeatherList());
                weatherCityFcDTOs.add(weatherDTO);
            }
        } else if (!CollectionUtils.isEmpty(listBmByCondition) && listBmByCondition.size() > 0) {
            for (WeatherCityFcBmDO weatherCityFcMeteoDO : listBmByCondition) {
                WeatherDTO weatherDTO = new WeatherDTO();
                weatherDTO.setId(UUID.randomUUID().toString().replace("-", ""));
                weatherDTO.setDate(weatherCityFcMeteoDO.getDate());
                weatherDTO.setWeek(DateUtil.getWeek(weatherCityFcMeteoDO.getDate()));
                try {
                    weatherDTO.setCity(this.cityService.findCityById(weatherCityFcMeteoDO.getCityId()).getCity());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                weatherDTO.setData(weatherCityFcMeteoDO.getWeatherList());
                weatherCityFcDTOs.add(weatherDTO);
            }
        }
        return weatherCityFcDTOs;
    }

    @Override
    public WeatherCityFcDO doCreate(WeatherCityFcDO vo) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcCopy1DO doCreate(WeatherCityFcCopy1DO vo) throws Exception {
        try {
            return (WeatherCityFcCopy1DO) weatherCityFcCopy1DAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcCopy2DO doCreate(WeatherCityFcCopy2DO vo) throws Exception {
        try {
            return (WeatherCityFcCopy2DO) weatherCityFcCopy2DAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityFcDO(WeatherCityFcDO vo) throws Exception {
        try {
            weatherCityFcDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityFcDOByPK(Serializable pk) throws Exception {
        try {
            weatherCityFcDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO doUpdateWeatherCityFcDO(WeatherCityFcDO vo) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcCopy1DO doUpdateWeatherCityFcDO(WeatherCityFcCopy1DO vo) throws Exception {
        try {
            return (WeatherCityFcCopy1DO) weatherCityFcCopy1DAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcCopy2DO doUpdateWeatherCityFcDO(WeatherCityFcCopy2DO vo) throws Exception {
        try {
            return (WeatherCityFcCopy2DO) weatherCityFcCopy2DAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO findWeatherCityFcDOByPk(Serializable pk) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            cityId = cityService.findWeatherCityId(cityId);
            return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, startDate, endDate);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate, String algorithmId) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        cityId = cityService.findWeatherCityId(cityId);
        Date tomorrowDate = org.apache.commons.lang3.time.DateUtils.addDays(targetDate, +1);
        List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcAdapterService
                .findFcWeather(cityId, algorithmId, null, tomorrowDate, tomorrowDate);
        List<WeatherCityHisDO> weatherCityHisDO = weatherCityHisDAO
            .findWeatherCityHisDO(cityId, null, targetDate, targetDate);
        if (weatherCityFcVOS.size() < 1 && weatherCityHisDO.size() < 1) {
            return null;
        }
        for (WeatherCityFcDO weatherCityFcVO : weatherCityFcVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityFcVO.getType()));
            weatherNameDTO.setWeather(BasePeriodUtils
                .toList(weatherCityFcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherNameDTOS.add(weatherNameDTO);
        }
        if (!CollectionUtils.isEmpty(weatherCityHisDO)) {
            for (WeatherCityHisDO cityHisDO : weatherCityHisDO) {
                for (WeatherNameDTO weatherNameDTO : weatherNameDTOS) {
                    if (WeatherEnum.getValueByName(cityHisDO.getType()).equals(weatherNameDTO.getName())) {
                        weatherNameDTO.setHisWeather(BasePeriodUtils
                            .toList(cityHisDO, Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO));
                    }
                }
            }
        }
        return weatherNameDTOS;
    }

    @Override
    public List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date startDate, Date endDate) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        cityId = cityService.findWeatherCityId(cityId);
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcDAO
            .findWeatherCityFcDO(cityId, null, startDate, endDate);
        List<WeatherCityHisDO> weatherCityHisDO = weatherCityHisDAO
            .findWeatherCityHisDO(cityId, null, startDate, endDate);
        if (weatherCityFcVOS.size() < 1 && weatherCityHisDO.size() < 1) {
            return null;
        }
        for (WeatherCityFcDO weatherCityFcVO : weatherCityFcVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityFcVO.getType()));
            weatherNameDTO.setWeather(BasePeriodUtils
                .toList(weatherCityFcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherNameDTOS.add(weatherNameDTO);
        }
        for (int i = 0; i < weatherCityHisDO.size(); i++) {
            weatherNameDTOS.get(i).setHisWeather(BasePeriodUtils
                .toList(weatherCityHisDO.get(i), Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
        }
        return weatherNameDTOS;
    }

    @Override
    public WeatherCityFcDO findWeatherCityFcDO(String cityId, Integer type, Date date) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "cityId", cityId);
        if(type != null){
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        List<WeatherCityFcDO> datas = weatherCityFcDAO.query(dbQueryParamBuilder.build()).getDatas();
        if (datas!=null&&datas.size()!=0)
            return datas.get(0);
        return null;
    }


    @Override
    public void doInsertOrUpdate(WeatherCityFcDO weatherCityFcVO) throws Exception {
        if (weatherCityFcVO.getDate() == null || weatherCityFcVO.getCityId() == null
            || weatherCityFcVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", weatherCityFcVO.getDate())
            .where(QueryOp.StringEqualTo, "cityId", weatherCityFcVO.getCityId())
            .where(QueryOp.NumberEqualTo, "type", weatherCityFcVO.getType());
        List<WeatherCityFcDO> weatherCityFcVOS = this.queryWeatherCityFcDO(dbQueryParamBuilder.build()).getDatas();
        if (weatherCityFcVOS == null || weatherCityFcVOS.size() < 1) {
            weatherCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            this.doCreate(weatherCityFcVO);
            return;
        }
        weatherCityFcDAO.getSession().flush();
        weatherCityFcDAO.getSession().clear();
        WeatherCityFcDO old = weatherCityFcVOS.get(0);
        PeriodDataUtil.replaceValues(old, weatherCityFcVO);
        old.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        this.doUpdateWeatherCityFcDO(old);
    }

    @Override
    public void doSaveAllData(WeatherCityFcDO weatherCityFcDO) throws Exception {
        weatherCityFcDAO.save(weatherCityFcDO);
    }

    @Override
    public void doSaveAllData1(WeatherCityFcCopy1DO weatherCityFcDO) throws Exception {
        weatherCityFcCopy1DAO.save(weatherCityFcDO);
    }

    @Override
    public void doInsertOrUpdateList0(List<WeatherCityFcDO> weatherCityFcVOs) throws Exception {
        for (WeatherCityFcDO weatherCityFcVO : weatherCityFcVOs) {
            PeriodDataUtil.do24To96VO(weatherCityFcVO);
            this.doInsertOrUpdate(weatherCityFcVO);
        }
    }

    @Override
    public void doInsertOrUpdateList1(List<WeatherCityFcCopy1DO> weatherCityFcVOs) throws Exception {
        for (WeatherCityFcCopy1DO weatherCityFcVO : weatherCityFcVOs) {
            PeriodDataUtil.do24To96VO(weatherCityFcVO);
            this.doInsertOrUpdate(weatherCityFcVO);
        }
    }

    @Override
    public void doInsertOrUpdateList2(List<WeatherCityFcCopy2DO> weatherCityFcVOs) throws Exception {
        for (WeatherCityFcCopy2DO weatherCityFcVO : weatherCityFcVOs) {
            PeriodDataUtil.do24To96VO(weatherCityFcVO);
            this.doInsertOrUpdate(weatherCityFcVO);
        }
    }


    @Override
    public void doInsertOrUpdate(WeatherCityFcCopy1DO weatherCityFcVO) throws Exception {
        if (weatherCityFcVO.getDate() == null || weatherCityFcVO.getCityId() == null
            || weatherCityFcVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", weatherCityFcVO.getDate())
            .where(QueryOp.StringEqualTo, "cityId", weatherCityFcVO.getCityId())
            .where(QueryOp.StringEqualTo, "orderId", weatherCityFcVO.getOrderId())
            .where(QueryOp.NumberEqualTo, "type", weatherCityFcVO.getType());
        List<WeatherCityFcCopy1DO> weatherCityFcVOS = this.queryWeatherCityFcDO1(dbQueryParamBuilder.build()).getDatas();
        if (weatherCityFcVOS == null || weatherCityFcVOS.size() < 1) {
            weatherCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            this.doCreate(weatherCityFcVO);
            return;
        }
        weatherCityFcCopy1DAO.getSession().flush();
        weatherCityFcCopy1DAO.getSession().clear();
        WeatherCityFcCopy1DO old = weatherCityFcVOS.get(0);
        PeriodDataUtil.replaceValues(old, weatherCityFcVO);
        old.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        this.doUpdateWeatherCityFcDO(old);
    }

    @Override
    public void doInsertOrUpdate(WeatherCityFcCopy2DO weatherCityFcVO) throws Exception {
        if (weatherCityFcVO.getDate() == null || weatherCityFcVO.getCityId() == null
            || weatherCityFcVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", weatherCityFcVO.getDate())
            .where(QueryOp.StringEqualTo, "cityId", weatherCityFcVO.getCityId())
            .where(QueryOp.NumberEqualTo, "type", weatherCityFcVO.getType());
        List<WeatherCityFcCopy2DO> weatherCityFcVOS = this.queryWeatherCityFcDO2(dbQueryParamBuilder.build()).getDatas();
        if (weatherCityFcVOS == null || weatherCityFcVOS.size() < 1) {
            weatherCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            this.doCreate(weatherCityFcVO);
            return;
        }
        weatherCityFcCopy2DAO.getSession().flush();
        weatherCityFcCopy2DAO.getSession().clear();
        WeatherCityFcCopy2DO old = weatherCityFcVOS.get(0);
        PeriodDataUtil.replaceValues(old, weatherCityFcVO);
        old.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        this.doUpdateWeatherCityFcDO(old);
    }


    @Override
    public List<BigDecimal> find96WeatherCityFcValue(Date date, String cityId, Integer type) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityFcDO> weatherCityFcVOs = this.findWeatherCityFcDOs(cityId, type, date, date);
        if (!CollectionUtils.isEmpty(weatherCityFcVOs)) {
            return BasePeriodUtils.toList(weatherCityFcVOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    @Override
    public List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception {
        List<BigDecimal> points = new ArrayList<>();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : dates) {
            List<BigDecimal> value = find96WeatherCityFcValue(date, cityId, type);
            if (value != null && value.size()>0){
                points.addAll(value);
            }else {
                points.addAll(DataUtil.getNullList(Constants.WEATHER_CURVE_POINT_NUM));
            }
        }
        return points;
    }


    @Override
    public WeatherDeatilDTO findWeatherDeatilDTO(Date date, String cityId) throws Exception {

        List<WeatherCityFcDO> weatherCityFcVOs = this.findWeatherCityFcDOs(cityId, null, date, date);
        Map<Integer, WeatherCityFcDO> weatherMap = weatherCityFcVOs.stream()
            .collect(Collectors.toMap(WeatherCityFcDO::getType, Function.identity()));
        String weatherCityId = cityService.findWeatherCityId(cityId);

        List<WeatherCityFcModifyDO> modifyWeatherByDateAndType = weatherCityFcModifyService
            .findModifyWeatherByDateAndType(new SimpleDateFormat("yyyy-MM-dd").format(date), null, weatherCityId);
        Map<Integer, List<WeatherCityFcModifyDO>> modifyWeather = null;
        if (!CollectionUtils.isEmpty(modifyWeatherByDateAndType)) {
            modifyWeather = modifyWeatherByDateAndType.stream()
                .collect(Collectors.groupingBy(WeatherCityFcModifyDO::getType));
        }

        List<WeatherCurveDTOList> weatherCurveDTOListList = new ArrayList<>();
        List<WeatherFeatureDTO> weatherFeatureDTOS = new ArrayList<WeatherFeatureDTO>() {
            {
                add(new WeatherFeatureDTO("气象源"));
                add(new WeatherFeatureDTO("人工调整"));
            }
        };
        for (WeatherEnum weatherEnum : WeatherEnum.values()) {
            if (weatherEnum.getType() > 4) {
                continue;
            }
            WeatherCityFcDO fcVO = weatherMap.get(weatherEnum.getType());
            if (fcVO != null) {
                List<BigDecimal> weatherFcModifyValues = null;
                List<BigDecimal> weatherFcValues = BasePeriodUtils
                    .toList(fcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                if (modifyWeather == null || CollectionUtils.isEmpty(modifyWeather.get(weatherEnum.getType()))) {
                    weatherFcModifyValues = weatherFcValues;
                } else {
                    List<WeatherCityFcModifyDO> WeatherCityFcModifyDOS = modifyWeather.get(weatherEnum.getType());
                    if(WeatherCityFcModifyDOS.get(0) == null){
                        weatherFcModifyValues = weatherFcValues;
                    } else {
                        weatherFcModifyValues = BasePeriodUtils
                            .toList(WeatherCityFcModifyDOS.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO);
                    }
                }

                List<WeatherCurveDTO> weatherCurveDTOS = new ArrayList<>();
                WeatherCurveDTO weatherFcDTO = new WeatherCurveDTO("101", weatherFcValues);
                weatherCurveDTOS.add(weatherFcDTO);
                WeatherCurveDTO weatherModifyDTO = new WeatherCurveDTO("102", weatherFcModifyValues);
                weatherCurveDTOS.add(weatherModifyDTO);
                WeatherCurveDTOList weatherCurveDTOList = new WeatherCurveDTOList(String.valueOf(weatherEnum.getType()),
                    weatherCurveDTOS);
                weatherCurveDTOListList.add(weatherCurveDTOList);

                if (weatherEnum.equals(WeatherEnum.TEMPERATURE)) {
                    weatherFeatureDTOS.get(0).setHighestTemperature(LoadCalUtil.max(weatherFcValues));
                    weatherFeatureDTOS.get(0).setLowestTemperature(LoadCalUtil.min(weatherFcValues));

                    weatherFeatureDTOS.get(1).setHighestTemperature(LoadCalUtil.max(weatherFcModifyValues));
                    weatherFeatureDTOS.get(1).setLowestTemperature(LoadCalUtil.min(weatherFcModifyValues));
                }
                if (weatherEnum.equals(WeatherEnum.RAINFALL)) {
                    weatherFeatureDTOS.get(0).setRainfall(BigDecimalUtils.addAllValue(weatherFcValues));
                    weatherFeatureDTOS.get(1).setRainfall(BigDecimalUtils.addAllValue(weatherFcModifyValues));
                }
                if (weatherEnum.equals(WeatherEnum.HUMIDITY)) {
                    weatherFeatureDTOS.get(0)
                        .setAveHumidity(BasePeriodUtils.getMaxMinAvg(weatherFcValues, 4).get(BasePeriodUtils.LOAD_AVG));
                    weatherFeatureDTOS.get(1).setAveHumidity(
                        BasePeriodUtils.getMaxMinAvg(weatherFcModifyValues, 4).get(BasePeriodUtils.LOAD_AVG));
                }
            }
        }

        return new WeatherDeatilDTO(weatherCurveDTOListList, weatherFeatureDTOS);
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Date date, Date date1) throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, date, date);
    }

    @Override
    public void doInsertOrUpdateBatch(List<WeatherCityFcDO> updateD0List) {
        if (!CollectionUtils.isEmpty(updateD0List)) {
            updateD0List.forEach(updateD0 -> {
                try {
                    this.doInsertOrUpdate(updateD0);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }

    @Override
    public List<WeatherCityFcCopy1DO> findWeatherCityFcDOs1(String cityId, Date date, Date date1,Integer type,String orderId) throws Exception {
        return weatherCityFcCopy1DAO.findWeatherCityFcDO(cityId, type,date, date,orderId);
    }

    @Override
    public List<WeatherCityFcCopy2DO> findWeatherCityFcDOs2(String cityId, Date date, Date date1,Integer type) throws Exception {
        return weatherCityFcCopy2DAO.findWeatherCityFcDO(cityId, date, date,type);
    }

    @Override
    public List<WeatherCityFcDO> getWeatherCityFcDOList(List<String> cityIds, Integer type, Date startDate, Date endDate) throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDOList(cityIds,type,startDate,endDate);
    }

    @Override
    public List<WeatherCityFcDO> queryWeatherCityHisDOAllTypeList(String weatherCityId, List<Integer> typeList, List<Date> dateList) {
        return weatherCityFcMapper.selectList(
            new QueryWrapper<WeatherCityFcDO>().lambda()
                .eq(StringUtils.isNotBlank(weatherCityId), WeatherCityFcDO::getCityId, weatherCityId)
                .in(!CollectionUtils.isEmpty(typeList), WeatherCityFcDO::getType, typeList)
                .in(!CollectionUtils.isEmpty(dateList), WeatherCityFcDO::getDate, dateList)
        );
    }


    private Map<String, List<WeatherFcCollectDTO>> weatherFcCollectVOListToDTOMap(
        List<WeatherFcCollectVO> weatherFcCollectVOS) {
        Map<String, List<WeatherFcCollectDTO>> map = new HashMap<String, List<WeatherFcCollectDTO>>();
        for (WeatherFcCollectVO weatherFcCollectVO : weatherFcCollectVOS) {
            Date date = DateUtils.addHours(weatherFcCollectVO.getForecastStartTime(), weatherFcCollectVO.getTimes());
            String time = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            if (map.get(time) == null) {
                map.put(time, new ArrayList<WeatherFcCollectDTO>());
            }
            WeatherFcCollectDTO weatherFcCollectDTO = new WeatherFcCollectDTO();
            weatherFcCollectDTO.setDate(date);
            weatherFcCollectDTO.setHumidity(weatherFcCollectVO.getHumidity());
            weatherFcCollectDTO.setRainfall(weatherFcCollectVO.getRainfall());
            weatherFcCollectDTO.setStationNo(weatherFcCollectVO.getStationNo());
            weatherFcCollectDTO.setTemperature(weatherFcCollectVO.getTemperature());
            weatherFcCollectDTO.setWindSpeed(weatherFcCollectVO.getWindSpeed());
            map.get(time).add(weatherFcCollectDTO);
        }
        return map;
    }

    @Override
    public List<WeatherCityFcDO>  findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        return null;
    }

    @Override
    public void doSaveOrUpdateList(List<WeatherCityFcDO> results) {
        for(WeatherCityFcDO result:results) {
            weatherCityFcDAO.saveOrUpdateByTemplate(result);
        }
    }

    /**
     * 获取日前一天n小时前最新的批次数据
     * @param cityId
     * @param date
     * @param type
     * @param hour
     * @return
     */
    @SneakyThrows
    @Override
    public List<WeatherCityFcCopy1DO> findBatchWeatherFcByHour(String cityId, Date date, Integer type, String hour) {
        // 1. 获取前一天日期
        String dateStr = DateUtils.date2String(DateUtils.addDays(date, -1), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);

        // 2. 构造目标 orderId 前缀
        String targetOrderIdPrefix = dateStr + hour; // 例如：2023101508

        // 3. 查询数据
        List<WeatherCityFcCopy1DO> weatherCityFcDOList;
        if (Constants.PROVINCE_ID.equals(cityId)) {
            // 如果 cityId 为 1，查询所有城市的数据
            weatherCityFcDOList = weatherCityFcCopy1DAO.findWeatherCityFcDO(null, null, date, date, null);
        } else {
            // 否则，查询指定城市的数据
            weatherCityFcDOList = weatherCityFcCopy1DAO.findWeatherCityFcDO(cityId, null, date, date, null);
        }

        // 4. 按 cityId 和 type 分组
        Map<String, Map<Integer, List<WeatherCityFcCopy1DO>>> cityAndTypeGroupMap = weatherCityFcDOList.stream()
                .collect(Collectors.groupingBy(
                        WeatherCityFcCopy1DO::getCityId,
                        Collectors.groupingBy(WeatherCityFcCopy1DO::getType)
                ));

        // 5. 过滤每个城市和 type 的数据
        List<WeatherCityFcCopy1DO> result = new ArrayList<>();
        for (Map.Entry<String, Map<Integer, List<WeatherCityFcCopy1DO>>> cityEntry : cityAndTypeGroupMap.entrySet()) {
            Map<Integer, List<WeatherCityFcCopy1DO>> typeGroupMap = cityEntry.getValue();

            for (Map.Entry<Integer, List<WeatherCityFcCopy1DO>> typeEntry : typeGroupMap.entrySet()) {
                List<WeatherCityFcCopy1DO> cityDataList = typeEntry.getValue();

                // 过滤逻辑：筛选出 orderId 小于 targetOrderIdPrefix 的记录，并按 orderId 降序排序取第一条
                WeatherCityFcCopy1DO latestRecord = cityDataList.stream()
                        .filter(data -> {
                            String orderId = data.getOrderId();
                            return orderId.compareTo(targetOrderIdPrefix) < 0;
                        })
                        .max(Comparator.comparing(WeatherCityFcCopy1DO::getOrderId))
                        .orElse(null);

                if (latestRecord != null) {
                    result.add(latestRecord);
                }
            }
        }

        // 6. 如果是省份 ID，统计全省数据
        if (Constants.PROVINCE_ID.equals(cityId)) {
            List<WeatherCityFcDO> weatherCityFcDO = statProvinceWeather(result);
            result = weatherCityFcDO.stream().map(
                    src -> {
                        WeatherCityFcCopy1DO weatherCityFcCopy1DO = new WeatherCityFcCopy1DO();
                        BeanUtils.copyProperties(src, weatherCityFcCopy1DO);
                        return weatherCityFcCopy1DO;
                    }
            ).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<WeatherCityFcCopy1DO> findBatchWeatherFcByHourData(String cityId, Date startDate, Date endDate, Integer type, String hour) throws Exception {
        List<WeatherCityFcCopy1DO> weatherCityFcDOList;
        if (Constants.PROVINCE_ID.equals(cityId)) {
            weatherCityFcDOList = weatherCityFcCopy1DAO.findWeatherCityFcDOS(Arrays.asList("2","4","7","9","10","12"), type, startDate, endDate, null);
        } else {
            weatherCityFcDOList = weatherCityFcCopy1DAO.findWeatherCityFcDO(cityId, type, startDate, endDate, null);
        }
        Map<String, List<WeatherCityFcCopy1DO>> cityAndTypeGroupMap = weatherCityFcDOList.stream().collect(Collectors.groupingBy(t -> t.getCityId() + "-" + t.getType() + "-" + DateUtils.date2String(t.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)));
        List<WeatherCityFcCopy1DO> result = new ArrayList<>();
        for (Map.Entry<String, List<WeatherCityFcCopy1DO>> stringListEntry : cityAndTypeGroupMap.entrySet()) {
            List<WeatherCityFcCopy1DO> value = stringListEntry.getValue();
            if (!CollectionUtils.isEmpty(value)) {
                for (Map.Entry<java.sql.Date, List<WeatherCityFcCopy1DO>> dateListEntry : value.stream().collect(Collectors.groupingBy(t -> t.getDate())).entrySet()) {
                    java.sql.Date key = dateListEntry.getKey();
                    String yesDate = DateUtils.date2String(DateUtils.addDays(key, -1), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                    String keyOrder = yesDate + hour;
                    List<WeatherCityFcCopy1DO> value1 = dateListEntry.getValue();
                    WeatherCityFcCopy1DO latestRecord = value1.stream()
                            .filter(data -> {
                                String orderId = data.getOrderId();
                                return orderId.compareTo(keyOrder) < 0;
                            })
                            .max(Comparator.comparing(WeatherCityFcCopy1DO::getOrderId))
                            .orElse(null);
                    if (latestRecord != null) {
                        result.add(latestRecord);
                    }
                }
            }
        }
        if (Constants.PROVINCE_ID.equals(cityId)) {
            List<WeatherCityFcDO> weatherCityFcDO = statProvinceWeather(result);
            result = weatherCityFcDO.stream().map(
                    src -> {
                        WeatherCityFcCopy1DO weatherCityFcCopy1DO = new WeatherCityFcCopy1DO();
                        BeanUtils.copyProperties(src, weatherCityFcCopy1DO);
                        return weatherCityFcCopy1DO;
                    }
            ).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<WeatherCityFcCopy1DO> findBatchWeatherFcByHourAllData(String cityId, Date startDate, Date endDate, Integer type, String hour) throws Exception {
        List<WeatherCityFcCopy1DO> weatherCityFcDO = weatherCityFcCopy1DAO.findWeatherCityFcDO(cityId, type, startDate, endDate, null);
        return weatherCityFcDO;
    }

    @SneakyThrows
    public List<WeatherCityFcDO> statProvinceWeather(List<? extends BaseWeatherDO> weathers){
        List<WeatherCityFcDO> results = new ArrayList<>();
        Map<String, List<BaseWeatherDO>> dateWeatherMap = weathers.stream()
                .collect(Collectors.groupingBy(t -> DateUtil.formateDate(t.getDate()) + Constants.SEPARATOR_PUNCTUATION + t.getType()));
        Map<String, String> cityMap = cityService.findAllCitys().stream()
                .collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
        dateWeatherMap.forEach((key, weatherDOS) -> {
            if (!org.springframework.util.CollectionUtils.isEmpty(weatherDOS)) {
                try {
                    List<BigDecimal> resultValues = new ArrayList<>();
                    for (BaseWeatherDO baseWeatherDO : weatherDOS) {
                        if (baseWeatherDO.getCityId().equals(cityMap.get("武汉"))) {
                            List<BigDecimal> weatherValues = BasePeriodUtils
                                    .toList(baseWeatherDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                            weatherValues.add(baseWeatherDO.getT2400());
                            //log.info("武汉原始数据：" + weatherValues.toString());
                            List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.33333333));
                            //log.info("武汉*0.33333333后的结果：" + values.toString());
                            resultValues = DataUtil.listAdd(resultValues, values);
                        } else if (baseWeatherDO.getCityId().equals(cityMap.get("黄冈"))) {
                            List<BigDecimal> weatherValues = BasePeriodUtils.toList(baseWeatherDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                            weatherValues.add(baseWeatherDO.getT2400());
                            //log.info("黄冈原始数据：" + weatherValues.toString());
                            List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.15896188));
                            //log.info("黄冈*0.15896188后的结果：" + values.toString());
                            resultValues = DataUtil.listAdd(resultValues, values);
                        } else if (baseWeatherDO.getCityId().equals(cityMap.get("襄阳"))) {
                            List<BigDecimal> weatherValues = BasePeriodUtils.toList(baseWeatherDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                            weatherValues.add(baseWeatherDO.getT2400());
                            //log.info("襄阳原始数据：" + weatherValues.toString());
                            List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.14220059));
                            //log.info("襄阳*0.14220059后的结果：" + values.toString());
                            resultValues = DataUtil.listAdd(resultValues, values);
                        } else if (baseWeatherDO.getCityId().equals(cityMap.get("荆州"))) {
                            List<BigDecimal> weatherValues = BasePeriodUtils.toList(baseWeatherDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                            weatherValues.add(baseWeatherDO.getT2400());
                            //log.info("荆州原始数据：" + weatherValues.toString());
                            List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.14138956));
                            //log.info("荆州*0.14138956后的结果：" + values.toString());
                            resultValues = DataUtil.listAdd(resultValues, values);
                        } else if (baseWeatherDO.getCityId().equals(cityMap.get("孝感"))) {
                            List<BigDecimal> weatherValues = BasePeriodUtils.toList(baseWeatherDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                            weatherValues.add(baseWeatherDO.getT2400());
                            //log.info("孝感原始数据：" + weatherValues.toString());
                            List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.1154366));
                            //log.info("孝感*0.1154366后的结果：" + values.toString());
                            resultValues = DataUtil.listAdd(resultValues, values);
                        } else if (baseWeatherDO.getCityId().equals(cityMap.get("宜昌"))) {
                            List<BigDecimal> weatherValues = BasePeriodUtils.toList(baseWeatherDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                            weatherValues.add(baseWeatherDO.getT2400());
                            //log.info("宜昌原始数据：" + weatherValues.toString());
                            List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.10867802));
                            //log.info("宜昌*0.10867802后的结果：" + values.toString());
                            resultValues = DataUtil.listAdd(resultValues, values);
                        }
                    }

                    if (!org.springframework.util.CollectionUtils.isEmpty(resultValues)) {
                        //log.info("湖北最后的结果：" + resultValues.toString());
                        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                        weatherCityFcDO.setType(WeatherEnum.TEMPERATURE.getType());
                        String[] split = key.split(Constants.SEPARATOR_PUNCTUATION);
                        weatherCityFcDO.setType(Integer.valueOf(split[1]));
                        weatherCityFcDO.setDate(new java.sql.Date(DateUtil.getDate(split[0], "yyyy-MM-dd").getTime()));
                        weatherCityFcDO.setCityId(CityConstants.PROVINCE_ID);
                        Map<String, BigDecimal> decimalMap = ColumnUtil
                                .listToMap(resultValues.subList(0, 96), true);
                        BasePeriodUtils.setAllFiled(weatherCityFcDO, decimalMap);
                        weatherCityFcDO.setT2400(resultValues.get(96));
                        results.add(weatherCityFcDO);
                    }
                } catch (Exception e) {
                    log.error("装配省调气象异常", e);
                }
            }
        });
        return results;
    }
}
