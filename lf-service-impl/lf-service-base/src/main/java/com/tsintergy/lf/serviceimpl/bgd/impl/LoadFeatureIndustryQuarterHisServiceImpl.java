package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryQuarterHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryMonthHisServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryQuarterHisServiceDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadFeatureIndustryQuarterHisServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("loadFeatureIndustryQuarterHisService")
public class LoadFeatureIndustryQuarterHisServiceImpl extends BaseFacadeServiceImpl implements LoadFeatureIndustryQuarterHisService {

    @Autowired
    private LoadFeatureIndustryQuarterHisServiceDAO loadFeatureIndustryQuarterHisServiceDAO;

    @Override
    public List<LoadFeatureIndustryQuarterHisServiceDO> getLoadFeatureIndustryQuarterHisServiceList(String cityId, String caliberId, String year, String quarter) {
        return loadFeatureIndustryQuarterHisServiceDAO.findAll(JpaWrappers.<LoadFeatureIndustryQuarterHisServiceDO>lambdaQuery()
        .eq(LoadFeatureIndustryQuarterHisServiceDO::getYear,year)
        .eq(!StringUtils.isEmpty(quarter), LoadFeatureIndustryQuarterHisServiceDO::getQuarter,quarter)
        .eq(!StringUtils.isEmpty(caliberId), LoadFeatureIndustryQuarterHisServiceDO::getCaliberId,caliberId)
        .eq(LoadFeatureIndustryQuarterHisServiceDO::getCityId,cityId));
    }

    @Override
    public List<LoadFeatureIndustryQuarterHisServiceDO> getLoadFeatureIndustryQuarterHisServiceAllList(String cityId, String industryId, String startYear, String endYear) {
        List<LoadFeatureIndustryQuarterHisServiceDO> all = loadFeatureIndustryQuarterHisServiceDAO.findAll(JpaWrappers.<LoadFeatureIndustryMonthHisServiceDO>lambdaQuery()
                .ge(LoadFeatureIndustryMonthHisServiceDO::getYear, startYear)
                .le(LoadFeatureIndustryMonthHisServiceDO::getYear, endYear)
                .eq(LoadFeatureIndustryMonthHisServiceDO::getCityId, cityId)
                .eq(LoadFeatureIndustryMonthHisServiceDO::getType, industryId));
        return all;
    }

    @Override
    public void saveOrUpdate(LoadFeatureIndustryQuarterHisServiceDO loadFeatureIndustryQuarterHisServiceDO) {
        List<LoadFeatureIndustryQuarterHisServiceDO> list = loadFeatureIndustryQuarterHisServiceDAO.findAll(
                JpaWrappers.<LoadFeatureIndustryQuarterHisServiceDO>lambdaQuery()
                        .eq(LoadFeatureIndustryQuarterHisServiceDO::getCityId, loadFeatureIndustryQuarterHisServiceDO.getCityId())
                        .eq(LoadFeatureIndustryQuarterHisServiceDO::getType, loadFeatureIndustryQuarterHisServiceDO.getType())
                        .eq(LoadFeatureIndustryQuarterHisServiceDO::getQuarter, loadFeatureIndustryQuarterHisServiceDO.getQuarter())
                        .eq(LoadFeatureIndustryQuarterHisServiceDO::getYear, loadFeatureIndustryQuarterHisServiceDO.getYear())
        );

        if (CollectionUtils.isEmpty(list)) {
            loadFeatureIndustryQuarterHisServiceDAO.save(loadFeatureIndustryQuarterHisServiceDO);
        } else {
            LoadFeatureIndustryQuarterHisServiceDO loadFeatureEveryIndustryDO = list.get(0);
            loadFeatureIndustryQuarterHisServiceDO.setId(loadFeatureEveryIndustryDO.getId());
            loadFeatureIndustryQuarterHisServiceDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadFeatureIndustryQuarterHisServiceDAO.saveOrUpdateByTemplate(loadFeatureIndustryQuarterHisServiceDO);
        }
    }

    @Override
    public List<LoadFeatureIndustryQuarterHisServiceDO> getLoadFeatureIndustryQuarterHisServiceListByType(String cityId, String type, String year, String quarter) {
        return loadFeatureIndustryQuarterHisServiceDAO.findAll(JpaWrappers.<LoadFeatureIndustryQuarterHisServiceDO>lambdaQuery()
                .eq(LoadFeatureIndustryQuarterHisServiceDO::getYear,year)
                .eq(!StringUtils.isEmpty(quarter), LoadFeatureIndustryQuarterHisServiceDO::getQuarter,quarter)
                .eq(!StringUtils.isEmpty(type), LoadFeatureIndustryQuarterHisServiceDO::getType,type)
                .eq(LoadFeatureIndustryQuarterHisServiceDO::getCityId,cityId));
    }

    @Override
    public List<LoadFeatureIndustryQuarterHisServiceDO> getLoadFeatureIndustryQuarterHisServiceList(String cityId, String type, Date startDate, Date endDate) {
        String startYear = DateUtil.getYearByDate(startDate);
        String endYear = DateUtil.getYearByDate(endDate);
        List<LoadFeatureIndustryQuarterHisServiceDO> featureQuarterList = loadFeatureIndustryQuarterHisServiceDAO.findAll(JpaWrappers.<LoadFeatureIndustryMonthHisServiceDO>lambdaQuery()
                .ge(LoadFeatureIndustryMonthHisServiceDO::getYear, startYear)
                .le(LoadFeatureIndustryMonthHisServiceDO::getYear, endYear)
                .eq(LoadFeatureIndustryMonthHisServiceDO::getCityId, cityId)
                .eq(LoadFeatureIndustryMonthHisServiceDO::getType, type)
        );
        if (!CollectionUtils.isEmpty(featureQuarterList)) {
            return  featureQuarterList.stream().filter(featureQuarter ->
                    !startDate.after(DateUtil.getFirstDayDateOfOfQuarter(Integer.parseInt(featureQuarter.getYear()), Integer.parseInt(featureQuarter.getQuarter()))) &&
                    !endDate.before(DateUtil.getLastDayOfQuarter(Integer.parseInt(featureQuarter.getYear()), Integer.parseInt(featureQuarter.getQuarter())))).collect(Collectors.toList());
        }
        return null;
    }
}
