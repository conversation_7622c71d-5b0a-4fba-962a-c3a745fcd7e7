
package com.tsintergy.lf.serviceimpl.assess.impl;

import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessUnitDTO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceimpl.assess.dao.SettingAssessDAO;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 考核点设置
 *
 * <AUTHOR>
 */
@Service("settingAssessService")
public class SettingAssessServiceImpl implements SettingAssessService {

    @Resource
    private SettingAssessDAO settingAssessDAO;

    @Override
    public List<SettingAssessDataDTO> findAssessDataList(String year, String caliberId) {
        List<SettingAssessDataDTO> resultList = new ArrayList<>();
        List<SettingAssessDO> settingAssessDataS = this.settingAssessDAO.selectListByYear(year, caliberId, null);
        Map<String, List<SettingAssessDO>> listMap = settingAssessDataS.stream()
            .collect(Collectors.groupingBy(SettingAssessDO::getAssessName));
        for (Map.Entry<String, List<SettingAssessDO>> entry : listMap.entrySet()) {
            SettingAssessDataDTO result = new SettingAssessDataDTO();
            List<SettingAssessDO> assessDOList = entry.getValue();
            result.setAssessName(assessDOList.get(0).getAssessName());
            result.setValid(assessDOList.get(0).getValid());
            List<SettingAssessUnitDTO> unitDTOS = new ArrayList<>();
            assessDOList.forEach(src -> {
                SettingAssessUnitDTO unit = new SettingAssessUnitDTO();
                BeanUtils.copyProperties(src, unit);
                unitDTOS.add(unit);
            });
            result.setDate(new Date(assessDOList.get(0).getCreatetime().getTime()));
            result.setUnitList(unitDTOS);
            resultList.add(result);
        }
        resultList.sort(Comparator.comparing(SettingAssessDataDTO::getDate));
        return resultList;
    }

    @Override
    public List<SettingAssessDO> findAssessByDataList(String year, String month, String caliberId,
                                                      String assessName) {
        List<SettingAssessDO> settingAssessDOS = this.settingAssessDAO.selectListDataByName(year, month, caliberId,
                assessName);
        return settingAssessDOS;
    }

    @Override
    public void doSaveOrUpdateAssess(List<SettingAssessDataDTO> dataList) {
        dataList.forEach(data -> {
            List<SettingAssessUnitDTO> unitList = data.getUnitList();
            for (SettingAssessUnitDTO unit : unitList) {
                SettingAssessDO toSave = new SettingAssessDO();
                BeanUtils.copyProperties(unit, toSave);
                toSave.setAssessName(data.getAssessName());
                toSave.setValid(data.getValid());
                //排序默认值1
                toSave.setSort(1);
                settingAssessDAO.saveOrUpdateEntityByTemplate(toSave);
            }
        });
    }


    @Override
    public void doDeleteByAssessName(String year, String caliberId, String assessName) {
        List<SettingAssessDO> settingAssessDOS = this.settingAssessDAO.selectListByName(year, caliberId, assessName);
        for (SettingAssessDO src : settingAssessDOS) {
            settingAssessDAO.delete(src);
        }
    }

    @Override
    public List<String> findAllValidAssess(String year, String caliberId) {
        List<SettingAssessDO> assessDataList = this.settingAssessDAO.selectListByYear(year, caliberId, true);
        if (CollectionUtils.isEmpty(assessDataList)) {
            return null;
        }
        List<String> stringList = assessDataList.stream().map(SettingAssessDO::getAssessName)
            .collect(Collectors.toList());
        return stringList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean checkNameAvailable(String year, String caliberId, String assessName) {
        List<SettingAssessDO> settingAssessDOS = this.settingAssessDAO.selectListByName(year, caliberId, assessName);
        if (CollectionUtils.isEmpty(settingAssessDOS)) {
            return true;
        }
        return false;
    }

    @Override
    public Map<String, List<SettingAssessDO>> findAssessSettingByData(Date startDate, Date endDate, String caliberId) {
        List<SettingAssessDO> assessDOS = settingAssessDAO.selectListByStartEndValid(startDate, endDate, caliberId, true);
        if (CollectionUtils.isEmpty(assessDOS)) {
            return null;
        }
        return assessDOS.stream()
            .collect(Collectors.groupingBy(src -> src.getYear() + "-" + src.getMonth()));
    }
}
