package com.tsintergy.lf.serviceimpl.forecast.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.dao.type.hibernate.DeltaUnit;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.component.cache.service.RedisCacheUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ultraShort.UltraShortMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ultraShort.UltraShortParam;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ultraShort.UltraShortResult;
import com.tsintergy.lf.core.constants.*;
import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.core.properties.AiAlgorithmConfigProperties;
import com.tsintergy.lf.core.util.*;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.api.UltraGuangxiForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.*;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingCompositeAccuracyService;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.ForecastCommentType;
import com.tsintergy.lf.serviceapi.base.common.enumeration.SimilarityCommentType;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyCompositeService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DeviationLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DeviationLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcShortService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadDecomposeCityWeekService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadDecomposeCityWeekStabilityService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadDecomposeCityWeekDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadDecomposeCityWeekStabilityDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.AccessStatisResultDTO;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityFcUltraBatchService;
import com.tsintergy.lf.serviceapi.base.ultra.enums.UltraMultipointFifteenEnum;
import com.tsintergy.lf.serviceapi.base.ultra.enums.UltraMultipointFiveEnum;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityFcUltraBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.base.dao.HolidayDAO;
import com.tsintergy.lf.serviceimpl.common.util.AlgorithmUtil;
import com.tsintergy.lf.serviceimpl.common.util.WeatherCalcUtil;
import com.tsintergy.lf.serviceimpl.datamanage.dao.InterfaceInfoDAO;
import com.tsintergy.lf.serviceimpl.enums.AssessEnum;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyLoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.DeviationLoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.ForecastInfoDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.ForecastLoadDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayHisDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisDAO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okio.BufferedSource;
import okio.Okio;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 预测服务 User:taojingui Date:18-2-10 Time:下午2:23
 */
@Service("forecastService")
@Slf4j
public class ForecastServiceImpl extends BaseServiceImpl implements ForecastService {


    public static BigDecimal CONFIDENCE;
    public static BigDecimal RANGE;
    //超短期多点是短曲线入库是否是连续曲线 true  连续 false  不连续
    private static Boolean continuousCurveFlag = false;

    static {
        CONFIDENCE = BigDecimal.valueOf(Double.valueOf(0.95));
        RANGE = BigDecimal.valueOf(Double.valueOf(0.001));
    }

    private static final String BAOGONG_KEY = "保供";
    private static final String NOON_KEY = "午间";
    private static final String NIGHT_KEY = "夜间";

    public static OkHttpClient client = new OkHttpClient();

    @Autowired
    AccuracyLoadCityFcDAO accuracyLoadCityFcDAO;
    @Autowired
    StatisticsCityDayFcDAO statisticsCityDayFcDAO;
    @Autowired
    LoadFeatureCityDayHisDAO loadFeatureCityDayHisDAO;
    @Autowired
    LoadCityHisDAO loadCityHisDAO;
    @Autowired
    DeviationLoadCityFcDAO deviationLoadCityFcDAO;
    @Autowired
    SettingSystemService systemService;
    @Autowired
    AlgorithmService algorithmService;
    @Autowired
    InterfaceInfoDAO interfaceInfoDAO;
    @Autowired
    HolidayDAO holidayDAO;
    @Autowired
    WeatherFeatureCityDayHisDAO weatherFeatureCityDayHisDAO;
    @Autowired
    WeatherFeatureCityDayFcDAO weatherFeatureCityDayFcDAO;
    @Autowired
    ForecastInfoService forecastInfoService;
    @Autowired
    ForecastLoadDAO forecastLoadDAO;
    @Autowired
    CityService cityService;
    @Autowired
    DeviationLoadCityFcService deviationLoadCityFcService;
    @Autowired
    LoadCityFcService loadCityFcService;
    @Autowired
    LoadCityFcUltraService loadCityFcUltraService;
    @Autowired
    ForecastInfoDAO forecastInfoDAO;
    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    RedisService redisService;
    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;
    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;
    @Autowired
    UltraGuangxiForecastService ultraGuangxiForecastService;
    @Autowired
    LoadCityFcUltraBatchService loadCityFcUltraBatchService;
    @Autowired
    private LoadCityFcShortService loadCityFcShortService;
    @Autowired
    private LoadDecomposeCityWeekService loadDecomposeCityWeekService;
    @Autowired
    private LoadDecomposeCityWeekStabilityService loadDecomposeCityWeekStabilityService;
    @Autowired
    private CustomizationForecastService<PreProcessParam, GeneralResult> preProcessService;
    @Autowired
    private CustomizationForecastService<HolidayParam, GeneralResult> holidayForecastService;
    @Autowired
    private CustomizationForecastService<TyphoonParam, GeneralResult> typhoonForecast;
    @Autowired
    private CustomizationForecastService<AnalysisParam, AnalysisResult> analysisForecastService;
    @Autowired
    private CustomizationForecastService<ShortForecastParam, Result> shortForecastService;
    @Autowired
    private CustomizationForecastService<SensitivityParam, SensitivityResult> sensitivityForecastService;
    @Autowired
    private CustomizationForecastService<ForecastParam, GeneralResult> forecastable;
    @Autowired
    private LoadCityFcDAO loadCityFcDAO;

    @Autowired
    private AccuracyCompositeService accuracyCompositeService;

    @Autowired
    private SettingCompositeAccuracyService settingCompositeAccuracyService;

    @Autowired
    private AiAlgorithmConfigProperties aiAlgorithmConfigProperties;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcBmService weatherCityFcBmService;

    @Autowired
    private WeatherCityFcMeteoService weatherCityFcMeteoService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private SettingAssessService settingAssessService;

    @Autowired
    private AccuracyAssessService accuracyAssessService;


    @Override
    public List<BigDecimal> smoothLineVslf(List<BigDecimal> singleLoadCityVOList, String startTime,
                                           String endTime) throws Exception {
        if (startTime == null || endTime == null) {
            return AlgorithmUtil.smoothLine(singleLoadCityVOList, 5);
        }
        List<BigDecimal> result = new ArrayList<>();
        result.addAll(singleLoadCityVOList);
        int startPoint = VslfDateUtil.getTimePoint(startTime, singleLoadCityVOList.size());
        int endPoint = VslfDateUtil.getTimePoint(endTime, singleLoadCityVOList.size());
        List<BigDecimal> bigDecimals = AlgorithmUtil.smoothLine(singleLoadCityVOList, 5);
        for (int i = startPoint; i <= endPoint; i++) {
            result.set(i - 1, bigDecimals.get(i - 1));
        }
        return result;
    }

    @Override
    public LoadCityFcUltraDO creatFcDO(String cityId, String caliberId, Date date, Integer timeSpan, String algorithmId,
                                       List<BigDecimal> zeroOrNullList, Integer tarPointInBatch, IfEnum report, Boolean success) {
        int pointSize =
                DataPointListMeta.MINUTE_5 == timeSpan ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        LoadCityFcUltraDO fcDO = new LoadCityFcUltraDO();
        fcDO.setCityId(cityId);
        fcDO.setCaliberId(caliberId);
        fcDO.setAlgorithmId(algorithmId);
        fcDO.setDateTime(new java.sql.Date(date.getTime()));
        String dateTime = DateFormatUtils.format(fcDO.getDateTime(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue());
        Date startTime = null;
        if (UltraSystemUtils.startWithZero()) {
            startTime = com.tsintergy.aif.tool.core.utils.date.DateUtil.string2Date(dateTime + " 00:00:00", DateFormatType.DATE_FORMAT_STR);
        } else {
            startTime = com.tsintergy.aif.tool.core.utils.date.DateUtil
                    .string2Date(dateTime + " 00:" + (DataPointListMeta.MINUTE_5 == timeSpan ? "05" : "15") + ":00",
                            DateFormatType.DATE_FORMAT_STR);
        }
        fcDO.setTvMeta(DataPointListMeta.create(startTime, pointSize, timeSpan, DeltaUnit.MINUTES));
        fcDO.setTvData(zeroOrNullList);
        fcDO.setCreateTime(new Timestamp(System.currentTimeMillis()));
        fcDO.setMultipointType(tarPointInBatch);
        fcDO.setRecommend(false);
        fcDO.setReport(report);
        fcDO.setSucceed(success);
        return fcDO;
    }


    @Override
    public List<BigDecimal> doUltraForecast(String cityId, String caliberId, Integer timeSpan, Date startDate,
                                            Integer startTime, com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum algorithmEnum, String param)
            throws Exception {
        return doUltraGuangxiForecast(genParam(cityId, caliberId, timeSpan, startDate, startTime, algorithmEnum, param));
    }


    public List<BigDecimal> doUltraGuangxiForecast(UltraShortParam param) throws Exception {
        Date date = param.getStartDate();
        Integer timeSpan = param.getTimeUnit();
        Integer startTimePoint = param.getStartTime();
        String cityId = param.getCityId();
        String caliberId = param.getCaliberId();
        String algorithmId = param.getAlgorithmEnum().getId();
        String recommendAlgorithm;
        SystemData systemSetting = systemService.getSystemSetting();
        if (Constants.PROVINCE_ID.equals(cityId)) {
            recommendAlgorithm =
                    Constants.TIME_SPAN_MINUTE_5.equals(timeSpan) ? systemSetting.getProvinceUltraFiveAlgorithm()
                            : systemSetting.getProvinceUltraFifteenAlgorithm();
        } else {
            recommendAlgorithm =
                    Constants.TIME_SPAN_MINUTE_5.equals(timeSpan) ? systemSetting.getCityUltraFiveAlgorithm()
                            : systemSetting.getCityUltraFifteenAlgorithm();
        }
        List<String> columns = ColumnUtil.getColumns(
                Constants.TIME_SPAN_MINUTE_5.equals(timeSpan) ? Constants.NUM_288 : Constants.NUM_96,
                UltraSystemUtils.startWithZero(), false);
        String fcStartTime = columns.get(startTimePoint - 1);
        //系统设置的超短期预测的时长 默认4小时

        String hour;
        if (cityId.equals(Constants.PROVINCE_ID)) {
            if (Constants.TIME_SPAN_MINUTE_5.equals(timeSpan)) {
                hour = systemSetting.getProvinceShortFiveTime();
            } else {
                hour = systemSetting.getProvinceShortFifteenTime();
            }
        } else {
            if (Constants.TIME_SPAN_MINUTE_5.equals(timeSpan)) {
                hour = systemSetting.getCityShortFiveTime();
            } else {
                hour = systemSetting.getCityShortFifteenTime();
            }
        }
        Integer pointLen = Integer.valueOf(hour) * (60 / timeSpan);
        param.setPointLen(pointLen);
        //调用超短期算法
        param.setTimestamp(fcStartTime);
        UltraShortResult result = ultraGuangxiForecastService.forecast(param);
        if (result == null || result.getStatus() == UltraShortResult.STOP_FLAG) {
            return null;
        }
        List<UltraShortMetaData> forecastResultList = result.getForecastResultList();
        List<BigDecimal> dataList = new ArrayList<>();
        for (UltraShortMetaData ultraGuangxiMetaData : forecastResultList) {
            dataList.add(ultraGuangxiMetaData.getForecastObject());
        }

        LoadCityFcUltraBatchDO loadCityFcUltraBatchDO = new LoadCityFcUltraBatchDO();
        loadCityFcUltraBatchDO.setAlgorithmId(algorithmId);
        loadCityFcUltraBatchDO.setCaliberId(caliberId);
        loadCityFcUltraBatchDO.setCityId(cityId);
        loadCityFcUltraBatchDO.setDateTime(new java.sql.Date(date.getTime()));
        loadCityFcUltraBatchDO.setCreateTime(new Timestamp(System.currentTimeMillis()));
        loadCityFcUltraBatchDO.setStartTime(fcStartTime);

        String dateTime = DateFormatUtils
                .format(loadCityFcUltraBatchDO.getDateTime(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue());
        Date dateStart = null;
        if (UltraSystemUtils.startWithZero()) {
            dateStart = com.tsintergy.aif.tool.core.utils.date.DateUtil.string2Date(dateTime + " 00:00:00", DateFormatType.DATE_FORMAT_STR);
        } else {
            dateStart = com.tsintergy.aif.tool.core.utils.date.DateUtil
                    .string2Date(dateTime + " 00:" + (timeSpan.equals(Constants.TIME_SPAN_MINUTE_5) ? "05" : "15") + ":00",
                            DateFormatType.DATE_FORMAT_STR);
        }
        loadCityFcUltraBatchDO.setTvMeta(
                DataPointListMeta
                        .create(dateStart, timeSpan.equals(Constants.TIME_SPAN_MINUTE_5) ? Constants.NUM_288 : Constants.NUM_96,
                                timeSpan, DeltaUnit.MINUTES)
        );
        loadCityFcUltraBatchDO.setTvData(dataList);
        //算法结果保存到超短期数据记录表
        loadCityFcUltraBatchService.doSaveOrUpdate(loadCityFcUltraBatchDO);
        //推荐算法需要自动上报；
        if (recommendAlgorithm.equals(algorithmId)) {
            //判断推荐算法结果是否负荷自动上报逻辑。如果人工修正上报没有修改过，则自动预测的直接上报。
            List<BigDecimal> bigDecimals = checkIsAutoReport(param, dataList, columns.size());
            //人工上报曲线没有调整过
            if (bigDecimals == null) {
                //是用户选择的推荐算法时，直接自动上报
                saveUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                        new ArrayList<>(dataList), Integer.valueOf(hour), AlgorithmEnum.FORECAST_MODIFY.getId(),
                        Constants.ULTRA_FORECAST_1);
            } else {//上报曲线有调整；
                //上报填补了【人工划线】数据的list
                saveUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                        new ArrayList<>(bigDecimals), Integer.valueOf(hour), AlgorithmEnum.FORECAST_MODIFY.getId(),
                        Constants.ULTRA_FORECAST_1);
            }
        }
        //结果处理后保存最新的一点到fcBasic表 后面的点循环覆盖
        saveUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                new ArrayList<>(dataList), Integer.valueOf(hour), algorithmId, Constants.ULTRA_FORECAST_1);

        //5分钟or15分钟间隔保存其他点
        saveMultipointUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                new ArrayList<>(dataList), Integer.valueOf(hour),
                timeSpan == DataPointListMeta.MINUTE_5 ? UltraMultipointFiveEnum.ULTRA_FORECAST_3.getType()
                        : UltraMultipointFifteenEnum.ULTRA_FORECAST_2.getType(),
                algorithmId);
        saveMultipointUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                new ArrayList<>(dataList), Integer.valueOf(hour),
                timeSpan == DataPointListMeta.MINUTE_5 ? UltraMultipointFiveEnum.ULTRA_FORECAST_6.getType()
                        : UltraMultipointFifteenEnum.ULTRA_FORECAST_3.getType(),
                algorithmId);
        saveMultipointUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                new ArrayList<>(dataList), Integer.valueOf(hour),
                timeSpan == DataPointListMeta.MINUTE_5 ? UltraMultipointFiveEnum.ULTRA_FORECAST_9.getType()
                        : UltraMultipointFifteenEnum.ULTRA_FORECAST_4.getType(),
                algorithmId);
        saveMultipointUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                new ArrayList<>(dataList), Integer.valueOf(hour),
                timeSpan == DataPointListMeta.MINUTE_5 ? UltraMultipointFiveEnum.ULTRA_FORECAST_12.getType()
                        : UltraMultipointFifteenEnum.ULTRA_FORECAST_8.getType(),
                algorithmId);
        saveMultipointUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                new ArrayList<>(dataList), Integer.valueOf(hour),
                timeSpan == DataPointListMeta.MINUTE_5 ? UltraMultipointFiveEnum.ULTRA_FORECAST_24.getType()
                        : UltraMultipointFifteenEnum.ULTRA_FORECAST_16.getType(),
                algorithmId);
        saveMultipointUltraToFcBasic(cityId, caliberId, date, timeSpan, fcStartTime,
                new ArrayList<>(dataList), Integer.valueOf(hour),
                timeSpan == DataPointListMeta.MINUTE_5 ? UltraMultipointFiveEnum.ULTRA_FORECAST_48.getType() : null,
                algorithmId);
        return dataList;
    }

    /**
     * 5分钟时 多保存单一批次的其他时刻点数据到fc表中； 单一批次的目标时刻点：1   3   6 9    12   24   48，对应fc表的算法id记为：12 122 123 127  124 125 126
     * 这里处理除了1点的其他点数
     */
    private void saveMultipointUltraToFcBasic(String cityId, String caliberId, Date date, Integer timeSpan,
                                              String fcStartTime,
                                              List<BigDecimal> dataList,
                                              Integer hour, Integer tarPointInBatch, String algorithmId) throws Exception {
        if (tarPointInBatch == null) {
            return;
        }
        int pointSize =
                DataPointListMeta.MINUTE_5 == timeSpan ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        List<String> columns = ColumnUtil.getColumns(pointSize, UltraSystemUtils.startWithZero(), false);
        int index = 0;
        for (int i = 0; i < columns.size(); i++) {
            if (columns.get(i).equals(fcStartTime)) {
                index = i;
            }
        }
        index = index + tarPointInBatch - 1;
        int remain = pointSize - (index);
        if (remain >= ((60 / timeSpan) * hour)) {
            List<BigDecimal> tvData = ColumnUtil.getZeroOrNullList(pointSize, null);
            LoadCityFcUltraDO loadCityFcDO = loadCityFcUltraService
                    .getLoadCityFcDO(date, cityId, caliberId, algorithmId, timeSpan, tarPointInBatch);
            if (!Objects.isNull(loadCityFcDO)) {
                tvData = loadCityFcDO.getTvData();
            }

            if (continuousCurveFlag) {
                for (int i = 0; i < dataList.size() - tarPointInBatch + 1; i++) {
                    tvData.set(index + i, dataList.get(i + tarPointInBatch - 1));
                }
            } else {
                int i = 0;
                tvData.set(index + i, dataList.get(i + tarPointInBatch - 1));
            }
            saveDesignatedPoint(cityId, caliberId, date, timeSpan, algorithmId, tvData, tarPointInBatch);
        } else {
            if (remain > 0) {
                //预测出的数据包含跨天，存储今天数据
                List<BigDecimal> tvData = ColumnUtil.getZeroOrNullList(pointSize, null);
                ;
                LoadCityFcUltraDO loadCityFcDO = loadCityFcUltraService
                        .getLoadCityFcDO(date, cityId, caliberId, algorithmId, timeSpan, tarPointInBatch);
                if (!Objects.isNull(loadCityFcDO)) {
                    tvData = loadCityFcDO.getTvData();
                }
                if (continuousCurveFlag) {
                    for (int i = 0; i < remain; i++) {
                        int flag = i + tarPointInBatch - 1;
                        if (flag < dataList.size()) {
                            tvData.set(index + i, dataList.get(i + tarPointInBatch - 1));
                        }
                    }
                } else {
                    int i = 0;
                    int flag = i + tarPointInBatch - 1;
                    if (flag < dataList.size()) {
                        tvData.set(index + i, dataList.get(i + tarPointInBatch - 1));
                    }
                }
                //存储今日数据
                saveDesignatedPoint(cityId, caliberId, date, timeSpan, algorithmId, tvData, tarPointInBatch);
            } else {
                remain = -remain;
                List<BigDecimal> tvData = ColumnUtil.getZeroOrNullList(pointSize, null);
                ;
                LoadCityFcUltraDO loadCityFcDO = loadCityFcUltraService
                        .getLoadCityFcDO(org.apache.commons.lang3.time.DateUtils.addDays(date, 1), cityId, caliberId, algorithmId, timeSpan,
                                tarPointInBatch);
                if (!Objects.isNull(loadCityFcDO)) {
                    tvData = loadCityFcDO.getTvData();
                }
                tvData.set(remain, dataList.get(tarPointInBatch - 1));
                //存储明天的数据
                saveDesignatedPoint(cityId, caliberId, com.tsintergy.aif.tool.core.utils.date.DateUtil.addDays(date, 1), timeSpan, algorithmId, tvData,
                        tarPointInBatch);
            }
        }
    }


    private void saveUltraToFcBasic(String cityId, String caliberId, Date date, Integer timeSpan, String fcStartTime,
                                    List<BigDecimal> srcDataList,
                                    Integer hour, String algorithmId, Integer tarPointInBatch) throws Exception {
        List<BigDecimal> dataList = new ArrayList<>();
        dataList.addAll(srcDataList);
        int pointSize =
                DataPointListMeta.MINUTE_5 == timeSpan ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        List<String> columns = ColumnUtil.getColumns(pointSize, UltraSystemUtils.startWithZero(), false);
        int index = 0;
        for (int i = 0; i < columns.size(); i++) {
            if (columns.get(i).equals(fcStartTime)) {
                index = i;
            }
        }
        int remain = pointSize - (index);
        if (remain >= ((60 / timeSpan) * hour)) {
            List<BigDecimal> tvData = ColumnUtil.getZeroOrNullList(pointSize, null);
            ;
            LoadCityFcUltraDO loadCityFcDO = loadCityFcUltraService
                    .getLoadCityFcDO(date, cityId, caliberId, algorithmId, timeSpan, tarPointInBatch);
            if (!Objects.isNull(loadCityFcDO)) {
                tvData = loadCityFcDO.getTvData();
            }
            for (int i = 0; i < dataList.size(); i++) {
                tvData.set(index + i, dataList.get(i));
            }
            saveDesignatedPoint(cityId, caliberId, date, timeSpan, algorithmId, tvData, tarPointInBatch);
        } else {
            //预测出的数据包含跨天，存储今天数据
            List<BigDecimal> tvData = ColumnUtil.getZeroOrNullList(pointSize, null);
            ;
            LoadCityFcUltraDO loadCityFcDO = loadCityFcUltraService
                    .getLoadCityFcDO(date, cityId, caliberId, algorithmId, timeSpan, tarPointInBatch);
            if (!Objects.isNull(loadCityFcDO)) {
                tvData = loadCityFcDO.getTvData();
            }
            for (int i = 0; i < remain; i++) {
                tvData.set(index + i, dataList.get(i));
            }
            //存储今日数据
            saveDesignatedPoint(cityId, caliberId, date, timeSpan, algorithmId, tvData, tarPointInBatch);

            List<BigDecimal> tvDataTom = ColumnUtil.getZeroOrNullList(pointSize, null);
            ;
            LoadCityFcUltraDO loadCityFcTomDO = loadCityFcUltraService
                    .getLoadCityFcDO(com.tsintergy.aif.tool.core.utils.date.DateUtil.addDays(date, 1), cityId, caliberId, algorithmId, timeSpan, tarPointInBatch);
            if (!Objects.isNull(loadCityFcTomDO)) {
                tvDataTom = loadCityFcTomDO.getTvData();
            }
            //去除今日的数据
            for (int i = 0; i < remain; i++) {
                dataList.remove(0);
            }
            //填充明日数据到tomorrowList中
            for (int i = 0; i < dataList.size(); i++) {
                tvDataTom.set(i, dataList.get(i));
            }
            //存储明天的数据
            saveDesignatedPoint(cityId, caliberId, com.tsintergy.aif.tool.core.utils.date.DateUtil.addDays(date, 1), timeSpan,
                    algorithmId, tvDataTom, tarPointInBatch);
        }
    }

    private void saveDesignatedPoint(String cityId, String caliberId, Date date, Integer timeSpan, String algorithmId,
                                     List<BigDecimal> zeroOrNullList, Integer tarPointInBatch)
            throws Exception {
        LoadCityFcUltraDO loadCityFcUltraDO =
                creatFcDO(cityId, caliberId, date, timeSpan, algorithmId, zeroOrNullList, tarPointInBatch,
                        AlgorithmEnum.FORECAST_MODIFY.getId().equals(algorithmId) ? IfEnum.YES : IfEnum.NO,
                        AlgorithmEnum.FORECAST_MODIFY.getId().equals(algorithmId));
        this.loadCityFcUltraService.doSaveOrUpdateByTemplate(loadCityFcUltraDO);
    }

    /**
     * 判断是否与允许自动上报。如果人工修正上报修改过，则本次预测不上报。 null 没有修改过，可以自动上报。 List结果；返回处理后的新数据
     */
    private List<BigDecimal> checkIsAutoReport(UltraShortParam param, List<BigDecimal> srcList, Integer pointSize)
            throws Exception {
        List<BigDecimal> tarList = new ArrayList<>();
        List<BigDecimal> tarReportList = new ArrayList<>();
        List<BigDecimal> tarFcList = new ArrayList<>();
        tarList.addAll(srcList);
        Integer startTime = param.getStartTime() - 1;
        Integer pointLen = param.getPointLen();

        LoadCityFcUltraDO reportLoadCityFcDO = loadCityFcUltraService
                .getReportLoadCityFcDO(param.getStartDate(), param.getCityId(), param.getCaliberId(), IfEnum.YES,
                        param.getTimeUnit(), Constants.ULTRA_FORECAST_1);

        LoadCityFcUltraDO nextDayReportLoadCityFcDO = loadCityFcUltraService
                .getReportLoadCityFcDO(com.tsintergy.aif.tool.core.utils.date.DateUtil.addDays(param.getStartDate(), 1), param.getCityId(), param.getCaliberId(),
                        IfEnum.YES,
                        param.getTimeUnit(), Constants.ULTRA_FORECAST_1);
        LoadCityFcUltraDO loadFc = loadCityFcUltraService
                .findLoadFc(param.getCityId(), param.getCaliberId(), param.getAlgorithmEnum().getId(), param.getStartDate(),
                        param.getTimeUnit(),
                        null, Constants.ULTRA_FORECAST_1);
        if (reportLoadCityFcDO == null || loadFc == null) {
            return null;
        }
        LoadCityFcUltraDO nextDayLoadFc = loadCityFcUltraService
                .findLoadFc(param.getCityId(), param.getCaliberId(), param.getAlgorithmEnum().getId(),
                        com.tsintergy.aif.tool.core.utils.date.DateUtil.addDays(param.getStartDate(), 1),
                        param.getTimeUnit(),
                        null, Constants.ULTRA_FORECAST_1);
        List<BigDecimal> zeroOrNullList = com.tsintergy.aif.algorithm.serviceapi.base.util.ColumnUtil
                .getZeroOrNullList(pointSize, BigDecimal.ZERO);
        tarReportList.addAll(reportLoadCityFcDO.getTvData());
        tarReportList
                .addAll(nextDayReportLoadCityFcDO == null ? zeroOrNullList : nextDayReportLoadCityFcDO.getTvData());
        tarFcList.addAll(loadFc.getTvData());
        tarFcList.addAll(nextDayLoadFc == null ? zeroOrNullList : nextDayLoadFc.getTvData());
        //目标预测未来n个点，和已存在的点重合部分为n-1
        List<BigDecimal> reportList = tarReportList.subList(startTime, startTime + pointLen - 1);
        List<BigDecimal> fcDataList = tarFcList.subList(startTime, startTime + pointLen - 1);
        //目标时刻去见人工修正上报是否有过调整
        boolean equals = reportList.equals(fcDataList);
        if (equals) {
            return null;
        }
        //有调整需要把调整点选取出来,放入tarList
        else {
            for (int i = 0; i < pointLen; i++) {
                if (reportList.get(i).compareTo(fcDataList.get(i)) != 0) {
                    tarList.set(i, reportList.get(i));
                }
            }
        }
        return tarList;
    }


    public UltraShortParam genParam(String cityId, String caliberId, Integer timeSpan, Date startDate,
                                    Integer startTime, com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum algorithmEnum,
                                    String paramStr) {
        UltraShortParam param = new UltraShortParam(null, startDate, startDate,
                new String[]{cityId, caliberId, timeSpan.toString()});
        param.setCityId(cityId);
        param.setCaliberId(caliberId);
        param.setAlgorithmEnum(algorithmEnum);
        //超短期预测x
        param.setParam(paramStr);
        //Y 以00:00开始   F 00:15开始
        param.setStartIndex(UltraSystemUtils.startWithZero() ? "Y" : "F");
        param.setTimeUnit(timeSpan);
        if (timeSpan == 5) {
            param.setPointLen(48);
        } else {
            param.setPointLen(16);
        }
        param.setSplitDate(org.apache.commons.lang3.time.DateUtils.addDays(startDate, -1));
        param.setStartDate(startDate);
        param.setStartTime(startTime);
        return param;
    }


    /**
     * @param cityId    城市ID
     * @param date      日期
     * @param caliberId 口径id
     */
    @Override
    public ForecastOverviewDTO getForecastOverview(String cityId, Date date, String caliberId) throws Exception {
        ForecastOverviewDTO forecastOverviewDTO = new ForecastOverviewDTO();
        // 获取预测统计结果
        StatisticsCityDayFcDO statisticsCityDayFcDO = statisticsCityDayFcDAO
                .getReportStatisticsCityDayFcDO(cityId, caliberId, date);
        if (statisticsCityDayFcDO == null) {
            return null;
        }
        // 预测准确率
        forecastOverviewDTO.setAccuracy(statisticsCityDayFcDO.getAccuracy());
        // 获取历史最大负荷
        LoadFeatureCityDayHisDO maxLoadDay = loadFeatureCityDayHisDAO
                .getLoadFeatureOfMaxLoadDay(cityId, caliberId);
        if (maxLoadDay != null) {
            forecastOverviewDTO.setForecastMax(maxLoadDay.getMaxLoad());
            forecastOverviewDTO.setRealMax(maxLoadDay.getMaxLoad());
        }
        // 负荷特性
        LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisDAO
                .getLoadFeatureCityDayHisDO(cityId, date, caliberId);
        if (loadFeatureCityDayHisDO != null) {
            forecastOverviewDTO.setRealMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
        }
        // 预测负荷
        LoadCityFcDO loadCityFcDO = loadCityFcDAO.getReportLoadCityFcDO(cityId, caliberId, date);
        if (loadCityFcDO != null) {
            BigDecimal maxFcLoad = BasePeriodUtils.getMaxMinAvg(BasePeriodUtils
                            .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                                    Constants.LOAD_CURVE_START_WITH_ZERO),
                    4).get("max");
            forecastOverviewDTO.setForecastMaxLoad(maxFcLoad);
        }
        // 预测评价
        ForecastCommentaryDTO commentaryDTO = new ForecastCommentaryDTO();
        // 预测准确率上限
        BigDecimal accuracyMax = new BigDecimal(systemService.getValue("accuracy_max"));
        // 预测准确率下限
        BigDecimal accuracyMin = new BigDecimal(systemService.getValue("accuracy_min"));

        // 准确率评价
        if (forecastOverviewDTO != null && forecastOverviewDTO.getAccuracy() != null && accuracyMax != null
                && accuracyMin != null) {
            if (forecastOverviewDTO.getAccuracy().compareTo(accuracyMax) >= 0) {
                commentaryDTO.setCommentType(ForecastCommentType.LEVEL1.value());
            } else if (forecastOverviewDTO.getAccuracy().compareTo(accuracyMin) >= 0) {
                commentaryDTO.setCommentType(ForecastCommentType.LEVEL2.value());
            } else {
                commentaryDTO.setCommentType(ForecastCommentType.LEVEL3.value());
            }
        }

        // 曲线评价
        LoadCityHisDO loadCityHisDO = loadCityHisDAO.getLoadCityHisDOByOneDate(cityId, date, caliberId); // 实际曲线
        BigDecimal similarity = LoadCalUtil.calSimilarity(loadCityHisDO, loadCityFcDO); // 相似度
        BigDecimal similarityMax = new BigDecimal(systemService.getValue("similarity_max")); // 相似度高阈值
        BigDecimal similarityMin = new BigDecimal(systemService.getValue("similarity_min")); // 相似度地阈值

        if (similarity != null && similarityMax != null && similarityMin != null) {
            if (similarity.compareTo(similarityMax) >= 0) {
                commentaryDTO.setCurveType(SimilarityCommentType.LEVEL1.value());
            } else if (similarity.compareTo(similarityMin) >= 0) {
                commentaryDTO.setCurveType(SimilarityCommentType.LEVEL2.value());
            } else {
                commentaryDTO.setCurveType(SimilarityCommentType.LEVEL3.value());
            }
        }
        // 误差评价
        if (loadCityFcDO != null && loadFeatureCityDayHisDO != null) {
            String maxTimeFc = null; // 预测最大负荷发生时刻
            String minTimeFc = null; // 预测最小负荷发生时刻
            Map<String, BigDecimal> maxMixAvgFc = BasePeriodUtils.getMaxMinAvg(BasePeriodUtils
                            .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                                    Constants.LOAD_CURVE_START_WITH_ZERO),
                    4);
            Map<String, BigDecimal> loadFcMap = BasePeriodUtils
                    .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO);

            for (String column : loadFcMap.keySet()) {
                BigDecimal load = loadFcMap.get(column);
                if (null != load) {
                    if (load.compareTo(maxMixAvgFc.get("max")) == 0) {
                        maxTimeFc = column.replace("t", "");
                    }
                    if (load.compareTo(maxMixAvgFc.get("min")) == 0) {
                        minTimeFc = column.replace("t", "");
                    }
                }
            }
            // 最大负荷预测偏高/偏低数值
            commentaryDTO
                    .setMaxHigh(BigDecimalUtils.sub(maxMixAvgFc.get("max"), loadFeatureCityDayHisDO.getMaxLoad()));
            // 最小负荷预测偏高/偏低数值
            commentaryDTO
                    .setMinHigh(BigDecimalUtils.sub(maxMixAvgFc.get("min"), loadFeatureCityDayHisDO.getMinLoad()));
            // 计算最大/小负荷提前/滞后分钟数
            commentaryDTO.setMaxEarlyTime(DateUtil.getMinutes(maxTimeFc, loadFeatureCityDayHisDO.getMaxTime()));
            commentaryDTO.setMinEarlyTime(DateUtil.getMinutes(minTimeFc, loadFeatureCityDayHisDO.getMinTime()));
        }
        forecastOverviewDTO.setCommentary(commentaryDTO);
        return forecastOverviewDTO;

    }

    @Override
    public List<BigDecimal> smoothLine(List<BigDecimal> singleLoadCityVOList) throws Exception {
        return AlgorithmUtil.smoothLine(singleLoadCityVOList, 5);
    }

    @Override
    public List<BigDecimal> recorrectLoad(List<BigDecimal> originLoad, BigDecimal distMax, BigDecimal distMin)
            throws BusinessException {
        return AlgorithmUtil.recorrectLoad(originLoad, distMax, distMin);
    }

    /**
     * 高级正常日 ----》获取最近一次预测
     */
    @Override
    public List<ForecastNormalDTO> getForecastNormal(String cityId, String caliberId, Date startDate, Date endDate)
            throws BusinessException {
        try {
            List<LoadCityFcDO> loadCityFcDOS = new ArrayList<>();
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                            t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                                    .equals(t.getType()))
                    .collect(Collectors.toList());

            List<AlgorithmDO> viewAlgorithms = null;
            CityDO cityDO = cityService.findCityById(cityId);
            if (CityConstants.PROVINCE_TYPE.equals(cityDO.getType())) {
                viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getProvinceView() == true)
                        .collect(Collectors.toList());
            } else if (CityConstants.CITY_TYPE.equals(cityDO.getType())) {
                viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true)
                        .collect(Collectors.toList());
            }


            for (AlgorithmDO algorithmId : viewAlgorithms) {
                LoadCityFcDO loadCityFcDO = loadCityFcDAO
                        .getLoadCityFcDO(cityId, caliberId, algorithmId.getId(), startDate);
                if (loadCityFcDO != null) {
                    loadCityFcDOS.add(loadCityFcDO);
                }
            }
            if (loadCityFcDOS.size() < 1) {
                throw new BusinessException("T706", "");
            }
            List<ForecastNormalDTO> forecastNormalDTOS = new ArrayList<ForecastNormalDTO>();
            forecastNormalDTOS.addAll(this.forecastLoadVOsToForecastNormalDTOS(loadCityFcDOS));
            return forecastNormalDTOS;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    /**
     * 功能描述: <br> 高级正常日预测
     *
     * @param cityId       城市id
     * @param caliberId    口径id
     * @param forecastDays 预测日
     * @return:
     * @since: 1.0.0
     * @Author:wangfeng
     * @Date: 2018/9/13 10:38
     */
    @Override
    @Deprecated
    public List<ForecastNormalDTO> doAdvancedForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
                                                            List<AlgorithmEnum> algorithmEnums)
            throws Exception {
        List<LoadCityFcDO> forecastLoadVOS = doForecastNormal(cityId, caliberId, forecastDays, algorithmEnums);
        if (!CollectionUtils.isEmpty(forecastLoadVOS)) {
            List<ForecastNormalDTO> forecastNormalDTOS = new ArrayList<>();
            //重新组装数据形式
            forecastNormalDTOS.addAll(this.forecastLoadVOsToForecastNormalDTOS(forecastLoadVOS));
            return forecastNormalDTOS;
        }
        return null;
    }

    @Override
    @Deprecated
    public List<LoadCityFcDO> doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
                                               List<AlgorithmEnum> algorithmEnums)
            throws Exception {
        this.doForecastNormal(cityId, caliberId, forecastDays, algorithmEnums, "102");
        return null;
    }


    @Override
    public void doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
                                 List<AlgorithmEnum> algorithmEnums, String weatherCode)
            throws Exception {
        String city = cityService.findCityById(cityId).getCity();
        ForecastParam param = new ForecastParam(AlgorithmUtil.getUid(), cityId, city, caliberId, algorithmEnums,
                forecastDays);
        param.setForecastType(ForecastParam.FORECAST_CYCLIC_TYPE);
        param.setFcstDayWeatherType(
                weatherCode.equals(SystemConstant.WEATHER_CODE_MANUAL) ? SystemConstant.FCST_DAY_WEATHER_FC
                        : SystemConstant.FCST_DAY_WEATHER_HIS);
        forecastable.forecast(param);
    }


    @Override
    public List<LoadCityFcDO> insertNormalData(String cityId, String caliberId,
                                               GeneralResult result)
            throws Exception {
        List<LoadCityFcDO> forecastLoadVOS = new ArrayList<>();
        String[] normalAlgorithmId = systemService.findByFieldId(SystemConstant.NORMAL_ALGORITHM).getValue()
                .split(Constants.SEPARATOR_PUNCTUATION);
        String[] autoReportData = systemService.findByFieldId(SystemConstant.AUTO_REPORT).getValue()
                .split(Constants.SEPARATOR_PUNCTUATION);
        //默认的系统算法id为相似日
        String systemAlgorithmId;
        //自动上报
        boolean autoReport;
        //正常日系统设置的算法id
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            systemAlgorithmId = normalAlgorithmId[0];
            autoReport = autoReportData[0].equals(ParamConstants.STRING_COMPOSITE_ON);
        } else {
            systemAlgorithmId = normalAlgorithmId[1];
            autoReport = autoReportData[1].equals(ParamConstants.STRING_COMPOSITE_ON);
        }

        List<Date> holidays = holidayDAO.getAllHolidays();
        for (LoadCityFcDO metaData : result.getResult()) {
            try {
                LoadCityFcDO loadCityFcDO = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                        caliberId,
                        metaData.getAlgorithmId());
                ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO);
                forecastLoadVOS.add(loadCityFcDO);
                insertOrUpdateData(cityId, caliberId, metaData, holidays, systemAlgorithmId, autoReport);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                continue;
            }
        }
        return forecastLoadVOS;
    }

    /**
     * <AUTHOR>  每日算法 多算法手动预测 只预测单天
     */
    @Override
    public void doForecast(String cityId, String caliber, List<Date> forecastDates,
                           AlgorithmEnum algorithmEnums) throws Exception {
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(algorithmEnums);
        String city = this.cityService.findCityById(cityId).getCity();
        ForecastParam param = new ForecastParam(AlgorithmUtil.getUid(), cityId, city, caliber, enums, forecastDates);
        param.setForecastType(ForecastParam.FORECAST_TYPE);
        forecastable.forecast(param);
    }


    private void insertOrUpdateData(String cityId, String caliberId, LoadCityFcDO metaData, List<Date> holidays,
                                    String systemAlgorithmId, boolean autoReport) throws Exception {
        String algorithmId = metaData.getAlgorithmId();
        List<LoadCityFcDO> loadCityFcVOS = loadCityFcDAO
                .getLoadCityFcDOs(cityId, caliberId, algorithmId, metaData.getDate(), metaData.getDate());
        //如果预测的日期是节假日，则需要将默认上报的是节假日算法，所以这里让所有正常日预测出的结果都不上报
        if (holidays.contains(metaData.getDate())) {
            systemAlgorithmId = AlgorithmEnum.HOLIDAY_FEST_SVM.getType();
        }
        LoadCityFcDO report = loadCityFcService.getReport(cityId, caliberId, metaData.getDate());
        if (loadCityFcVOS.size() < 1) {
            //如果数据库中没有此算法id的数据  则创建，
            LoadCityFcDO loadCityFcVO1 = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                    caliberId, algorithmId);
            //如果和系统推荐算法id一致
            if (algorithmId.equals(systemAlgorithmId)) {
                //如果没有上报记录
                if (autoReport && report == null) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                    loadCityFcVO1.setSucceed(true);
                } else {
                    loadCityFcVO1.setReport(false);
                    loadCityFcVO1.setSucceed(false);
                }
                loadCityFcVO1.setRecommend(true);
            } else {
                loadCityFcVO1.setRecommend(false);
                loadCityFcVO1.setReport(false);
                loadCityFcVO1.setSucceed(false);
            }
            loadCityFcVO1.setCreatetime(new Timestamp(System.currentTimeMillis()));
            ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcVO1);
            loadCityFcDAO.create(loadCityFcVO1);
            loadCityFcBatchService.doSave(loadCityFcVO1);
        } else {
            //如果数据库中有数据的话 直接做update处理
            LoadCityFcDO loadCityFcVO1 = loadCityFcVOS.get(0);
            ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcVO1);
            if (algorithmId.equals(systemAlgorithmId)) {
                //如果没有上报记录
                if (autoReport && report == null || report.getAlgorithmId().equals(algorithmId)) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setSucceed(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                } else if (autoReport && report.getAlgorithmId().equals(algorithmId)) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setSucceed(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                } else {
                    loadCityFcVO1.setReport(false);
                    loadCityFcVO1.setSucceed(false);
                }
                loadCityFcVO1.setRecommend(true);
            } else {
                loadCityFcVO1.setRecommend(false);
                loadCityFcVO1.setReport(false);
                loadCityFcVO1.setSucceed(false);
            }
            loadCityFcVO1.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadCityFcDAO.update(loadCityFcVO1);
            loadCityFcBatchService.doSave(loadCityFcVO1);
        }
    }

    /**
     * 页面调用节假日预测
     */
    @Override
    @Deprecated
    public List<ForecastNormalDTO> doForecastHoliday(String uid, String cityId, String caliberId, Date startDate,
                                                     Date endDate)
            throws Exception {
        HolidayParam param = new HolidayParam(uid, startDate, endDate, cityId, caliberId,
                AlgorithmEnum.HOLIDAY_FEST_SVM, cityService.findCityById(cityId).getCity());
        holidayForecastService.forecast(param);
        //todo  现在没有手动预测页面 暂时屏蔽方法
        return null;
    }

    @Override
    public void insertOrUpdateHoliday(String cityId, String caliberId, GeneralResult result) throws Exception {
        List<LoadCityFcDO> metaDataList = result.getResult();
        for (LoadCityFcDO metaData : metaDataList) {
            //节假日预测入库
            String algorithmId = metaData.getAlgorithmId();
            List<LoadCityFcDO> loadCityFcDOList = loadCityFcDAO
                    .getLoadCityFcDOs(cityId, caliberId, algorithmId, metaData.getDate(), metaData.getDate());
            if (loadCityFcDOList.size() < 1) {
                //如果数据库中没有 则创建，
                LoadCityFcDO loadCityFcDO1 = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                        caliberId, algorithmId);
                loadCityFcDO1.setRecommend(false);
                loadCityFcDO1.setReport(true);
                loadCityFcDO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDO1.setSucceed(true);
                ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO1);
                loadCityFcDO1.setCreatetime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDAO.create(loadCityFcDO1);
            } else {
                //如果数据库中有数据的话  做更新处理
                LoadCityFcDO loadCityFcDO2 = loadCityFcDOList.get(0);
                loadCityFcDO2.setCreatetime(new Timestamp(System.currentTimeMillis()));
                ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO2);
                loadCityFcDAO.update(loadCityFcDO2);
            }
        }
    }


    @Override
    public void doForecastTyphoon(String cityId, String caliberId, Date date, Integer type)
            throws Exception {
        Date loginDay = date;
        //台风预测 起始日是台风登陆日，结束日是登陆日后两天
        Date startDay = loginDay;
        Date baseDay;
        //历史台风
        if (loginDay.before(new Date())) {
            baseDay = DateUtils.addDays(loginDay, -1);
        } else {
            //未来的台风 基准日选取昨天（拥有完整96点数据的最近的一天）
            baseDay = DateUtils.addDays(new Date(), -1);
        }
        Date endDay = DateUtils.addDays(loginDay, 2);
        TyphoonParam param = new TyphoonParam(cityId, caliberId, loginDay, startDay, endDay, baseDay,
                AlgorithmEnum.TYPHOON_SVM);
        this.typhoonForecast.forecast(param);

    }


    /**
     * 转换返回数据格式  暂不维护 by wangh
     */
    @Deprecated
    private List<ForecastNormalDTO> forecastLoadVOsToForecastNormalDTOS(List<LoadCityFcDO> loadCityFcDOS) {
        List<ForecastNormalDTO> forecastNormalDTOS = new ArrayList<ForecastNormalDTO>();
        //1.先将源列表变成map,key为日期
        Map<Date, List<LoadCityFcDO>> listMap = new HashMap<Date, List<LoadCityFcDO>>();
        for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
            if (null != listMap.get(loadCityFcDO.getDate())) {
                listMap.get(loadCityFcDO.getDate()).add(loadCityFcDO);
            } else {
                List<LoadCityFcDO> forecastLoadVOList = new ArrayList<LoadCityFcDO>();
                forecastLoadVOList.add(loadCityFcDO);
                listMap.put(loadCityFcDO.getDate(), forecastLoadVOList);
            }
        }
        //2.遍历map,将map转为List<ForecastNormalDTO>
        for (Date date : listMap.keySet()) {
            ForecastNormalDTO forecastNormalDTO = new ForecastNormalDTO();
            forecastNormalDTO.setTargetDay(date);
            List<ForecastResultDTO> forecastList = new ArrayList<ForecastResultDTO>();
            for (LoadCityFcDO loadCityFcDO : listMap.get(date)) {
                List<BigDecimal> loadList = BasePeriodUtils
                        .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                                Constants.LOAD_CURVE_START_WITH_ZERO);
                ForecastResultDTO forecastResultDTO = new ForecastResultDTO();
                forecastResultDTO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
                forecastResultDTO.setAlgorithmName(
                        algorithmService.getAlgorithmDOById(loadCityFcDO.getAlgorithmId()).getAlgorithmCn());
                forecastResultDTO.setValue(loadList);
                forecastResultDTO.setMaxLoad(LoadCalUtil.max(loadList));
                forecastResultDTO.setMinLoad(LoadCalUtil.min(loadList));
                forecastResultDTO.setGradient(
                        LoadCalUtil.calGradient(forecastResultDTO.getMaxLoad(), forecastResultDTO.getMinLoad()));
                forecastResultDTO.setLoadGradient(LoadCalUtil
                        .calLoadGradient(BigDecimalUtils.avgList(loadList, Constants.SCALE, false),
                                forecastResultDTO.getMaxLoad()));
                forecastList.add(forecastResultDTO);
            }
            forecastNormalDTO.setForecastList(forecastList);
            forecastNormalDTOS.add(forecastNormalDTO);
        }
        return forecastNormalDTOS;
    }

    /**
     * 功能描述: <br> 调用数据修正算法
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @Override
    public void doDataRepairAlgorithm(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        try {
            PreProcessParam param = new PreProcessParam();
            param.setPreProcessBeginDay(startDate);
            param.setBaseDay(endDate);
            param.setCityId(cityId);
            param.setAlgorithmEnum(AlgorithmEnum.LOAD_PRE_PROCESS);
            param.setCaliberId(caliberId);
            preProcessService.forecast(param);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 调用稳定度分析算法 直接入库
     */
    @Override
    public LoadDecomposeCityWeekStabilityDO doPrecisionAnalysize(String caliberId, String cityId, Date date, int week,
                                                                 String userId)
            throws Exception {
        log.error("caliberId=" + caliberId + ",cityId=" + cityId + "date=" + DateUtils
                .date2String(date, DateFormatType.DATE_FORMAT_STR) + "week" + week);
        AnalysisParam param = new AnalysisParam(caliberId, cityId, date, week);
        param.setAlgorithmEnum(AlgorithmEnum.STABILITY);
        param.setUserId(userId);
        analysisForecastService.forecast(param);
        AnalysisResult bean = (AnalysisResult) redisService
                .redisGet(userId + Constants.SEPARATOR_BROKEN_LINE + CacheConstants.CACHE_ANALYSISRESULT_KEY,
                        AnalysisResult.class);

        if (bean == null) {
            log.error("稳定度分析结果为空。。。。");
            return null;
        }
        //周曲线数据
        List<LoadDecomposeCityWeekDO> loadDecomposeCityWeekVOS = bean.getLoadDecomposeCityWeekDO();
        if (loadDecomposeCityWeekVOS == null || loadDecomposeCityWeekVOS.size() < 1) {
            log.error("稳定度周曲线数据为空。。。。。。。。。");
        }
        //稳定度上下限
        LoadDecomposeCityWeekStabilityDO loadDecomposeCityWeekStabilityVO = bean.getVo();

        if (loadDecomposeCityWeekStabilityVO != null) {
            loadDecomposeCityWeekStabilityService.doCreate(loadDecomposeCityWeekStabilityVO);
        }

        for (LoadDecomposeCityWeekDO vo : loadDecomposeCityWeekVOS) {
            List<LoadDecomposeCityWeekDO> vos = loadDecomposeCityWeekService.findLoadDecomposeCityWeekDO(vo);
            if (vos.size() < 1) {
                vo.setCreatetime(new Timestamp(System.currentTimeMillis()));
                loadDecomposeCityWeekService.doCreate(vo);
            } else {
                LoadDecomposeCityWeekDO vo2 = vos.get(0);
                BasePeriodUtils.setAllFiled(vo2, ColumnUtil
                        .listToMap(BasePeriodUtils.toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO),
                                Constants.LOAD_CURVE_START_WITH_ZERO));
                loadDecomposeCityWeekService.doUpdateLoadDecomposeCityWeekDO(vo2);
            }
        }

        return loadDecomposeCityWeekStabilityVO;
    }

    /**
     * 功能描述: <br> 获取置信最大最小
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @Override
    public Map<String, List<BigDecimal>> getMaxMinConfidence(String cityId, String caliberId, String algorithmId,
                                                             Date date) throws Exception {
        LoadCityFcDO loadCityFcDO = loadCityFcService.getLoadCityFcDO(date, cityId, caliberId, algorithmId);
        if (loadCityFcDO == null) {
            return null;
        }
        Date startDate = DateUtils.addYears(date, -1);
        List<DeviationLoadCityFcDO> deviationLoadCityFcVOList = deviationLoadCityFcService
                .findDeviationLoadCityFcDO(cityId, caliberId, algorithmId, startDate, date);
        if (CollectionUtils.isEmpty(deviationLoadCityFcVOList)) {
            return null;
        }
        // <时刻点,List<Bigdecimal>>
        Map<String, List<BigDecimal>> listMap = LoadCalUtil
                .toColumnList(deviationLoadCityFcVOList, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
        //<时刻点,区间范围>
        Map<String, BigDecimal> map = new HashMap<>();
        for (Map.Entry<String, List<BigDecimal>> entry : listMap.entrySet()) {
            //每一个时刻点的list集合
            List<BigDecimal> list = entry.getValue();
            //时刻的最大误差值
            BigDecimal max = LoadCalUtil.getMax(list);
            if (list == null || max == null) {
                continue;
            }
            //每一个时刻点得置信区间
            BigDecimal bound = this.calculate(0, list, max, new BigDecimal(0));
            log.info(entry.getKey() + ":置信区间为" + bound);
            map.put(entry.getKey(), bound);
        }
        Map<String, BigDecimal> loadMap = BasePeriodUtils
                .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, List<BigDecimal>> dataListMap = new HashMap<>();
        List<BigDecimal> maxList = new ArrayList<>();
        List<BigDecimal> minList = new ArrayList<>();
        //map 排序
        Map<String, BigDecimal> sort = DataUtil.sortMapByKey(loadMap);
        for (String key : sort.keySet()) {
            if (loadMap.get(key) == null) {
                continue;
            }
            BigDecimal value = map.get(key);
            //置信最大限
            BigDecimal max = loadMap.get(key).add(value);
            maxList.add(max);
            //置信最下限
            BigDecimal min = loadMap.get(key).subtract(value);
            minList.add(min);
        }
        dataListMap.put("max", maxList);
        dataListMap.put("min", minList);
        return dataListMap;
    }


    /**
     * 功能描述: <br> 计算每个时刻点的置信区间
     *
     * @param list     list集合
     * @param maxValue 最大值
     * @param minValue 最小值
     * <AUTHOR>
     * @since 1.0.0
     */
    private BigDecimal calculate(int count, List<BigDecimal> list, BigDecimal maxValue, BigDecimal minValue)
            throws Exception {
        //二分法取最大最小误差中间值
        BigDecimal intervalValue = minValue.add(maxValue).divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_DOWN);
        //计算置信度
        BigDecimal bound = this.calculateBound(intervalValue, list);
        // 置信度和0.95相比
        BigDecimal gap = bound.subtract(CONFIDENCE);
        BigDecimal b = RANGE.multiply(new BigDecimal(-1));
        if (gap.compareTo(BigDecimal.ZERO) < 0 && gap.compareTo(b) >= 0) {//如果是负数
            log.info("------" + count + "次数");
            log.info("置信度为" + bound);
            return intervalValue;
        }
        if (gap.compareTo(BigDecimal.ZERO) >= 0 && gap.compareTo(RANGE) <= 0) {//如果是正数
            log.info("------" + count + "次数");
            return intervalValue;
        }
        if (count > 15) {//判断如果次数超过了15次,取最后一次计算结果
            log.info("------" + count + "次数");
            return intervalValue;
        }
        while (true) {
            count++;
            if (bound.compareTo(CONFIDENCE) > 0) {//大于0.95情况
                maxValue = intervalValue;
                return calculate(count, list, maxValue, minValue);
            }
            if (bound.compareTo(CONFIDENCE) < 0) {//小于0.95的情况
                minValue = intervalValue;
                return calculate(count, list, maxValue, minValue);
            }
        }
    }

    /**
     * 功能描述: <br> 计算置信度
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    public BigDecimal calculateBound(BigDecimal intervalValue, List<BigDecimal> list) throws Exception {
        int count = 0;
        for (BigDecimal number : list) {
            if (number == null) {
                continue;
            }
            if (number.compareTo(BigDecimal.ZERO) < 0
                    && number.compareTo(intervalValue.multiply(new BigDecimal(-1))) >= 0) {
                count++;
            }
            if (number.compareTo(BigDecimal.ZERO) >= 0 && number.compareTo(intervalValue) <= 0) {
                //计算一年的点在区间的范围
                count++;
            }
        }
        BigDecimal p = new BigDecimal(count).divide(BigDecimal.valueOf(list.size()), 4, BigDecimal.ROUND_DOWN);
        return p;
    }


    /**
     * 获取平均偏差率
     */
    @Override
    public List<BigDecimal> getAvgDeviation(String cityId, String caliberId, String algorithmId, Date date)
            throws Exception {
        Date startDate = DateUtils.addDays(date, -7);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, date, caliberId);
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
                .getLoadCityFcDOs(cityId, caliberId, algorithmId, startDate, date);
        if (CollectionUtils.isEmpty(loadCityFcDOS) || CollectionUtils.isEmpty(
                loadCityHisDOS) || loadCityFcDOS.size() != loadCityHisDOS.size()) {
            return null;
        }
        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<>();
        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
            String key = loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                    .getTime();
            loadCityHisVOMap.put(key, loadCityHisDO);
        }
        Map<String, List<LoadCityFcDO>> loadCityFcVOMap = new HashMap<>();
        for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
            String key =
                    loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            if (loadCityFcVOMap.get(key) == null) {
                loadCityFcVOMap.put(key, new ArrayList<>());
            }
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }
        Map<String, BigDecimal> bigDecimalMap = new HashMap<>();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (LoadCityFcDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    //计算一天得偏差率
                    Map<String, BigDecimal> map = deviationLoadCityFcDAO
                            .calculateDeviationRatio(loadCityHisVOMap.get(key), loadCityFcDO);
                    for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
                        if (bigDecimalMap.get(entry.getKey()) == null) {
                            bigDecimalMap.put(entry.getKey(), entry.getValue());
                        } else {
                            BigDecimal value = entry.getValue();
                            if (value == null) {
                                value = BigDecimal.ZERO;
                            }
                            bigDecimalMap.put(entry.getKey(), bigDecimalMap.get(entry.getKey()).add(value));
                        }
                    }
                }
            }
        }
        Map<String, BigDecimal> sortMap = DataUtil.sortMapByKey(bigDecimalMap);
        List<BigDecimal> list = new ArrayList<>();
        if (sortMap == null) {
            return null;
        }
        for (Map.Entry<String, BigDecimal> entry : sortMap.entrySet()) {
            BigDecimal value = entry.getValue();
            BigDecimal ratio =
                    value == null ? null : value.divide(new BigDecimal(loadCityFcDOS.size()), 4, BigDecimal.ROUND_DOWN)
                            .multiply(new BigDecimal(100));
            list.add(ratio);
        }
        return list;
    }


    @Override
    public ResultDTO doSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        try {
            SensitivityParam sensitivityParam = new SensitivityParam();
            sensitivityParam.setBeginDate(algorithmDTO.getStartDate());
            sensitivityParam.setEndDate(algorithmDTO.getEndDate());
            sensitivityParam.setMax(algorithmDTO.getMax());
            sensitivityParam.setMin(algorithmDTO.getMin());
            sensitivityParam.setStep(algorithmDTO.getStep());
            sensitivityParam.setLoadType(algorithmDTO.getLoadType());
            sensitivityParam.setWeatherType(algorithmDTO.getWeatherType());
            sensitivityParam.setCityId(algorithmDTO.getCityId());
            sensitivityParam.setAlgorithmEnum(AlgorithmEnum.SENSITIVITY);
            //排除日期中的周末
            List<String> dateNotIncludedDates = getDateNotIncluded(algorithmDTO.getStartDate(), algorithmDTO.getEndDate());
            sensitivityParam.setDateNotIncluded(dateNotIncludedDates);
            sensitivityParam.setCaliberId(algorithmDTO.getCaliberId());
            sensitivityParam.setUserId(algorithmDTO.getUserId());
            sensitivityParam.setTradeCode(algorithmDTO.getTradeCode());
            sensitivityParam.setRegionId(algorithmDTO.getRegionId());

            StringBuffer weightStr = new StringBuffer();
            String value = systemService.findByFieldId(SystemConstant.AVG_TEMPERATURE_VALUE).getValue();
            JSONObject jsonObjectMap = JSONObject.parseObject(value);
            for (String key : jsonObjectMap.keySet()) {
                weightStr.append(jsonObjectMap.get(key)).append(",");
            }
            weightStr.deleteCharAt(weightStr.length() - 1);
            // param 赋值
            sensitivityParam.setWeight(weightStr.toString());

            //调用灵敏度分析算法
            sensitivityForecastService.forecast(sensitivityParam);

            SensitivityResult result = (SensitivityResult) redisService
                    .redisGet(algorithmDTO.getUserId() + Constants.SEPARATOR_BROKEN_LINE
                            + CacheConstants.CACHE_SENSITIVITYRESULT_KEY, SensitivityResult.class);

            resultDTO.setAnalyze(result.getBeanList());
            FeatureDTO feature = new FeatureDTO();
            feature.setFeaturesLine(result.getFeaturesLine());
            feature.setFeaturesPoint(result.getFeaturesPoint());
            feature.setFittingAccuracy(result.getFittingAccuracy());
            resultDTO.setFeature(feature);
            resultDTO.setComment(generateSensitivityComment(result));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return resultDTO;
    }

    //获取日期集合中的周末
    private List<String> getDateNotIncluded(Date startDate, Date endDate) {
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<String> result = new ArrayList<>();
        for (Date date : listBetweenDay) {
            boolean weekend = DateUtil.isWeekend(date);
            if (weekend) {
                result.add(DateUtil.formateDate(date));
            }
        }
        return result;
    }

    private String generateSensitivityComment(SensitivityResult result) {
        if (!CollectionUtils.isEmpty(result.getFeaturesPoint())) {
            BigDecimal minTemp = result.getFeaturesPoint().get(0).get(0);
            BigDecimal maxTemp = result.getFeaturesPoint().get(result.getFeaturesPoint().size() - 1).get(0);
            StringBuilder comment = new StringBuilder();
            comment.append("可参考灵敏度区间为：温度大于等于")
                    .append(minTemp)
                    .append(",小于等于")
                    .append(maxTemp);
            return comment.toString();
        }
        return "";
    }


    private List<LoadFeatureCityDayHisDO> mergeHistoryLoadFeature(String cityId, String caliberId, Date startDate,
                                                                  Date endDate) throws Exception {
        return loadFeatureCityDayHisDAO.getLoadFeatureCityDayHisDOs(cityId, startDate, endDate, caliberId);
    }

    private List<WeatherFeatureCityDayHisDO> mergeSensitivityWeather(String cityId, Date startDate, Date endDate,
                                                                     List<SearchDTO> searchDTOList) throws Exception {
        return weatherFeatureCityDayHisDAO.findWeatherFeature(cityId, startDate, endDate, searchDTOList);
    }

    /**
     * 超短期预测
     *
     * <AUTHOR>
     */
    @Override
    public void doShortForecast(String cityId, String caliberId, Date date, Integer timeSpan,
                                Integer startTimePoint)
            throws Exception {
        List<String> columns = ColumnUtil
                .getColumns(ShortConstants.MINUTE.equals(timeSpan) ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        if (startTimePoint == 96 && !ShortConstants.MINUTE.equals(timeSpan)) {
            startTimePoint = 0;
        }
        if (startTimePoint == 288) {
            startTimePoint = 0;
        }
        String fcStartTime = columns.get(startTimePoint);
        //系统设置的超短期预测的时长 默认4小时
        SystemData systemSetting = systemService.getSystemSetting();
        String hour;
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            if (ShortConstants.MINUTE.equals(timeSpan)) {
                hour = systemSetting.getProvinceShortFiveTime();
            } else {
                hour = systemSetting.getProvinceShortFifteenTime();
            }
        } else {
            if (ShortConstants.MINUTE.equals(timeSpan)) {
                hour = systemSetting.getCityShortFiveTime();
            } else {
                hour = systemSetting.getCityShortFifteenTime();
            }
        }
        ShortForecastParam param = new ShortForecastParam();
        param.setForecastDate(date);
        param.setTimeSpan(String.valueOf(timeSpan));
        param.setStartTimePoint(String.valueOf(startTimePoint));
        param.setForecastPoint(String.valueOf((Integer.valueOf(hour) * 60) / timeSpan));
        param.setCityId(cityId);
        param.setAlgorithmEnum(AlgorithmEnum.SHORT_FORECAST);
        param.setCaliberId(caliberId);
        //调用超短期算法
        shortForecastService.forecast(param);
    }

    @Override
    public String getAiAlgorithmRecommendContent(String cityId, String caliberId, Date forecastDate) throws Exception {
        String key = Constants.ALGO_RECOMMEND_REDDIS_KEY + cityId + "-" + caliberId + "-" + forecastDate.getTime();
        String result = RedisCacheUtils.get(key, String.class);
        if (StrUtil.isNotBlank(result)){
            return result;
        }
        AlgorithmAccuracyAiDTO algorithmAccuracyAiDTO = getAlgorithmAccuracy(cityId, caliberId, forecastDate);
        if (Objects.isNull(algorithmAccuracyAiDTO) || CollectionUtils.isEmpty(algorithmAccuracyAiDTO.getItems())) {
            return "";
        }
        String jsonBody = parseAiRequestBody(algorithmAccuracyAiDTO);
        String stringCompletableFuture = sendRequest4Future(aiAlgorithmConfigProperties.getServiceUrl(), jsonBody).join();
        String text = removeBetweenTags(stringCompletableFuture, "<think>", "</think>");
        if (StrUtil.isNotBlank(text)) {
            RedisCacheUtils.put(key,text,7*24*60*60L);
        }
        return text;
    }

    @Override
    public void getAiAlgorithmRecommendHandler(String cityId, String caliberId, Date forecastDate) throws Exception {
        AlgorithmAccuracyAiDTO algorithmAccuracy = getAlgorithmAccuracy(cityId, caliberId, forecastDate);
        if (Objects.isNull(algorithmAccuracy) || CollectionUtils.isEmpty(algorithmAccuracy.getItems())) {
            throw TsieExceptionUtils.newBusinessException("获取准确率异常。无法调用AI推荐接口");
        }
        List<HolidayDO> holidayList = holidayDAO.findHoliday(DateUtils.addYears(forecastDate, -1), DateUtils.addDays(forecastDate, 30));

        ExecutorService executor = Executors.newFixedThreadPool(7); // 根据实际情况调整线程数
        List<CompletableFuture<Void>> tasks = new ArrayList<>();

        for (int i = 1; i <= 7; i++) {
            Date date = DateUtils.addDays(forecastDate, i);
            AlgorithmAccuracyAiDTO request  = new AlgorithmAccuracyAiDTO();
            BeanUtils.copyProperties(algorithmAccuracy, request);
            String dayType = getDayType(date, holidayList);
            for (AlgorithmAccuracyAiItemDTO item : request.getItems()) {
                getModelScore(item, dayType);
            }
            request.setDayType(dayType);
            String key = Constants.ALGO_RECOMMEND_REDDIS_KEY + cityId + "-" + caliberId + "-" + date.getTime();

            CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                try {
                    String jsonBody = parseAiRequestBody(request);
                    log.info("请求接口: 城市={}, 口径={}, 日期={}", cityId, caliberId, DateUtil.formateDate(date));
                    String response = sendRequest4Future(aiAlgorithmConfigProperties.getServiceUrl(), jsonBody).join();
                    String text = removeBetweenTags(response, "<think>", "</think>");
                    if (StrUtil.isNotBlank(text)) {
                        RedisCacheUtils.put(key, text, 7 * 24 * 60 * 60L);
                    }
                } catch (Exception e) {
                    log.error("请求失败: {}", e.getMessage());
                }
            }, executor);

            tasks.add(task);
        }

        CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
    }

    @Override
    public WeatherSourceFcAccuracyCompareDTO getForecastSuggestion(String cityId, Date systemDate) throws Exception {
        List<WeatherCityHisDO> weatherHisList = weatherCityHisService.findWeatherCityHisDOs(cityId, null, DateUtils.addDays(systemDate, -7),
                DateUtils.addDays(systemDate, -1));
        if (CollectionUtils.isEmpty(weatherHisList)) {
            return null;
        }
        Map<String, WeatherCityHisDO> weatherHisMap = weatherHisList.stream().collect(Collectors.toMap(x -> x.getType() +
                DateUtils.date2String(x.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR), Function.identity(), (key1, key2) -> key2));
        // 气象局气象
        List<WeatherCityFcDO> basicWeatherFcList = weatherCityFcService.findWeatherCityFcDOs(cityId, null, DateUtils.addDays(systemDate, -7),
                DateUtils.addDays(systemDate, 1));
        Map<String, WeatherCityFcDO> basicWeatherFcMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(basicWeatherFcList)) {
            basicWeatherFcMap = basicWeatherFcList.stream().collect(Collectors.toMap(x -> x.getType() +
                    DateUtils.date2String(x.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR), Function.identity(), (key1, key2) -> key2));
        }
        // bm气象
        List<WeatherCityFcBmDO> bmWeatherFcList = weatherCityFcBmService.getListByCondition(cityId, null,
                DateUtils.addDays(systemDate, -7), DateUtils.addDays(systemDate, 1));
        Map<String, WeatherCityFcBmDO> bmWeatherFcMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(bmWeatherFcList)) {
            bmWeatherFcMap = bmWeatherFcList.stream().collect(Collectors.toMap(x -> x.getType() +
                    DateUtils.date2String(x.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR), Function.identity(), (key1, key2) -> key2));
        }
        // ec气象
        List<WeatherCityFcMeteoDO> ecWeatherFcList = weatherCityFcMeteoService.getListByCondition(cityId, null,
                DateUtils.addDays(systemDate, -7), DateUtils.addDays(systemDate, 1));
        Map<String, WeatherCityFcMeteoDO> ecWeatherFcMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(ecWeatherFcList)) {
            ecWeatherFcMap = ecWeatherFcList.stream().collect(Collectors.toMap(x -> x.getType() +
                    DateUtils.date2String(x.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR), Function.identity(), (key1, key2) -> key2));
        }
        WeatherSourceFcAccuracyCompareDTO tempetureAvgAccuracyMaxWeatherFc = getTempetureAvgAccuracyMaxWeatherFc(weatherHisMap,
                basicWeatherFcMap, bmWeatherFcMap, ecWeatherFcMap, systemDate, cityId);
        return tempetureAvgAccuracyMaxWeatherFc;
    }

    private static String getDayType(Date forecastDate, List<HolidayDO> holidayList) {
        String day_type = "工作日";
        if (DateUtil.isWeekend(forecastDate)) {
            day_type = "周末";
        }
        if (CollectionUtil.isNotEmpty(holidayList)) {
            Optional<HolidayDO> holidayDOOptional = holidayList.stream().filter(t -> !forecastDate.before(t.getStartDate()) && !forecastDate.after(t.getEndDate())).findFirst();
            if (holidayDOOptional.isPresent()) {
                day_type = "节假日";
            }
            Optional<List<String>> offDateOp = holidayList.stream().filter(t -> ObjectUtil.isNotNull(t.getOffDates()))
                    .map(t -> Arrays.asList(t.getOffDates().split(","))).filter(x -> x.contains(DateUtil.formateDate(forecastDate)))
                    .findFirst();
            if (offDateOp.isPresent()) {
                day_type = "工作日";
            }
        }
        return day_type;
    }

    private List<Date> getNearFiveWorkDay(Date forecastDate, List<HolidayDO> holidayList){
        List<Date> result = new ArrayList<>();
        Date startDate = DateUtils.addDays(forecastDate, -2);
        while (result.size() < 10) {
            startDate = DateUtils.addDays(startDate, -1);
            String dayType = getDayType(startDate, holidayList);
            if ("工作日".equals(dayType)) {
                result.add(startDate);
            }
        }
        return result;
    }

    private List<Date> getNearThreeHoliday(Date forecastDate, List<HolidayDO> holidayList){
        List<Date> result = new ArrayList<>();
        List<HolidayDO> sortHoliday = holidayList.stream().filter(t -> t.getEndDate().before(forecastDate))
                .sorted(Comparator.comparing(HolidayDO::getEndDate).reversed()).collect(Collectors.toList());
        for (int i = 0; i < 3; i++) {
            HolidayDO holidayDO = sortHoliday.get(i);
            List<Date> dateList = DateUtil.getListBetweenDay(holidayDO.getStartDate(), holidayDO.getEndDate());
            result.addAll(dateList);
        }
        return result;
    }

    private List<Date> getNearThreeWeekDay(Date forecastDate, List<HolidayDO> holidayList) {
        List<Date> result = new ArrayList<>();
        Date startDate = DateUtils.addDays(forecastDate, 0);
        String toDayDateType = getDayType(startDate, holidayList);
        if ("周末".equals(toDayDateType)) {
            startDate = DateUtils.addDays(startDate, -3);
        }
        Integer flag = 0;
        while (flag < 3) {
            startDate = DateUtils.addDays(startDate, -1);
            String dayType = getDayType(startDate, holidayList);
            if ("周末".equals(dayType)) {
                result.add(startDate);
                if ("工作日".equals(getDayType(DateUtils.addDays(startDate, -1), holidayList))) {
                    flag += 1;
                }
            }
        }
        return result;
    }

    private void getModelScore(AlgorithmAccuracyAiItemDTO algorithmAccuracyAiDTO, String dayType) {
        if ("工作日".equals(dayType)) {
            algorithmAccuracyAiDTO.setScore(algorithmAccuracyAiDTO.getFiveWorkDayAccuracy() == null ? BigDecimal.ZERO : algorithmAccuracyAiDTO.getFiveWorkDayAccuracy());
        } else if ("周末".equals(dayType)) {
            BigDecimal multiply = BigDecimalUtils.multiply(new BigDecimal("0.6"), algorithmAccuracyAiDTO.getFiveWorkDayAccuracy() == null ? BigDecimal.ZERO : algorithmAccuracyAiDTO.getFiveWorkDayAccuracy());
            BigDecimal multiply1 = BigDecimalUtils.multiply(new BigDecimal("0.4"), algorithmAccuracyAiDTO.getThreeWeekendAccuracy() == null ? BigDecimal.ZERO : algorithmAccuracyAiDTO.getThreeWeekendAccuracy());
            algorithmAccuracyAiDTO.setScore(BigDecimalUtils.add(multiply, multiply1));
        } else if ("节假日".equals(dayType)) {
            BigDecimal multiply1 = BigDecimalUtils.multiply(BigDecimal.ONE, algorithmAccuracyAiDTO.getThreeHolidayAccuracy() == null ? BigDecimal.ZERO : algorithmAccuracyAiDTO.getThreeHolidayAccuracy());
            algorithmAccuracyAiDTO.setScore(multiply1);
        }
        if (algorithmAccuracyAiDTO.getThreeWeekendAccuracy() != null) {
            algorithmAccuracyAiDTO.setThreeWeekendAccuracyPoint(algorithmAccuracyAiDTO.getThreeWeekendAccuracy().multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP) + "%");
        }
        if (algorithmAccuracyAiDTO.getThreeHolidayAccuracy() != null) {
            algorithmAccuracyAiDTO.setThreeHolidayAccuracyPoint(algorithmAccuracyAiDTO.getThreeHolidayAccuracy().multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP) + "%");
        }
        if (algorithmAccuracyAiDTO.getFiveWorkDayAccuracy() != null) {
            algorithmAccuracyAiDTO.setFiveWorkDayAccuracyPoint(algorithmAccuracyAiDTO.getFiveWorkDayAccuracy().multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP) + "%");
        }
        if (algorithmAccuracyAiDTO.getScore() != null) {
            algorithmAccuracyAiDTO.setScorePoint(algorithmAccuracyAiDTO.getScore().multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP) + "%");
        }
    }

    /**
     * 移除指定起始和结束标签之间的内容（含标签）
     */
    public String removeBetweenTags(String input, String startTag, String endTag) {
        if (input == null || startTag == null || endTag == null) {
            return input;
        }

        String regex = Pattern.quote(startTag) + ".*?" + Pattern.quote(endTag);
        return Pattern.compile(regex, Pattern.DOTALL).matcher(input).replaceAll("");
    }

    public CompletableFuture<String> sendRequest4Future(String url, String param) {
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + aiAlgorithmConfigProperties.getBear())
                .post(okhttp3.RequestBody.create(okhttp3.MediaType.parse("application/json"), param))
                .build();

        CompletableFuture<String> future = new CompletableFuture<>();
        StringBuilder fullText = new StringBuilder(); // 保存完整结果
        client.newCall(request).enqueue(new okhttp3.Callback() {

            @Override
            public void onFailure(Call call, IOException e) {
                future.completeExceptionally(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (BufferedSource source = Okio.buffer(Okio.source(response.body().byteStream()))) {
                    String line;
                    while ((line = source.readUtf8Line()) != null) {
                        if (line.startsWith("data:")) {
                            try {
                                line = line.replaceFirst("data: ", "");
                                cn.hutool.json.JSONObject json = JSONUtil.parseObj(line);
                                if (json.containsKey("data")) {
                                    String text = json.getJSONObject("data").getStr("text");
                                    if (text != null) {
                                        fullText.append(unescapeUnicode(text));
                                    }
                                }
                            } catch (Exception e) {
                                log.error("---解析deepseek返回数据异常----:{}", e.getMessage());
                            }
                        }
                    }
                    // 所有 data 处理完成后才 complete
                    future.complete(fullText.toString());
                } finally {
                    response.close();
                }
            }
        });

        return future;
    }

    private String putContentToRequestTempalte2(String dayType, String accuracyListExpression, String algoName) {
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        jsonObject.put("response_mode", "streaming");
        jsonObject.put("user", aiAlgorithmConfigProperties.getUserName());
        cn.hutool.json.JSONObject inputs = new cn.hutool.json.JSONObject();
        inputs.put("DAY_TYPE", dayType);
        inputs.put("MODEL_ACCURACY_TABLE", accuracyListExpression);
        inputs.put("RECOMMENDED_MODEL", algoName);
        jsonObject.put("inputs", inputs);
        String stringParam = jsonObject.toString();
        return stringParam;
    }

    public static String unescapeUnicode(String input) {
        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile("\\\\u([0-9a-fA-F]{4})");
        Matcher matcher = pattern.matcher(input);

        // 使用 StringBuffer 来拼接结果
        StringBuffer sb = new StringBuffer();

        // 遍历匹配项并替换 Unicode 编码为字符
        while (matcher.find()) {
            String group = matcher.group(1); // 获取 Unicode 编码部分
            char ch = (char) Integer.parseInt(group, 16); // 转换为 char
            matcher.appendReplacement(sb, Character.toString(ch)); // 替换为中文字符
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    private AlgorithmAccuracyAiDTO getAlgorithmAccuracy(String cityId, String caliberId, Date forecastDate) throws Exception {
        List<HolidayDO> holidayList = holidayDAO.findHoliday(DateUtils.addYears(forecastDate, -1), forecastDate);
        String day_type = getDayType(forecastDate, holidayList);
        List<AlgorithmDTO> algorithmDTOList = algorithmService.getAllAlgorithmDTOByCityId(cityId);
        List<String> algoIdList = algorithmDTOList.stream().map(AlgorithmDTO::getId).collect(Collectors.toList());
        List<Date> nearThreeHoliday = getNearThreeHoliday(forecastDate, holidayList);
        List<AccuracyCompositeDO> threeHolidayCompositeAccuracyList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(algoIdList) && CollectionUtil.isNotEmpty(nearThreeHoliday)) {
            threeHolidayCompositeAccuracyList = accuracyCompositeService
                    .getCompositeAccuracyList(cityId, caliberId, null, nearThreeHoliday, algoIdList);
        }
        List<Date> nearFiveWorkDay = getNearFiveWorkDay(forecastDate, holidayList);
        List<AccuracyCompositeDO> fiveWorkDayCompositeAccuracyList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(algoIdList) && CollectionUtil.isNotEmpty(nearFiveWorkDay)) {
            fiveWorkDayCompositeAccuracyList = accuracyCompositeService
                    .getCompositeAccuracyList(cityId, caliberId, null, nearFiveWorkDay, algoIdList);
        }
        List<Date> nearThreeWeekDay = getNearThreeWeekDay(forecastDate, holidayList);
        List<AccuracyCompositeDO> threeWeekDayCompositeAccuracyList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(algoIdList) && CollectionUtil.isNotEmpty(nearThreeWeekDay)) {
            threeWeekDayCompositeAccuracyList = accuracyCompositeService
                    .getCompositeAccuracyList(cityId, caliberId, null, nearThreeWeekDay, algoIdList);
        }
        Map<String, List<AccuracyCompositeDO>> threeHolidayCompositeAccuracyMap = threeHolidayCompositeAccuracyList.stream().collect(Collectors.groupingBy(AccuracyCompositeDO::getAlgorithmId));
        Map<String, List<AccuracyCompositeDO>> fiveWorkDayCompositeAccuracyMap = fiveWorkDayCompositeAccuracyList.stream().collect(Collectors.groupingBy(AccuracyCompositeDO::getAlgorithmId));
        Map<String, List<AccuracyCompositeDO>> threeWeekDayCompositeAccuracyMap = threeWeekDayCompositeAccuracyList.stream().collect(Collectors.groupingBy(AccuracyCompositeDO::getAlgorithmId));
        AlgorithmAccuracyAiDTO algorithmAccuracyAiDTO = new AlgorithmAccuracyAiDTO();
        List<AlgorithmAccuracyAiItemDTO> items = new ArrayList<>();
        algorithmAccuracyAiDTO.setDayType(day_type);
        for (AlgorithmDTO algorithmDTO : algorithmDTOList) {
            AlgorithmAccuracyAiItemDTO algorithmAccuracyAiItemDTO = new AlgorithmAccuracyAiItemDTO();
            if (threeHolidayCompositeAccuracyMap.containsKey(algorithmDTO.getId())) {
                List<AccuracyCompositeDO> threeHolidayAccuracyList = threeHolidayCompositeAccuracyMap.get(algorithmDTO.getId());
                if (CollectionUtil.isNotEmpty(threeHolidayAccuracyList)) {
                    List<BigDecimal> accuracyList = threeHolidayAccuracyList.stream().map(x -> x.getAccuracy()).collect(Collectors.toList());
                    BigDecimal avgAccuracy = BigDecimalUtils.avgList(accuracyList, 4, true);
                    algorithmAccuracyAiItemDTO.setThreeHolidayAccuracy(avgAccuracy);
                }
            }
            if (fiveWorkDayCompositeAccuracyMap.containsKey(algorithmDTO.getId())) {
                List<AccuracyCompositeDO> fiveWorkDayAccuracyList = fiveWorkDayCompositeAccuracyMap.get(algorithmDTO.getId());
                if (CollectionUtil.isNotEmpty(fiveWorkDayAccuracyList)) {
                    if (fiveWorkDayAccuracyList.size() > 5) {
                        fiveWorkDayAccuracyList = fiveWorkDayAccuracyList.stream()
                                .sorted(Comparator.comparing(AccuracyCompositeDO::getDate).reversed()).collect(Collectors.toList()).subList(0, 5);
                    }
                    List<BigDecimal> accuracyList = fiveWorkDayAccuracyList.stream().map(x -> x.getAccuracy()).collect(Collectors.toList());
                    BigDecimal avgAccuracy = BigDecimalUtils.avgList(accuracyList, 4, true);
                    algorithmAccuracyAiItemDTO.setFiveWorkDayAccuracy(avgAccuracy);
                }
            }
            if (threeWeekDayCompositeAccuracyMap.containsKey(algorithmDTO.getId())) {
                List<AccuracyCompositeDO> threeWeekDayAccuracyList = threeWeekDayCompositeAccuracyMap.get(algorithmDTO.getId());
                if (CollectionUtil.isNotEmpty(threeWeekDayAccuracyList)) {
                    List<BigDecimal> accuracyList = threeWeekDayAccuracyList.stream().map(x -> x.getAccuracy()).collect(Collectors.toList());
                    BigDecimal avgAccuracy = BigDecimalUtils.avgList(accuracyList, 4, true);
                    algorithmAccuracyAiItemDTO.setThreeWeekendAccuracy(avgAccuracy);
                }
            }
            getModelScore(algorithmAccuracyAiItemDTO, day_type);
            algorithmAccuracyAiItemDTO.setAlgoId(algorithmDTO.getId());
            algorithmAccuracyAiItemDTO.setAlgoName(algorithmDTO.getAlgorithm());
            items.add(algorithmAccuracyAiItemDTO);
        }
        algorithmAccuracyAiDTO.setItems(items);
        return algorithmAccuracyAiDTO;
    }

    private String parseAiRequestBody(AlgorithmAccuracyAiDTO algorithmAccuracyAiDTO) {
        List<AlgorithmAccuracyAiItemDTO> scoreBestAlgo = algorithmAccuracyAiDTO.getItems().stream().filter(t -> ObjectUtil.isNotNull(t.getScore()))
                .sorted(Comparator.comparing(AlgorithmAccuracyAiItemDTO::getScore).reversed()).collect(Collectors.toList());
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariable("items", scoreBestAlgo);
        ExpressionParser parser = new SpelExpressionParser();
        String accuracyListExpression =
                "T(String).join('\n', #items.![algoName + '|' + fiveWorkDayAccuracyPoint + '|' + threeWeekendAccuracyPoint + '|' + threeHolidayAccuracyPoint])";
        String accuracyListContent = parser.parseExpression(accuracyListExpression).getValue(context, String.class);
        String jsonBody = putContentToRequestTempalte2(algorithmAccuracyAiDTO.getDayType(), accuracyListContent, scoreBestAlgo.get(0).getAlgoName());
        log.error("---------Deepseek发送请求:{}---------" + jsonBody);
        return jsonBody;
    }

    private WeatherSourceFcAccuracyCompareDTO getTempetureAvgAccuracyMaxWeatherFc(Map<String, WeatherCityHisDO> weatherHisMap,
                                                                                  Map<String, WeatherCityFcDO> basicWeatherFcMap,
                                                                                  Map<String, WeatherCityFcBmDO> bmWeatherFcMap,
                                                                                  Map<String, WeatherCityFcMeteoDO> ecWeatherFcMap,
                                                                                  Date systemDate, String cityId) throws Exception {
        Date startDate = DateUtils.addDays(systemDate, -7);
        Date endDate = DateUtils.addDays(systemDate, -1);
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> basicAccuracyList = new ArrayList<>();
        List<BigDecimal> bmAccuracyList = new ArrayList<>();
        List<BigDecimal> ecAccuracyList = new ArrayList<>();
        List<HolidayDO> holidayList = holidayDAO.findHoliday(startDate, DateUtils.addDays(systemDate, 10));
        for (Date date : dateList) {
            String key = getWeatherTypeKey(date, WeatherEnum.TEMPERATURE.getType());
            if (!weatherHisMap.containsKey(key) || CollectionUtil.isEmpty(weatherHisMap.get(key).getWeatherList())) {
                continue;
            }
            WeatherCityHisDO weatherCityHisDO = weatherHisMap.get(key);
            if (basicWeatherFcMap.containsKey(key) && CollectionUtil.isNotEmpty(basicWeatherFcMap.get(key).getWeatherList())) {
                WeatherCityFcDO weatherCityFcDO = basicWeatherFcMap.get(key);
                List<BigDecimal> basicDailyAccuracyList = WeatherCalcUtil.calcAccuracy(weatherCityHisDO.getWeatherList(), weatherCityFcDO.getWeatherList());
                basicAccuracyList.addAll(basicDailyAccuracyList);
            }
            if (bmWeatherFcMap.containsKey(key) && CollectionUtil.isNotEmpty(bmWeatherFcMap.get(key).getWeatherList())) {
                WeatherCityFcBmDO weatherCityFcBmDO = bmWeatherFcMap.get(key);
                List<BigDecimal> bmDailyAccuracyList = WeatherCalcUtil.calcAccuracy(weatherCityHisDO.getWeatherList(), weatherCityFcBmDO.getWeatherList());
                bmAccuracyList.addAll(bmDailyAccuracyList);
            }
            if (ecWeatherFcMap.containsKey(key) && CollectionUtil.isNotEmpty(ecWeatherFcMap.get(key).getWeatherList())) {
                WeatherCityFcMeteoDO weatherCityFcMeteoDO = ecWeatherFcMap.get(key);
                List<BigDecimal> ecDailyAccuracyList = WeatherCalcUtil.calcAccuracy(weatherCityHisDO.getWeatherList(), weatherCityFcMeteoDO.getWeatherList());
                ecAccuracyList.addAll(ecDailyAccuracyList);
            }
        }
        
        WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO = new WeatherSourceFcAccuracyCompareDTO();
        getMaxAvgAccuracyOfTemperatureFc(basicAccuracyList, bmAccuracyList, ecAccuracyList, weatherSourceFcAccuracyCompareDTO);
        Map<String, ? extends BaseWeatherDO> weatherFcList = new HashMap<>();
        switch (weatherSourceFcAccuracyCompareDTO.getWeatherFcSource()) {
            case 1:
                weatherFcList = basicWeatherFcMap;
            break;
            case 2:
            weatherFcList = bmWeatherFcMap;
            break;
            case 3:
            weatherFcList = ecWeatherFcMap;
            break;
        }
        calcWeatherFeature(weatherHisMap, weatherFcList, systemDate, weatherSourceFcAccuracyCompareDTO, cityId, holidayList);
        return weatherSourceFcAccuracyCompareDTO;
    }

    private static void getMaxAvgAccuracyOfTemperatureFc(List<BigDecimal> basicAccuracyList, List<BigDecimal> bmAccuracyList, List<BigDecimal> ecAccuracyList,
                                                         WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO) {
        BigDecimal basicAvgAccuracy = CollectionUtil.isEmpty(basicAccuracyList) ? BigDecimal.ZERO : BigDecimalUtils.avgList(basicAccuracyList, 4, true);
        BigDecimal bmAvgAccuracy = CollectionUtil.isEmpty(bmAccuracyList) ? BigDecimal.ZERO : BigDecimalUtils.avgList(bmAccuracyList, 4, true);
        BigDecimal ecAvgAccuracy = CollectionUtil.isEmpty(ecAccuracyList) ? BigDecimal.ZERO : BigDecimalUtils.avgList(ecAccuracyList, 4, true);

        int weatherSource = basicAvgAccuracy.compareTo(bmAvgAccuracy) >= 0 ?
                basicAvgAccuracy.compareTo(ecAvgAccuracy) >= 0 ? 1 : 3 :
                bmAvgAccuracy.compareTo(ecAvgAccuracy) >= 0 ? 2 : 3;
        weatherSourceFcAccuracyCompareDTO.setWeatherFcSource(weatherSource);
        switch (weatherSource) {
            case 1:
                weatherSourceFcAccuracyCompareDTO.setAccuracy(basicAvgAccuracy);
                weatherSourceFcAccuracyCompareDTO.setWeatherFcSourceName("气象局气象");
                break;
            case 2:
                weatherSourceFcAccuracyCompareDTO.setAccuracy(bmAvgAccuracy);
                weatherSourceFcAccuracyCompareDTO.setWeatherFcSourceName("BM气象");
                break;
            case 3:
                weatherSourceFcAccuracyCompareDTO.setAccuracy(ecAvgAccuracy);
                weatherSourceFcAccuracyCompareDTO.setWeatherFcSourceName("EC气象");
                break;
        }
    }

    private void calcWeatherFeature(Map<String, WeatherCityHisDO> weatherHisList,
                                    Map<String,? extends BaseWeatherDO> weatherFcList, Date systemDate,
                                    WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO, String cityId, List<HolidayDO> holidayList) throws Exception {
        Date lasttDate = DateUtils.addDays(systemDate, -1);
        Date forecastDate = DateUtils.addDays(systemDate, 1);
        List<String> columns = ColumnUtil.getColumns(Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO, false);
        calcTemperatureFeatureDiff(weatherHisList, weatherFcList, lasttDate, forecastDate, weatherSourceFcAccuracyCompareDTO, columns);
        setLongestRainPeriod(weatherSourceFcAccuracyCompareDTO, weatherFcList, forecastDate, columns);
        setAvgHumidityFeatureDiff(weatherHisList, weatherFcList, lasttDate, forecastDate, weatherSourceFcAccuracyCompareDTO);
        setAvgEffectiveTemperatureDiff(weatherHisList, weatherFcList, lasttDate, forecastDate, weatherSourceFcAccuracyCompareDTO, cityId);
        setDateTypeOfDate(forecastDate, lasttDate, weatherSourceFcAccuracyCompareDTO, holidayList);
    }

    private void setAvgEffectiveTemperatureDiff(Map<String, WeatherCityHisDO> weatherHisList, Map<String,? extends BaseWeatherDO> weatherFcList,
                                                Date lasttDate, Date forecastDate, WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO, String cityId) throws Exception {
        String fcKey = getWeatherTypeKey(forecastDate, WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
        String hisKey = getWeatherTypeKey(lasttDate, WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
        List<BigDecimal> lastTemperatureList = new ArrayList<>();
        List<WeatherDTO> weatherCityHisDTOs = weatherCityHisService.findWeatherCityHisDTOs(cityId, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(), lasttDate, lasttDate);
        if (!weatherHisList.containsKey(hisKey) || !weatherFcList.containsKey(fcKey)) {
            return;
        }
        lastTemperatureList = weatherCityHisDTOs.get(0).getData();
        BaseWeatherDO fcWeather = weatherFcList.get(fcKey);
        List<BigDecimal> fcTemperatureList = BasePeriodUtils.toList(fcWeather, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> lastFeature = BasePeriodUtils.getMaxMinAvg(lastTemperatureList, 4);
        Map<String, BigDecimal> fcFeature = BasePeriodUtils.getMaxMinAvg(fcTemperatureList,4);
        weatherSourceFcAccuracyCompareDTO.setAvgEffectiveTemperatureDiff(BigDecimalFunctions.subtract(fcFeature.get("avg"), lastFeature.get("avg")));

    }

    private void setAvgHumidityFeatureDiff(Map<String, WeatherCityHisDO> weatherHisList, Map<String,? extends BaseWeatherDO> weatherFcList, 
                                           Date lasttDate, Date forecastDate, WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO) {
        String fcKey = getWeatherTypeKey(forecastDate, WeatherEnum.HUMIDITY.getType());
        String hisKey = getWeatherTypeKey(lasttDate, WeatherEnum.HUMIDITY.getType());
        if (!weatherHisList.containsKey(hisKey) || !weatherFcList.containsKey(fcKey)) {
            return;
        }
        WeatherCityHisDO weatherCityHisDO = weatherHisList.get(hisKey);
        BaseWeatherDO weatherFc = weatherFcList.get(fcKey);
        List<BigDecimal> lastTemperatureList = weatherCityHisDO.getWeatherList();
        List<BigDecimal> fcTemperatureList = BasePeriodUtils.toList(weatherFc, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        if (CollectionUtil.isEmpty(lastTemperatureList) || CollectionUtil.isEmpty(fcTemperatureList)) {
            throw TsieExceptionUtils.newBusinessException("T706", "预测日温度预测数据或者前一日历史温度为空，检查数据后重试");
        }
        Map<String, BigDecimal> lastFeature = BasePeriodUtils.getMaxMinAvg(lastTemperatureList, 4);
        Map<String, BigDecimal> fcFeature = BasePeriodUtils.getMaxMinAvg(fcTemperatureList,4);
        weatherSourceFcAccuracyCompareDTO.setAvgHumidityFeatureDiff(BigDecimalFunctions.subtract(fcFeature.get("avg"), lastFeature.get("avg")));
    }

    private void calcTemperatureFeatureDiff(Map<String, WeatherCityHisDO> weatherHisList, Map<String,? extends BaseWeatherDO> weatherFcList,
                                            Date lasttDate, Date forecastDate, WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO, List<String> columns) {
        String fcKey = getWeatherTypeKey(forecastDate, WeatherEnum.TEMPERATURE.getType());
        String hisKey = getWeatherTypeKey(lasttDate, WeatherEnum.TEMPERATURE.getType());
        if (!weatherHisList.containsKey(hisKey) || !weatherFcList.containsKey(fcKey)) {
            throw TsieExceptionUtils.newBusinessException("T701", "预测日温度预测数据或者前一日历史温度为空，检查数据后重试");
        }
        WeatherCityHisDO weatherCityHisDO = weatherHisList.get(hisKey);
        BaseWeatherDO weatherFc = weatherFcList.get(fcKey);
        List<BigDecimal> lastTemperatureList = weatherCityHisDO.getWeatherList();
        List<BigDecimal> fcTemperatureList = BasePeriodUtils.toList(weatherFc, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        if (CollectionUtil.isEmpty(lastTemperatureList) || CollectionUtil.isEmpty(fcTemperatureList)) {
            throw TsieExceptionUtils.newBusinessException("T701", "预测日温度预测数据或者前一日历史温度为空，检查数据后重试");
        }
        Map<String, BigDecimal> lastFeature = BasePeriodUtils.getMaxMinAvg(lastTemperatureList, 4);
        Map<String, BigDecimal> fcFeature = BasePeriodUtils.getMaxMinAvg(fcTemperatureList,4);
        setBasicTemperatureFeatures(weatherSourceFcAccuracyCompareDTO, fcFeature, lastFeature);
        Map<String, List<SettingAssessDO>> assessSettingByData = settingAssessService.findAssessSettingByData(lasttDate, forecastDate, weatherCityHisDO.getCityId().equals(Constants.PROVINCE_ID)
                ? Constants.CALIBER_ID_BG_QW : Constants.CALIBER_ID_BG_DS);
        if (CollectionUtil.isNotEmpty(assessSettingByData)) {
            List<SettingAssessDO> settingAssessDOS = assessSettingByData.get(DateUtils.date2String(forecastDate, DateFormatType.YEAR_MONTH_STR));
            if (CollectionUtil.isEmpty(settingAssessDOS)) {
                throw TsieExceptionUtils.newBusinessException("T701", "无考核设置，请检查数据后重试");
            }
            // 4. 时段特征差异计算
            processAssessPeriod(settingAssessDOS, BAOGONG_KEY, fcTemperatureList, lastTemperatureList, columns,
                    weatherSourceFcAccuracyCompareDTO::setBaogongAvgTemperatureDiff);

            processAssessPeriod(settingAssessDOS, NOON_KEY, fcTemperatureList, lastTemperatureList, columns,
                    weatherSourceFcAccuracyCompareDTO::setNoonAvgTemperatureDiff);

            processAssessPeriod(settingAssessDOS, NIGHT_KEY, fcTemperatureList, lastTemperatureList, columns,
                    weatherSourceFcAccuracyCompareDTO::setNightAvgTemperatureDiff);
            setMaxDiffPeriod(weatherSourceFcAccuracyCompareDTO);
        }
    }

    private void setLongestRainPeriod(WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO,
                                      Map<String, ? extends BaseWeatherDO> weatherFcMap, Date forecastDate, List<String> columns) {
        String key = getWeatherTypeKey(forecastDate, WeatherEnum.RAINFALL.getType());
        if (!weatherFcMap.containsKey(key)) {
            weatherSourceFcAccuracyCompareDTO.setRainPeriod("无降雨");
            return;
        }
        BaseWeatherDO weatherFc = weatherFcMap.get(key);
        List<BigDecimal> rainfallList = BasePeriodUtils.toList(weatherFc, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        if (CollectionUtil.isEmpty(rainfallList)) {
            weatherSourceFcAccuracyCompareDTO.setRainPeriod("无降雨");
            return;
        }
        int[] maxNonZeroInterval = findMaxNonZeroInterval(rainfallList);
        if (maxNonZeroInterval == null) {
            weatherSourceFcAccuracyCompareDTO.setRainPeriod("无降雨");
            return;
        }
        weatherSourceFcAccuracyCompareDTO.setRainPeriod(columns.get(maxNonZeroInterval[0]) + "~" + columns.get(maxNonZeroInterval[1]));
    }

    private void setBasicTemperatureFeatures(WeatherSourceFcAccuracyCompareDTO dto,
                                             Map<String, BigDecimal> fcFeatures,
                                             Map<String, BigDecimal> hisFeatures) {
        Function<String, BigDecimal> getFeature = key -> fcFeatures.getOrDefault(key, BigDecimal.ZERO);

        dto.setMaxTemperature(getFeature.apply("max"));
        dto.setAvgTemperature(getFeature.apply("avg"));
        dto.setMinTemperature(getFeature.apply("min"));

        dto.setMaxTemperatureDiff(subtractFeatures(fcFeatures, hisFeatures, "max"));
        dto.setAvgTemperatureDiff(subtractFeatures(fcFeatures, hisFeatures, "avg"));
        dto.setMinTemperatureDiff(subtractFeatures(fcFeatures, hisFeatures, "min"));

        // 判断平均温度变化趋势
        BigDecimal avgTempDiff = dto.getAvgTemperatureDiff();
        if (avgTempDiff.abs().compareTo(BigDecimal.valueOf(0.5)) <= 0) {
            dto.setTemperatureConclusion(WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_STABLE);
        } else if (avgTempDiff.compareTo(BigDecimal.ZERO) > 0) {
            dto.setTemperatureConclusion(WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_UP);
        } else {
            dto.setTemperatureConclusion(WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_DOWN);
        }
        setMaxLoadTrendConclusion(dto);
    }

    private void setMaxLoadTrendConclusion(WeatherSourceFcAccuracyCompareDTO dto) {
        BigDecimal avgTempDiff = dto.getAvgTemperatureDiff();
        BigDecimal maxTempDiff = dto.getMaxTemperatureDiff();

        boolean isAvgUp = avgTempDiff.abs().compareTo(BigDecimal.valueOf(0.5)) > 0 && avgTempDiff.compareTo(BigDecimal.ZERO) > 0;
        boolean isAvgStable = avgTempDiff.abs().compareTo(BigDecimal.valueOf(0.5)) <= 0;
        boolean isAvgDown = avgTempDiff.abs().compareTo(BigDecimal.valueOf(0.5)) > 0 && avgTempDiff.compareTo(BigDecimal.ZERO) < 0;

        boolean isMaxUp = maxTempDiff.abs().compareTo(BigDecimal.valueOf(0.5)) > 0 && maxTempDiff.compareTo(BigDecimal.ZERO) > 0;
        boolean isMaxStable = maxTempDiff.abs().compareTo(BigDecimal.valueOf(0.5)) <= 0;
        boolean isMaxDown = maxTempDiff.abs().compareTo(BigDecimal.valueOf(0.5)) > 0 && maxTempDiff.compareTo(BigDecimal.ZERO) < 0;

        if (isAvgUp) {
            if (isMaxUp || isMaxStable) {
                dto.setMaxLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            } else if (isMaxDown) {
                dto.setMaxLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_STABLE);
            }
        } else if (isAvgStable) {
            if (isMaxUp) {
                dto.setMaxLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            } else if (isMaxStable) {
                dto.setMaxLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_STABLE);
            } else if (isMaxDown) {
                dto.setMaxLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            }
        } else if (isAvgDown) {
            if (isMaxStable || isMaxDown) {
                dto.setMaxLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            } else if (isMaxUp) {
                dto.setMaxLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_STABLE);
            }
        }
    }

    private BigDecimal subtractFeatures(Map<String, BigDecimal> fc,
                                        Map<String, BigDecimal> his,
                                        String feature) {
        return BigDecimalFunctions.subtract(
                fc.getOrDefault(feature, BigDecimal.ZERO),
                his.getOrDefault(feature, BigDecimal.ZERO)
        );
    }

    private void processAssessPeriod(List<SettingAssessDO> settings,
                                     String periodName,
                                     List<BigDecimal> fcTemps,
                                     List<BigDecimal> hisTemps,
                                     List<String> columns,
                                     Consumer<BigDecimal> resultSetter) {

        List<SettingAssessDO> periodSettings = settings.stream()
                .filter(s -> s.getAssessName().contains(periodName))
                .map(this::copyAsAvgType)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(periodSettings)) {
            return;
        }

        BigDecimal[] results = calculatePeriodAverages(periodSettings, fcTemps, hisTemps, columns);
        resultSetter.accept(BigDecimalFunctions.subtract(results[0], results[1]));
    }

    private SettingAssessDO copyAsAvgType(SettingAssessDO original) {
        SettingAssessDO copy = new SettingAssessDO();
        BeanUtils.copyProperties(original, copy);
        copy.setType(AssessEnum.AVG.getType());
        return copy;
    }

    private BigDecimal[] calculatePeriodAverages(List<SettingAssessDO> settings,
                                                 List<BigDecimal> fcTemps,
                                                 List<BigDecimal> hisTemps,
                                                 List<String> columns) {

        BigDecimal fcSum = BigDecimal.ZERO;
        BigDecimal hisSum = BigDecimal.ZERO;
        int count = settings.size();

        for (SettingAssessDO setting : settings) {
            AccessStatisResultDTO fcResult = accuracyAssessService.getAccessLoad(fcTemps, setting, columns);
            AccessStatisResultDTO hisResult = accuracyAssessService.getAccessLoad(hisTemps, setting, columns);

            fcSum = fcSum.add(fcResult.getResult());
            hisSum = hisSum.add(hisResult.getResult());
        }

        return new BigDecimal[] {
                fcSum.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP),
                hisSum.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP)
        };
    }

    private int[] findMaxNonZeroInterval(List<BigDecimal> list) {
        if (list == null || list.isEmpty()) {
            return null; // 根据需求返回空或抛出异常
        }

        int maxStart = -1;
        int maxEnd = -1;
        int currentStart = -1;
        int maxLength = 0;

        for (int i = 0; i < list.size(); i++) {
            BigDecimal num = list.get(i);
            boolean isValid = num != null && num.compareTo(BigDecimal.ZERO) != 0;

            if (isValid) {
                if (currentStart == -1) { // 新区间开始
                    currentStart = i;
                }
                int currentLength = i - currentStart + 1;
                if (currentLength > maxLength) {
                    maxLength = currentLength;
                    maxStart = currentStart;
                    maxEnd = i;
                }
            } else { // 遇到无效值，重置指针
                currentStart = -1;
            }
        }

        return (maxStart != -1) ? new int[]{maxStart, maxEnd} : null;
    }

    private String getWeatherTypeKey(Date forecastDate, Integer type) {
        return type + DateUtils.date2String(forecastDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
    }

    private void setMaxDiffPeriod(WeatherSourceFcAccuracyCompareDTO weatherSourceFcAccuracyCompareDTO) {
        // 获取三个时段的温度差值
        BigDecimal baogongDiff = weatherSourceFcAccuracyCompareDTO.getBaogongAvgTemperatureDiff();
        BigDecimal noonDiff = weatherSourceFcAccuracyCompareDTO.getNoonAvgTemperatureDiff();
        BigDecimal nightDiff = weatherSourceFcAccuracyCompareDTO.getNightAvgTemperatureDiff();

        // 使用三元运算符找出最大值及其对应的时段
        String maxPeriod = BAOGONG_KEY;

        BigDecimal maxDiff = baogongDiff == null ? BigDecimal.ZERO : baogongDiff.abs();

        if (noonDiff != null && noonDiff.abs().compareTo(maxDiff) > 0) {
            maxDiff = noonDiff.abs();
            maxPeriod = NOON_KEY;
        }

        if (nightDiff != null && nightDiff.abs().compareTo(maxDiff) > 0) {
            maxDiff = nightDiff.abs();
            maxPeriod = NIGHT_KEY;
        }

        // 设置最大时段到 changeMaxPeriod 字段
        weatherSourceFcAccuracyCompareDTO.setChangeMaxPeriod(maxPeriod);
    }

    private void setDateTypeOfDate(Date forecastDate, Date lasttDate,WeatherSourceFcAccuracyCompareDTO dto, List<HolidayDO> holidayList) {
        dto.setForecastDate(DateUtils.date2String(forecastDate, DateFormatType.SIMPLE_DATE_CHINESE));
        dto.setCompareDate(DateUtils.date2String(lasttDate, DateFormatType.SIMPLE_DATE_CHINESE));
        dto.setForecastDateType(getDayType(forecastDate, holidayList));
        dto.setCompareDateType(getDayType(lasttDate, holidayList));
        String dateTypeChange = dto.getForecastDateType().equals(dto.getCompareDateType()) ? WeatherSourceFcAccuracyCompareDTO.DATE_TYPE_CHANGE_NO :
                WeatherSourceFcAccuracyCompareDTO.DATE_TYPE_CHANGE_YES;
        dto.setDateTypeChange(dateTypeChange);
        String temperatureConclusion = dto.getTemperatureConclusion();
        if ("工作日".equals(dto.getCompareDateType()) && "节假日".equals(dto.getForecastDateType())) {
            dto.setSocialLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_STABLE.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_UP.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_DOWN.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            }
            dto.setDateTypeChangeConclusion("由" + dto.getCompareDateType() + "转移为" + dto.getForecastDateType());
        } else if ("工作日".equals(dto.getCompareDateType()) && "周末".equals(dto.getForecastDateType())) {
            dto.setSocialLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_STABLE.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_UP.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_DOWN.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            }
            dto.setDateTypeChangeConclusion("由" + dto.getCompareDateType() + "转移为" + dto.getForecastDateType());
        } else if ("周末".equals(dto.getCompareDateType()) && "工作日".equals(dto.getForecastDateType())) {
            dto.setSocialLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_STABLE.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_UP.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_DOWN.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            }
            dto.setDateTypeChangeConclusion("由" + dto.getCompareDateType() + "转移为" + dto.getForecastDateType());
        } else if ("节假日".equals(dto.getCompareDateType()) && "工作日".equals(dto.getForecastDateType())) {
            dto.setSocialLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_STABLE.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_UP.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_DOWN.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            }
            dto.setDateTypeChangeConclusion("由" + dto.getCompareDateType() + "转移为" + dto.getForecastDateType());
        } else {
            dto.setSocialLoadForecast(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_STABLE);
            dto.setDateTypeChangeConclusion("无变化");
            if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_STABLE.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_STABLE);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_UP.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_UP);
            } else if (WeatherSourceFcAccuracyCompareDTO.TEMP_DIFF_CONCLUSION_DOWN.equals(temperatureConclusion)) {
                dto.setLoadChangeConclusion(WeatherSourceFcAccuracyCompareDTO.MAX_LOAD_FORECAST_DOWN);
            }
        }
    }
}
