package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.common.util.ExcelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Slf4j
@Service("weatherCityRpaService")
public class WeatherCityRpaServiceImpl implements WeatherCityRpaService {

    private static Map<String, String> cityNameIdMap;
    private static Map<String, String> stationNameIdMap;

    static {
        cityNameIdMap = new HashMap<>(16);
        cityNameIdMap.put("潜江", "21");

        stationNameIdMap = new HashMap<>(64);
        stationNameIdMap.put("微软气象源", "1");
        stationNameIdMap.put("综合", "2");
        stationNameIdMap.put("综合-体感", "2");
        stationNameIdMap.put("欧洲-44", "3");
        stationNameIdMap.put("欧洲-44-体感", "3");
        stationNameIdMap.put("欧洲-44 -体感", "3");
        stationNameIdMap.put("欧洲-25", "4");
        stationNameIdMap.put("欧洲-25-体感", "4");
        stationNameIdMap.put("欧洲-28", "5");
        stationNameIdMap.put("欧洲-28-体感", "5");
        stationNameIdMap.put("中国", "6");
        stationNameIdMap.put("中国-体感", "6");
        stationNameIdMap.put("澳洲", "7");
        stationNameIdMap.put("澳洲-体感", "7");
        stationNameIdMap.put("美国", "8");
        stationNameIdMap.put("美国-体感", "8");
        stationNameIdMap.put("日本", "9");
        stationNameIdMap.put("日本-体感", "9");
        stationNameIdMap.put("德国", "10");
        stationNameIdMap.put("德国-体感", "10");
        stationNameIdMap.put("加拿大", "11");
        stationNameIdMap.put("加拿大-体感", "11");
        stationNameIdMap.put("法国", "12");
        stationNameIdMap.put("法国-体感", "12");
        stationNameIdMap.put("挪威", "13");
        stationNameIdMap.put("挪威-体感", "13");
        stationNameIdMap.put("荷兰", "14");
        stationNameIdMap.put("荷兰-体感", "14");
        stationNameIdMap.put("丹麦", "15");
        stationNameIdMap.put("丹麦-体感", "15");
    }

    @Autowired
    WeatherCityHisRpaService weatherCityHisRpaService;
    @Autowired
    WeatherCityFcCoverRpaService weatherCityFcCoverRpaService;
    @Autowired
    WeatherCityFcRpaService weatherCityFcRpaService;
    @Autowired
    CityService cityService;

    @Autowired
    private WeatherStatService weatherStatService;

    @Autowired
    private WeatherCityFcMeteoService weatherCityFcMeteoService;

    @Autowired
    private WeatherCityFcBmService weatherCityFcBmService;

    @Autowired
    private WeatherCityFcMeteoBatchService weatherCityFcMeteoBatchService;

    @Autowired
    private WeatherCityFcBmBatchService weatherCityFcBmBatchService;

    @Autowired
    private WeatherCityHisMeteoService weatherCityHisMeteoService;

    @Override
    public void weatherUpload(InputStream inputStream, Integer type) {
        if (type == 1) {
            // 预测
            uploadFcData(inputStream);
        } else {
            // 实际
            uploadHisData(inputStream);
        }
    }

    public void uploadFcData(InputStream inputStream) {
        Map<String, List<Map<Integer, Object>>> listStringMap = null;
        try {
            listStringMap = ExcelUtil.resolveList(inputStream, false);
        } catch (IOException e) {
            log.error("解析文件失败", e);
        }

        if (this.isMeteoSourceFile(listStringMap.keySet())) {
            this.processMeteoSourceFcData(listStringMap, inputStream);
        } else {
            this.processRpaSourceFcData(listStringMap, inputStream);
        }
    }

    @SneakyThrows
    private void processMeteoSourceFcData(Map<String, List<Map<Integer, Object>>> listStringMap, InputStream inputStream) {
        try {
            String batchId = this.getBatchId();
            java.sql.Date curDate = null;
            // 记录一下日期
            List<Date> dateList = new ArrayList<>();
            for (Entry<String, List<Map<Integer, Object>>> listStringEntry : listStringMap.entrySet()) {
                String sheetName = listStringEntry.getKey();
                List<Map<Integer, Object>> values = listStringEntry.getValue();
                if (CollectionUtils.isEmpty(values)) {
                    continue;
                }
                Map<String, String> cityNameIdMap = cityService.findCityByLevel(2).stream()
                        .collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
                values.subList(0, 1).clear();
                for (Map<Integer, Object> map : values) {
                    String city = map.get(0).toString();
                    String dateTime = map.get(1).toString();
                    Map<String, BigDecimal> mapPoint = new HashMap<>(32);
                    for (int i = 2; i <= 26; i++) {
                        if ((i - 2) < 10) {
                            if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                mapPoint.put("t0" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                            }
                        } else {
                            if (i == 26 && map.get(i) == null) {
                                if (map.get(i - 1) != null && !"null".equals(map.get(i - 1)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                    mapPoint.put("t" + (i - 2) + "00", new BigDecimal(map.get(i - 1).toString()));
                                }
                            } else {
                                if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                    mapPoint.put("t" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                                }
                            }
                        }
                    }
                    if (MapUtils.isEmpty(mapPoint)) {
                        continue;
                    }
                    this.supplimentPoit(mapPoint);
                    dateList.add(new java.sql.Date(DateUtil.getDate(dateTime, "yyyy-MM-dd").getTime()));
                    if (sheetName.contains("meteo-")) {
                        WeatherCityFcMeteoDO weatherCityFcMeteoDO = new WeatherCityFcMeteoDO();
                        weatherCityFcMeteoDO.setCityId(this.getCityIdByCityName(city, cityNameIdMap));
                        java.sql.Date date = new java.sql.Date(DateUtil.getDate(dateTime, "yyyy-MM-dd").getTime());
                        curDate = date;
                        weatherCityFcMeteoDO.setDate(date);
                        weatherCityFcMeteoDO.setType(this.getWeatherType(sheetName));
                        BasePeriodUtils.setAllFiled(weatherCityFcMeteoDO, mapPoint);
                        weatherCityFcMeteoService.insertYesterday24HourData(weatherCityFcMeteoDO);
                        weatherCityFcMeteoService.doSaveOrUpdate(weatherCityFcMeteoDO);

                        WeatherCityFcMeteoBatchDO weatherCityFcMeteoBatchDO = new WeatherCityFcMeteoBatchDO();
                        BeanUtils.copyProperties(weatherCityFcMeteoDO, weatherCityFcMeteoBatchDO);
                        weatherCityFcMeteoBatchDO.setBatchId(batchId);
                        weatherCityFcMeteoBatchService.insertYesterday24HourData(weatherCityFcMeteoBatchDO);
                        weatherCityFcMeteoBatchService.doSaveOrUpdate(weatherCityFcMeteoBatchDO);
                    } else if (sheetName.contains("meteo(BM)-")) {
                        WeatherCityFcBmDO weatherCityFcBmDO = new WeatherCityFcBmDO();
                        weatherCityFcBmDO.setCityId(this.getCityIdByCityName(city, cityNameIdMap));
                        java.sql.Date date = new java.sql.Date(DateUtil.getDate(dateTime, "yyyy-MM-dd").getTime());
                        curDate = date;
                        weatherCityFcBmDO.setDate(date);
                        weatherCityFcBmDO.setType(this.getWeatherType(sheetName));
                        BasePeriodUtils.setAllFiled(weatherCityFcBmDO, mapPoint);
                        weatherCityFcBmService.insertYesterday24HourData(weatherCityFcBmDO);
                        weatherCityFcBmService.doSaveOrUpdate(weatherCityFcBmDO);

                        WeatherCityFcBmBatchDO weatherCityFcBmBatchDO = new WeatherCityFcBmBatchDO();
                        BeanUtils.copyProperties(weatherCityFcBmDO, weatherCityFcBmBatchDO);
                        weatherCityFcBmBatchDO.setBatchId(batchId);
                        weatherCityFcBmBatchService.insertYesterday24HourData(weatherCityFcBmBatchDO);
                        weatherCityFcBmBatchService.doSaveOrUpdate(weatherCityFcBmBatchDO);
                    }
                }
            }

            //计算meteo(BM)气象源省气象数据
            List<WeatherCityFcBmDO> weatherCityFcBmDOS = weatherCityFcBmService.getListByCondition(null, null, curDate, curDate);
            List<WeatherCityFcBmDO> bmDOS = weatherStatService.calcProvinceWeather(weatherCityFcBmDOS, WeatherCityFcBmDO.class);
            for (WeatherCityFcBmDO weatherCityFcBmDO : bmDOS) {
                weatherCityFcBmService.doSaveOrUpdate(weatherCityFcBmDO);
                weatherCityFcBmService.insertYesterday24HourData(weatherCityFcBmDO);
            }

            //计算meteo气象源省气象数据
            List<WeatherCityFcMeteoDO> weatherCityFcMeteoDOS = weatherCityFcMeteoService.getListByCondition(null, null, curDate, curDate);
            List<WeatherCityFcMeteoDO> meteoDOS = weatherStatService.calcProvinceWeather(weatherCityFcMeteoDOS, WeatherCityFcMeteoDO.class);
            for (WeatherCityFcMeteoDO weatherCityFcMeteoDO : meteoDOS) {
                weatherCityFcMeteoService.doSaveOrUpdate(weatherCityFcMeteoDO);
                weatherCityFcMeteoService.insertYesterday24HourData(weatherCityFcMeteoDO);
            }
            dateList.stream().distinct().forEach(
                    date -> {
                        // 计算体感温度（meteo）
                        List<WeatherCityFcMeteoBatchDO> weatherCityFcDO = weatherCityFcMeteoBatchService.findWeatherCityFcDO(null, null, date, null);
                        if (!CollectionUtils.isEmpty(weatherCityFcDO)) {
                            Map<String, List<WeatherCityFcMeteoBatchDO>> collect = weatherCityFcDO.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStr(t.getDate()) + "-" + t.getCityId() + "-" + t.getBatchId()));
                            for (Map.Entry<String, List<WeatherCityFcMeteoBatchDO>> stringListEntry : collect.entrySet()) {
                                List<WeatherCityFcMeteoBatchDO> value = stringListEntry.getValue();
                                if (!org.springframework.util.CollectionUtils.isEmpty(value)) {
                                    Map<Integer, List<WeatherCityFcMeteoBatchDO>> collectType = value.stream().collect(Collectors.groupingBy(t -> t.getType()));
                                    List<BigDecimal> list = new ArrayList<>();
                                    if (!org.springframework.util.CollectionUtils.isEmpty(collectType.get(2)) && !org.springframework.util.CollectionUtils.isEmpty(collectType.get(4))
                                            && !org.springframework.util.CollectionUtils.isEmpty(collectType.get(1))) {
                                        for (int i = 0; i < 96; i++) {
                                            BigDecimal bigDecimal = BigDecimalUtils.effTem(collectType.get(2).get(0).getWeatherList().get(i),
                                                    collectType.get(4).get(0).getWeatherList().get(i),
                                                    collectType.get(1).get(0).getWeatherList().get(i));
                                            list.add(bigDecimal);
                                        }
                                        WeatherCityFcMeteoBatchDO weatherCityHisDO = new WeatherCityFcMeteoBatchDO();
                                        weatherCityHisDO.setCityId(value.get(0).getCityId());
                                        weatherCityHisDO.setDate(value.get(0).getDate());
                                        weatherCityHisDO.setType(WeatherEnum.EFFECTIVE_NEW_TEMPERATURE.getType());
                                        weatherCityHisDO.setBatchId(value.get(0).getBatchId());
                                        weatherCityHisDO.setCreatetime(value.get(0).getCreatetime());
                                        weatherCityHisDO.setUpdatetime(value.get(0).getUpdatetime());
                                        try {
                                            BasePeriodUtils.setAllFiled(weatherCityHisDO,
                                                    ColumnUtil.listToMap(list, Constants.LOAD_CURVE_START_WITH_ZERO));
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                        try {
                                            weatherCityFcMeteoBatchService.doSaveOrUpdate(weatherCityHisDO);
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                    }
                                }
                            }
                        }
                        // 计算体感温度（bm）
                        List<WeatherCityFcBmBatchDO> weatherCityFcDOS = weatherCityFcBmBatchService.findWeatherCityFcDO(null, null, date, null);
                        if (!CollectionUtils.isEmpty(weatherCityFcDOS)) {
                            Map<String, List<WeatherCityFcBmBatchDO>> collect = weatherCityFcDOS.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStr(t.getDate()) + "-" + t.getCityId() + "-" + t.getBatchId()));
                            for (Map.Entry<String, List<WeatherCityFcBmBatchDO>> stringListEntry : collect.entrySet()) {
                                List<WeatherCityFcBmBatchDO> value = stringListEntry.getValue();
                                if (!org.springframework.util.CollectionUtils.isEmpty(value)) {
                                    Map<Integer, List<WeatherCityFcBmBatchDO>> collectType = value.stream().collect(Collectors.groupingBy(t -> t.getType()));
                                    List<BigDecimal> list = new ArrayList<>();
                                    if (!org.springframework.util.CollectionUtils.isEmpty(collectType.get(2)) && !org.springframework.util.CollectionUtils.isEmpty(collectType.get(4))
                                            && !org.springframework.util.CollectionUtils.isEmpty(collectType.get(1))) {
                                        for (int i = 0; i < 96; i++) {
                                            BigDecimal bigDecimal = BigDecimalUtils.effTem(collectType.get(2).get(0).getWeatherList().get(i),
                                                    collectType.get(4).get(0).getWeatherList().get(i),
                                                    collectType.get(1).get(0).getWeatherList().get(i));
                                            list.add(bigDecimal);
                                        }
                                        WeatherCityFcBmBatchDO weatherCityHisDO = new WeatherCityFcBmBatchDO();
                                        weatherCityHisDO.setCityId(value.get(0).getCityId());
                                        weatherCityHisDO.setDate(value.get(0).getDate());
                                        weatherCityHisDO.setType(WeatherEnum.EFFECTIVE_NEW_TEMPERATURE.getType());
                                        weatherCityHisDO.setBatchId(value.get(0).getBatchId());
                                        weatherCityHisDO.setCreatetime(value.get(0).getCreatetime());
                                        weatherCityHisDO.setUpdatetime(value.get(0).getUpdatetime());
                                        try {
                                            BasePeriodUtils.setAllFiled(weatherCityHisDO,
                                                    ColumnUtil.listToMap(list, Constants.LOAD_CURVE_START_WITH_ZERO));
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                        try {
                                            weatherCityFcBmBatchService.doSaveOrUpdate(weatherCityHisDO);
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                    }
                                }
                            }
                        }
                    }
            );

        } catch (Exception e) {
            log.error("解析文件失败", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭文件失败", e);
                }
            }
        }
    }

    private Integer getWeatherType(String sheetName) {
        Integer type = 2;
        if (sheetName.contains("体感")) {
            type = WeatherEnum.EFFECTIVE_TEMPERATURE.getType();
        }
        if (sheetName.contains("湿度")) {
            type = WeatherEnum.HUMIDITY.getType();
        }
        if (sheetName.contains("风速")) {
            type = WeatherEnum.WINDSPEED.getType();
        }
        if (sheetName.contains("降水")) {
            type = WeatherEnum.RAINFALL.getType();
        }
        return type;
    }

    private String getCityIdByCityName(String cityName, Map<String, String> cityNameIdMap) {
        for (Entry<String, String> cityEntry : cityNameIdMap.entrySet()) {
            if (cityName.contains(cityEntry.getKey())) {
                return cityEntry.getValue();
            }
        }
        log.error("没有找到城市：" + cityName);
        return null;
    }

    private String getBatchId() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH00");
        Date now = new Date();
        return sdf.format(now);
    }

    private void processRpaSourceFcData(Map<String, List<Map<Integer, Object>>> listStringMap, InputStream
            inputStream) {
        try {
            int d = 1;

            for (Entry<String, List<Map<Integer, Object>>> listStringEntry : listStringMap.entrySet()) {
                String value = listStringEntry.getKey();
                if ("法国".equals(value)) {
                    System.out.println();
                }
                List<Map<Integer, Object>> list = listStringEntry.getValue();
                System.out.println("开始计算：" + value);
                if (CollectionUtils.isNotEmpty(list)) {
                    list.subList(0, 1).clear();
                    Map<String, String> collect = cityService.findCityByLevel(2).stream()
                            .collect(Collectors.toMap(t -> t.getCity(), t -> t.getId()));
                    collect.put("潜江", "21");
                    List<WeatherCityFcRpaCoverDO> weatherCityFcRpaCoverDOList = new ArrayList<>();
                    List<WeatherCityFcRpaDO> weatherCityFcRpaDOList = new ArrayList<>();
                    for (Map<Integer, Object> map : list) {
                        String city = null;
                        String dateTime = null;
                        Map<String, BigDecimal> mapPoint = new HashMap<>(32);
                        if ("微软气象源".equals(value)) {
                            city = map.get(0).toString();
                            dateTime = map.get(1).toString();
                            for (int i = 3; i <= 27; i++) {
                                if ((i - 3) < 10) {
                                    if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                        mapPoint.put("t0" + (i - 3) + "00", new BigDecimal(map.get(i).toString()));
                                    }
                                } else {
                                    if (i == 27 && map.get(i) == null) {
                                        if (map.get(i - 1) != null && !"null".equals(map.get(i - 1)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                            mapPoint.put("t" + (i - 3) + "00", new BigDecimal(map.get(i - 1).toString()));
                                        }
                                    } else {
                                        if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                            mapPoint.put("t" + (i - 3) + "00", new BigDecimal(map.get(i).toString()));
                                        }
                                    }
                                }
                            }
                        } else {
                            city = map.get(0).toString();
                            dateTime = map.get(1).toString();
                            for (int i = 2; i <= 26; i++) {
                                if ((i - 2) < 10) {
                                    if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                        mapPoint.put("t0" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                                    }
                                } else {
                                    if (i == 26 && map.get(i) == null) {
                                        if (map.get(i - 1) != null && !"null".equals(map.get(i - 1)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                            mapPoint.put("t" + (i - 2) + "00", new BigDecimal(map.get(i - 1).toString()));
                                        }
                                    } else {
                                        if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                            mapPoint.put("t" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                                        }
                                    }
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(mapPoint)) {
                            supplimentPoit(mapPoint);
                        } else {
                            continue;
                        }
                        WeatherCityFcRpaCoverDO weatherCityFcRpaCoverDO = new WeatherCityFcRpaCoverDO();
                        weatherCityFcRpaCoverDO.setCityId(collect.get(city));
                        weatherCityFcRpaCoverDO.setDate(new java.sql.Date(DateUtil.getDate(dateTime, "yyyyMMdd").getTime()));
                        weatherCityFcRpaCoverDO.setType(2);
                        if (value.contains("体感")) {
                            weatherCityFcRpaCoverDO.setType(5);
                        }
                        weatherCityFcRpaCoverDO.setStationId(stationNameIdMap.get(value));
                        BasePeriodUtils.setAllFiled(weatherCityFcRpaCoverDO, mapPoint);

                        weatherCityFcRpaCoverDOList.add(weatherCityFcRpaCoverDO);
                        WeatherCityFcRpaDO weatherCityFcRpaDO = new WeatherCityFcRpaDO();
                        weatherCityFcRpaDO.setCityId(collect.get(city));
                        weatherCityFcRpaDO.setDate(new java.sql.Date(DateUtil.getDate(dateTime, "yyyyMMdd").getTime()));
                        weatherCityFcRpaDO.setUploadDate(new java.sql.Date(System.currentTimeMillis()));
                        weatherCityFcRpaDO.setType(2);
                        if (value.contains("体感")) {
                            weatherCityFcRpaDO.setType(5);
                        }
                        weatherCityFcRpaDO.setStationId(stationNameIdMap.get(value));
                        BasePeriodUtils.setAllFiled(weatherCityFcRpaDO, mapPoint);
                        weatherCityFcRpaDOList.add(weatherCityFcRpaDO);
                    }
                    try {
                        // 覆盖
                        d++;
                        weatherCityFcCoverRpaService.doSave(weatherCityFcRpaCoverDOList);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        // 不覆盖
                        weatherCityFcRpaService.doSave(weatherCityFcRpaDOList);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                System.out.println("开始计算：" + value + "成功");
            }
            System.out.println(d);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void uploadHisData(InputStream inputStream) {
        Map<String, List<Map<Integer, Object>>> listStringMap = null;
        try {
            listStringMap = ExcelUtil.resolveList(inputStream, false);
        } catch (IOException e) {
            log.error("解析文件失败", e);
        }
        if (this.isMeteoSourceFile(listStringMap.keySet())) {
            this.processMeteoSourceHisData(listStringMap, inputStream);
        } else {
            this.processRpaSourceHisData(listStringMap, inputStream);
        }
    }

    private void processRpaSourceHisData(Map<String, List<Map<Integer, Object>>> listStringMap, InputStream inputStream) {
        try {
            for (Entry<String, List<Map<Integer, Object>>> listStringEntry : listStringMap.entrySet()) {
                List<Map<Integer, Object>> list = listStringEntry.getValue();
                String value = listStringEntry.getKey();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.subList(0, 1).clear();
                    Map<String, String> collect = cityService.findCityByLevel(2).stream()
                            .collect(Collectors.toMap(t -> t.getCity(), t -> t.getId()));
                    collect.put("潜江", "21");
                    List<WeatherCityHisRpaDO> weatherCityHisRpaDOList = new ArrayList<>();
                    for (Map<Integer, Object> map : list) {
                        // 地区
                        String city = map.get(0).toString();
                        // 日期
                        String dateTime = map.get(1).toString();
                        Map<String, BigDecimal> mapPoint = new HashMap<>(32);
                        for (int i = 2; i <= 26; i++) {
                            if ((i - 2) < 10) {
                                mapPoint.put("t0" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                            } else {
                                mapPoint.put("t" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                            }
                        }
                        supplimentPoit(mapPoint);
                        WeatherCityHisRpaDO weatherCityHisRpaDO = new WeatherCityHisRpaDO();
                        weatherCityHisRpaDO.setCityId(collect.get(city));
                        weatherCityHisRpaDO.setDate(new java.sql.Date(DateUtil.getDate(dateTime, "yyyyMMdd").getTime()));
                        weatherCityHisRpaDO.setType(2);
                        if (value.contains("体感")) {
                            weatherCityHisRpaDO.setType(5);
                        }
                        weatherCityHisRpaDO.setStationId(stationNameIdMap.get(value));
                        BasePeriodUtils.setAllFiled(weatherCityHisRpaDO, mapPoint);
                        weatherCityHisRpaDOList.add(weatherCityHisRpaDO);
                        System.out.println();
                    }
                    weatherCityHisRpaService.doSaveOrUpdate(weatherCityHisRpaDOList);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void processMeteoSourceHisData(Map<String, List<Map<Integer, Object>>> listStringMap, InputStream inputStream) {
        try {
            for (Entry<String, List<Map<Integer, Object>>> listStringEntry : listStringMap.entrySet()) {
                String sheetName = listStringEntry.getKey();
                List<Map<Integer, Object>> values = listStringEntry.getValue();
                if (CollectionUtils.isEmpty(values)) {
                    continue;
                }
                Map<String, String> cityNameIdMap = cityService.findCityByLevel(2).stream()
                        .collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
                values.subList(0, 1).clear();
                for (Map<Integer, Object> map : values) {
                    String city = map.get(0).toString();
                    String dateTime = map.get(1).toString();
                    Map<String, BigDecimal> mapPoint = new HashMap<>(32);
                    for (int i = 2; i <= 26; i++) {
                        if ((i - 2) < 10) {
                            if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                mapPoint.put("t0" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                            }
                        } else {
                            if (i == 26 && map.get(i) == null) {
                                if (map.get(i - 1) != null && !"null".equals(map.get(i - 1)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                    mapPoint.put("t" + (i - 2) + "00", new BigDecimal(map.get(i - 1).toString()));
                                }
                            } else {
                                if (map.get(i) != null && !"null".equals(map.get(i)) && ObjectUtils.isNotEmpty(map.get(i))) {
                                    mapPoint.put("t" + (i - 2) + "00", new BigDecimal(map.get(i).toString()));
                                }
                            }
                        }
                    }
                    if (MapUtils.isEmpty(mapPoint)) {
                        continue;
                    }
                    this.supplimentPoit(mapPoint);

                    WeatherCityHisMeteoDO weatherCityHisMeteoDO = new WeatherCityHisMeteoDO();
                    weatherCityHisMeteoDO.setCityId(this.getCityIdByCityName(city, cityNameIdMap));
                    weatherCityHisMeteoDO.setDate(new java.sql.Date(DateUtil.getDate(dateTime, "yyyy-MM-dd").getTime()));
                    weatherCityHisMeteoDO.setType(this.getWeatherType(sheetName));
                    BasePeriodUtils.setAllFiled(weatherCityHisMeteoDO, mapPoint);
                    weatherCityHisMeteoService.insertYesterday24HourData(weatherCityHisMeteoDO);
                    weatherCityHisMeteoService.doSaveOrUpdate(weatherCityHisMeteoDO);
                }
            }
        } catch (Exception e) {
            log.error("解析文件失败", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭文件失败", e);
                }
            }
        }
    }

    private void supplimentPoit(Map<String, BigDecimal> value) {
        for (int i = 0; i <= 23; i++) {
            String first_field = "t" + (i < 10 ? "0" + i : i) + "00";
            String next_field = "t" + ((i + 1) < 10 ? "0" + (i + 1) : (i + 1)) + "00";
            BigDecimal firstValue = value.get(first_field);
            BigDecimal nextValue = value.get(next_field);
            if (firstValue == null || nextValue == null) {
                continue;
            }
            BigDecimal devation = nextValue.subtract(firstValue);
            BigDecimal avgDevation = devation.divide(new BigDecimal(4), 2, BigDecimal.ROUND_HALF_UP);
            value.put(first_field.substring(0, 3) + "15", firstValue.add(avgDevation));
            value.put(first_field.substring(0, 3) + "30", firstValue.add(avgDevation.multiply(new BigDecimal(2))));
            value.put(first_field.substring(0, 3) + "45", firstValue.add(avgDevation.multiply(new BigDecimal(3))));
        }
    }

    private boolean isMeteoSourceFile(Set<String> sheetNames) {
        for (String sheetName : sheetNames) {
            if (sheetName.contains("meteo")) {
                return true;
            }
        }
        return false;
    }
}
