package com.tsintergy.lf.serviceimpl.weather.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureSourceTenDaysFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureSourceTenDaysFcDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureSourceTenDaysFcDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("weatherFeatureSourceTenDaysFcService")
public class WeatherFeatureSourceTenDaysFcServiceImpl implements WeatherFeatureSourceTenDaysFcService {

    @Autowired
    private WeatherFeatureSourceTenDaysFcDAO weatherFeatureSourceTenDaysFcDAO;

    @Override
    public List<WeatherFeatureSourceTenDaysFcDO> findByParam(String cityId, String startYm, String endYm, String sourceType) throws Exception {
        List<WeatherFeatureSourceTenDaysFcDO> all = weatherFeatureSourceTenDaysFcDAO.findAll(JpaWrappers.<WeatherFeatureSourceTenDaysFcDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), WeatherFeatureSourceTenDaysFcDO::getCityId, cityId)
                .eq(WeatherFeatureSourceTenDaysFcDO::getSourceType, sourceType)
                .ge(WeatherFeatureSourceTenDaysFcDO::getYm, startYm)
                .le(WeatherFeatureSourceTenDaysFcDO::getYm, endYm)
        );
        return all;
    }

    @Override
    public void saveOrUpdate(WeatherFeatureSourceTenDaysFcDO weatherFeatureSourceTenDaysFcDO) throws Exception {
        List<WeatherFeatureSourceTenDaysFcDO> all = weatherFeatureSourceTenDaysFcDAO.findAll(JpaWrappers.<WeatherFeatureSourceTenDaysFcDO>lambdaQuery()
                .eq(WeatherFeatureSourceTenDaysFcDO::getCityId, weatherFeatureSourceTenDaysFcDO.getCityId())
                .eq(WeatherFeatureSourceTenDaysFcDO::getSourceType, weatherFeatureSourceTenDaysFcDO.getSourceType())
                .eq(WeatherFeatureSourceTenDaysFcDO::getYm, weatherFeatureSourceTenDaysFcDO.getYm())
                .eq(WeatherFeatureSourceTenDaysFcDO::getType, weatherFeatureSourceTenDaysFcDO.getType())
        );
        if (CollectionUtils.isEmpty(all)) {
            weatherFeatureSourceTenDaysFcDAO.save(weatherFeatureSourceTenDaysFcDO);
        } else {
            String id = all.get(0).getId();
            weatherFeatureSourceTenDaysFcDO.setId(id);
            weatherFeatureSourceTenDaysFcDAO.saveOrUpdateByTemplate(weatherFeatureSourceTenDaysFcDO);
        }
    }
}
