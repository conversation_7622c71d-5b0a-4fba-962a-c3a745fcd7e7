package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.trade.api.IndustryCityLoadDayHisClctService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.IndustryCityLoadDayHisClctDO;
import com.tsintergy.lf.serviceimpl.trade.dao.IndustryCityLoadDayHisClctDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Service("industryCityLoadDayHisClctSerivce")
public class IndustryCityLoadDayHisClctServiceImpl implements IndustryCityLoadDayHisClctService {

    @Autowired
    private IndustryCityLoadDayHisClctDAO industryCityLoadDayHisClctDAO;

    @Override
    public List<IndustryCityLoadDayHisClctDO> findIndustryCityLoadDayHisClctDOS(String cityId, List<String> tradeCodes, Date startDate, Date endDate) {
        return industryCityLoadDayHisClctDAO.findAll(JpaWrappers.<IndustryCityLoadDayHisClctDO>lambdaQuery()
                .in(!CollectionUtils.isEmpty(tradeCodes), IndustryCityLoadDayHisClctDO::getTradeCode, tradeCodes)
                .eq(!StringUtils.isEmpty(cityId), IndustryCityLoadDayHisClctDO::getCityId, cityId)
                .le(endDate != null, IndustryCityLoadDayHisClctDO::getDate, endDate)
                .ge(startDate != null, IndustryCityLoadDayHisClctDO::getDate, startDate)
        );
    }

    @Override
    public List<IndustryCityLoadDayHisClctDO> findIndustryCityLoadDayHisClctDOS(String cityId, List<String> tradeCodes, List<Date> dates) {
        return industryCityLoadDayHisClctDAO.findAll(JpaWrappers.<IndustryCityLoadDayHisClctDO>lambdaQuery()
                .in(!CollectionUtils.isEmpty(tradeCodes), IndustryCityLoadDayHisClctDO::getTradeCode, tradeCodes)
                .in(!CollectionUtils.isEmpty(dates), IndustryCityLoadDayHisClctDO::getDate, dates)
                .eq(!StringUtils.isEmpty(cityId), IndustryCityLoadDayHisClctDO::getCityId, cityId)
        );
    }

    @Override
    public List<IndustryCityLoadDayHisClctDO> findIndustryCityLoadDayHisClctDOS(String cityId, String tradeCodes, Date startDate, Date endDate) {
        return industryCityLoadDayHisClctDAO.findAll(JpaWrappers.<IndustryCityLoadDayHisClctDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(tradeCodes), IndustryCityLoadDayHisClctDO::getTradeCode, tradeCodes)
                .ge(startDate != null, IndustryCityLoadDayHisClctDO::getDate, startDate)
                .eq(!StringUtils.isEmpty(cityId), IndustryCityLoadDayHisClctDO::getCityId, cityId)
                .le(endDate != null, IndustryCityLoadDayHisClctDO::getDate, endDate)
        );
    }

    @Override
    public void doUpdateIndustryLoadCityHisDO(IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO) throws Exception {
        List<IndustryCityLoadDayHisClctDO> all = industryCityLoadDayHisClctDAO.findAll(JpaWrappers.<IndustryCityLoadDayHisClctDO>lambdaQuery()
                .eq(IndustryCityLoadDayHisClctDO::getTradeCode, industryCityLoadDayHisClctDO.getTradeCode())
                .eq(IndustryCityLoadDayHisClctDO::getDate, industryCityLoadDayHisClctDO.getDate())
                .eq(IndustryCityLoadDayHisClctDO::getCityId, industryCityLoadDayHisClctDO.getCityId()));
        if (CollectionUtils.isEmpty(all)) {
            industryCityLoadDayHisClctDAO.save(industryCityLoadDayHisClctDO);
        } else {
            String id = all.get(0).getId();
            industryCityLoadDayHisClctDO.setId(id);
            industryCityLoadDayHisClctDAO.saveOrUpdateByTemplate(industryCityLoadDayHisClctDO);
        }
    }
}
