
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.ColumnUtil;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.SettingAssessEnum;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingCompositeAccuracyDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseStatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.*;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.BaseLoadIntervalDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.AccessStatisResultDTO;
import com.tsintergy.lf.serviceimpl.assess.dao.SettingAssessDAO;
import com.tsintergy.lf.serviceimpl.assess.dao.SettingCompositeAccuracyDAO;
import com.tsintergy.lf.serviceimpl.enums.AssessEnum;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyAssessDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyCompositeDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcBatchDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcDAO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsintergy.lf.serviceimpl.evalucation.impl.BatchDataFilterServiceImpl.batchMap;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("accuracyAssessService")
public class AccuracyAssessServiceImpl implements AccuracyAssessService {


    @Resource
    private AccuracyAssessDAO accuracyAssessDAO;

//    @Resource
//    private LoadCityFcBatchService loadCityFcBatchService;

    @Resource
    private LoadCityFcService loadCityFcService;

    @Resource
    private SettingAssessService settingAssessService;

    @Resource
    private SettingAssessDAO settingAssessDAO;

    @Resource
    private AccuracyCompositeDAO accuracyCompositeDAO;

    @Resource
    private SettingCompositeAccuracyDAO settingCompositeAccuracyDAO;

    @Resource
    private StatisticsCityDayFcDAO statisticsCityDayFcDAO;

    @Resource
    private LoadCityHisService loadCityHisByMinuteService;

    @Resource
    private SettingBatchInitService settingBatchInitService;

    @Resource
    private LoadCityFcBatchService loadCityFcBatchService;

    @Resource
    private BatchDataFilterServiceImpl batchDataFilterService;

    @Resource
    private StatisticsCityDayFcBatchDAO statisticsCityDayFcBatchDAO;

    @Resource
    private AccuracyAssessService accuracyAssessService;

    private final BigDecimal HUNDRED = new BigDecimal(100);

    @Getter
    enum AccuracyRange {
        //区间范围；
        RANGE1("98", "100"),
        RANGE2("95", "98"),
        RANGE3("90", "95"),
        RANGE4("80", "90"),
        RANGE5("0", "80");

        private String max;

        private String min;

        AccuracyRange(String min, String max) {
            this.min = min;
            this.max = max;
        }

    }

    @Override
    public void calculateAssessAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
                                        Date endDate) throws Exception {
        List<AccuracyAssessDO> toSaveList = new ArrayList<>();
        Map<String, List<SettingAssessDO>> assessSettingByData = settingAssessService
                .findAssessSettingByData(startDate, endDate, caliberId);
        if (assessSettingByData == null) {
            return;
        }
        //极值统一从自定义间隔负荷表里读取后计算；
        List hisLoadSecond = loadCityHisByMinuteService
                .selectListData(cityId, caliberId, startDate, endDate);
//        List hisLoadSecond = findOtherIntervalData(cityId, caliberId, startDate,
//            endDate);
        List<LoadCityFcBatchDO> fcList = loadCityFcBatchService
                .findByConditionByBatchId(cityId, startDate, endDate, caliberId, algorithmId, null);

        Map<String, List<LoadCityFcBatchDO>> collectFc = fcList.stream().collect(
                Collectors.groupingBy(src -> src.getCityId() + Constants.SEPARATOR_PUNCTUATION + src.getCaliberId()
                        + Constants.SEPARATOR_PUNCTUATION + DateUtil.getDateToStr(src.getDate())));

        //96点数据预测准确率
        List<StatisticsCityDayFcBatchDO> statBatchList = statisticsCityDayFcBatchDAO
                .getStatisticsCityDayFcBatchDOs(Arrays.asList(cityId), caliberId,
                        algorithmId, startDate, endDate, null);

        hisLoadSecond.forEach(src -> {
            if (src instanceof BaseLoadIntervalDO) {
                String monthDate = DateUtil.getMonthByDate(((BaseLoadIntervalDO) src).getDate());
                List<LoadCityFcBatchDO> fcData = collectFc
                        .get(((BaseLoadIntervalDO) src).getCityId() + Constants.SEPARATOR_PUNCTUATION
                                + ((BaseLoadIntervalDO) src).getCaliberId()
                                + Constants.SEPARATOR_PUNCTUATION + DateUtil
                                .getDateToStr(((BaseLoadIntervalDO) src).getDate()));
                if (!CollectionUtils.isEmpty(fcData)) {
                    fcData.forEach(oneFc -> {
                        Optional<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchOptional = statBatchList.stream().filter(
                                x -> x.getBatchId().equals(oneFc.getBatchId()) && x.getDate().equals(oneFc.getDate())
                                        && x.getCityId().equals(oneFc.getCityId()) && x.getCaliberId().equals(oneFc.getCaliberId())
                                        && x.getAlgorithmId().equals(oneFc.getAlgorithmId())
                        ).findFirst();
                        BaseStatisticsCityDayFcDO statisticsCityDayFcBatchDO = new BaseStatisticsCityDayFcDO();
                        if (statisticsCityDayFcBatchOptional.isPresent()) {
                            BeanUtils.copyProperties(statisticsCityDayFcBatchOptional.get(), statisticsCityDayFcBatchDO);
                        }
                        //统计准确率
                        List<AccuracyAssessDO> statistics = statistics(((BaseLoadIntervalDO) src), oneFc,
                                assessSettingByData.get(monthDate), statisticsCityDayFcBatchDO);
                        toSaveList.addAll(statistics);
                    });
                }
            }
        });
        accuracyAssessService.doSaveOrUpdateBatch(toSaveList);
    }

    @Override
    public List<AccuracyAssessDO> findAccuracyList(String cityId, String caliberId, String algorithmId, Date startDate,
                                                   Date endDate, String batchId) {
        return accuracyAssessDAO.selectList(cityId, caliberId, algorithmId, startDate, endDate, batchId);
    }

    @Override
    public List<AccuracyAssessDTO> findAccuracyDTO(String cityId, String caliberId, String algorithmId,
                                                   Date startDate, Date endDate, String batchId, Integer day) throws Exception {
        //综合准确率&考核点准确率合并对象；
        List<AccuracyAssessDTO> mixList = new ArrayList<>();

        List<AccuracyAssessDO> accuracyAssessDOS = this.accuracyAssessDAO.selectList(cityId, caliberId, algorithmId, startDate, endDate, null);
        //按日前n天+最新的一条 清洗数据
        List<AccuracyAssessDO> assessDOS = batchDataFilterService.filterAssessByBatchId(accuracyAssessDOS, cityId, batchId, day);

        List<SettingAssessDO> settingAssessDOS = settingAssessDAO
                .selectListByStartEndValid(startDate, endDate, caliberId, true);
        List<SettingAssessDataDTO> assessDataList = settingAssessService
                .findAssessDataList(DateUtil.getYearByDate(endDate), caliberId);
        Map<String, SettingAssessDO> settingAssessDOMap = settingAssessDOS.stream()
                .collect(Collectors.toMap(SettingAssessDO::getAssessName, Function
                        .identity(), (key1, key2) -> key2));
        List<SettingCompositeAccuracyDO> accuracyDOS = settingCompositeAccuracyDAO
                .selectListByStartEndValid(startDate, endDate, caliberId);
        Map<String, SettingCompositeAccuracyDO> settingCompositeMap = accuracyDOS.stream()
                .collect(Collectors.toMap(SettingCompositeAccuracyDO::getAccuracyName, Function
                        .identity(), (key1, key2) -> key2));
        List<String> nameList = assessDOS.stream().map(AccuracyAssessDO::getAssessName)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assessDOS)) {
            return Collections.emptyList();
        }

        List<AccuracyCompositeDO> accuracyCompositeDOS = this.accuracyCompositeDAO.selectListByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate, null);
        //按批次+日前n天清洗数据
        List<AccuracyCompositeDO> compositeDOS = batchDataFilterService.filterCompositeByBatchId(accuracyCompositeDOS, cityId, batchId, day);

        Map<Date, List<AccuracyAssessDO>> assessMap = assessDOS.stream()
                .collect(Collectors.groupingBy(AccuracyAssessDO::getDate));
        Map<Date, List<AccuracyCompositeDO>> compositeMap = compositeDOS.stream()
                .collect(Collectors.groupingBy(AccuracyCompositeDO::getDate));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<Integer> collect = assessDOS.stream().map(AccuracyAssessDO::getAssessType)
                .collect(Collectors.toList());
        Map<Date, BaseLoadIntervalDO> collectHis = new HashedMap<>();
        Map<Date, LoadCityFcBatchDO> collectFc = new HashedMap<>();
        //如果考核点结果里包含最大or最小负荷的，需要额外查询预测or实际负荷特性数据；按照考核点时间判定最大最小，因此从fc和his表里获取；
        if (collect.contains(AssessEnum.MAX.getType()) || collect.contains(AssessEnum.MIN.getType())) {
            List<BaseLoadIntervalDO> hisLoadSecond = loadCityHisByMinuteService
                    .selectListData(cityId, caliberId, startDate, endDate);
//            List<LoadCityHisDO> hisList = loadCityHisService
//                .getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
//            List<LoadCityFcDO> fcList = loadCityFcService
//                .findFcByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate);
            //需求调整；展示各个批次的数据
//            List<LoadCityFcBatchDO> fcList = loadCityFcBatchService
//                .findByConditionByBatchId(cityId, startDate, endDate, caliberId, algorithmId, Integer.valueOf(batchId));
            //需求调整；预测批次数据也需要按照批次+日前n天对应筛选；否则和计算考核点&综合准确率使用的预测数据对不上；
            List<LoadCityFcBatchDO> fcListsrc = selectListInBatchForecastTime(cityId, caliberId, algorithmId, startDate, endDate, batchId);
            //按批次+日前n天清洗数据
            List<LoadCityFcBatchDO> fcList = filterForecastByBatch(fcListsrc, day);
            collectHis = hisLoadSecond.stream()
                    .collect(Collectors.toMap(BaseLoadIntervalDO::getDate, Function
                            .identity(), (key1, key2) -> key2));
            collectFc = fcList.stream()
                    .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function
                            .identity(), (key1, key2) -> key2));
        }
        Map<Date, BaseLoadIntervalDO> finalCollectHis = collectHis;
        Map<Date, LoadCityFcBatchDO> finalCollectFc = collectFc;
        dateList.forEach(date -> {
            AccuracyAssessDTO mix = new AccuracyAssessDTO();
            mix.setDate(date);
            List<AccuracyAssessDO> assessDOList = assessMap.get(date);
            if (!CollectionUtils.isEmpty(assessDOList)) {
                List<AccuracyAssessUnitDTO> assessUnitList = new ArrayList<>();
                Map<String, AccuracyAssessDO> accuracyAssessMap = assessDOList.stream().collect(
                    Collectors.toMap(AccuracyAssessDO::getAssessName, Function.identity(), (key1, key2) -> key2));
                for (SettingAssessDataDTO assessData : assessDataList) {
                    if (StrUtil.isEmpty(assessData.getAssessName()) || !accuracyAssessMap.containsKey(assessData.getAssessName())){
                        continue;
                    }
                    AccuracyAssessDO assessDO = accuracyAssessMap.get(assessData.getAssessName());
                    SettingAssessDO settingAssessDO = null;
                    if (assessDO != null) {
                        settingAssessDO = settingAssessDOMap.get(assessDO.getAssessName());
                    }
                    //只装载可用状态的考核点数据
                    if (settingAssessDO != null) {
                        AccuracyAssessUnitDTO assessUnit = new AccuracyAssessUnitDTO();
                        assessUnit.setAssessName(assessDO.getAssessName());
                        assessUnit.setAssessAccuracy(assessDO.getAccuracy());
                        assessUnit.setType(assessDO.getAssessType());
                        assessUnit.setDate(settingAssessDO.getCreatetime());
                        //如果考核点配置的是最大or最小负荷，需要额外装载目标日的预测日最大（or最小）负荷以及实际日最大（or最小）负荷
                        // 时刻点类型，按照最大值取值
                        if (AssessEnum.MAX.getType().equals(assessDO.getAssessType()) ||
                                AssessEnum.DATE_POINT.getType().equals(assessDO.getAssessType())) {
                            BaseLoadIntervalDO dayHisDO = finalCollectHis.get(date);
                            if (dayHisDO != null) {
                                BigDecimal max = getMaxOrMinFromTimeQuantumByData(dayHisDO,
                                        settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MAX);
                                assessUnit.setDailyExtremumHis(max.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                            LoadCityFcBatchDO dayFcDO = finalCollectFc.get(date);
                            if (dayFcDO != null) {
                                BigDecimal max = getMaxOrMinFromTimeQuantum(dayFcDO.getloadList(),
                                        settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MAX);
                                assessUnit.setDailyExtremumFc(max.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                        }
                        if (AssessEnum.MIN.getType().equals(assessDO.getAssessType())) {
                            BaseLoadIntervalDO dayHisDO = finalCollectHis.get(date);
                            if (dayHisDO != null) {
                                BigDecimal min = getMaxOrMinFromTimeQuantumByData(dayHisDO,
                                        settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MIN);
                                assessUnit.setDailyExtremumHis(min.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                            LoadCityFcBatchDO dayFcDO = finalCollectFc.get(date);
                            if (dayFcDO != null) {
                                BigDecimal min = getMaxOrMinFromTimeQuantum(dayFcDO.getloadList(),
                                        settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MIN);
                                assessUnit.setDailyExtremumFc(min.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                        }
                        //添加偏差；偏差=实际-预测
                        assessUnit.setDeviation(
                                BigDecimalUtils.sub(assessUnit.getDailyExtremumHis(), assessUnit.getDailyExtremumFc()));
                        assessUnitList.add(assessUnit);
                    }
                }
                mix.setAssessUnitList(assessUnitList);
            }
            //获取目标日的综合准确率数据
            List<AccuracyCompositeDO> compositeDOList = compositeMap.get(date);
            if (!CollectionUtils.isEmpty(compositeDOList)) {
                compositeDOList.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime));
                List<CompositeAccuracyUnitDTO> unitList = new ArrayList<>();
                compositeDOList.forEach(one -> {
                    //只装载可用状态的考核点数据
                    SettingCompositeAccuracyDO settingCompositeAccuracyDO = settingCompositeMap
                        .get(one.getAccuracyName());
                    if (settingCompositeAccuracyDO != null) {
                        CompositeAccuracyUnitDTO dto = new CompositeAccuracyUnitDTO();
                        BeanUtils.copyProperties(one, dto);
                        dto.setCreatetime(settingCompositeAccuracyDO.getCreatetime());
                        //把综合准确率名称放入名称筛选列表
                        nameList.add(one.getAccuracyName());
                        unitList.add(dto);
                    }
                });
                unitList.sort(Comparator.comparing(CompositeAccuracyUnitDTO::getCreatetime));
                mix.setCompositeAccuracyList(unitList);
            }
            mixList.add(mix);
        });
        return mixList;
    }

    public List<LoadCityFcBatchDO> selectListInBatchForecastTime(String cityId, String caliberId, String algorithmId,
                                                                 Date startDate,
                                                                 Date endDate, String batchId) throws Exception {
        List<LoadCityFcBatchDO> resultList = new ArrayList<>();
        //逻辑调整；传入的批次id对应批次设置的时间范围，查询时间范围内最新的一条数据；（数据表的批次id和此处的batchId不是一个概念；）
        List<LoadCityFcBatchDO> fcList = loadCityFcBatchService
                .findByConditionByBatchId(cityId, startDate, endDate, caliberId, algorithmId, null);
        SettingBatchInitDO batchById = settingBatchInitService.getBatchByCondition(batchMap.get(batchId), cityId);
        for (LoadCityFcBatchDO one : fcList) {
            if (com.tsintergy.lf.core.util.DateUtil
                    .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        return resultList;
    }

    /**
     * 根据批次+日前n天过滤综合准确率
     *
     * @param assessDOS 综合准确率列表
     * @param day       日前n天
     */
    private List<AccuracyCompositeDO> filterCompositeByBatch(List<AccuracyCompositeDO> assessDOS, Integer day) {
        List<AccuracyCompositeDO> resultList = new ArrayList<>();
        List<AccuracyCompositeDO> collect = assessDOS.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                            .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
        Map<String, List<AccuracyCompositeDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getAccuracyName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                        + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                        .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
        );
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return collect;
    }

    /**
     * 根据批次+日前n天过滤批次预测数据
     *
     * @param assessDOS 批次预测列表
     * @param day       日前n天
     */
    private List<LoadCityFcBatchDO> filterForecastByBatch(List<LoadCityFcBatchDO> assessDOS, Integer day) {
        List<LoadCityFcBatchDO> resultList = new ArrayList<>();
        List<LoadCityFcBatchDO> collect = assessDOS.stream()
                .filter(src -> {
                    Date anObject = DateUtils.addDays(src.getDate(), (-day));
                    return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                            .equals(DateUtil.getDateToStr(anObject));
                }).collect(Collectors.toList());
        Map<String, List<LoadCityFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
                src -> src.getDate()
                        + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                        .getCityId() + Constants.SEPARATOR_BROKEN_LINE, TreeMap::new, Collectors.toList())
        );
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return collect;
    }

    @Override
    public List<String> findNameList(String cityId, String caliberId, String algorithmId,
                                     Date startDate, Date endDate) {
        //查询可用状态的考核点名称
        List<SettingAssessDO> settingAssessDOS = settingAssessDAO
                .selectListByStartEndValid(startDate, endDate, caliberId, true);
        settingAssessDOS.sort(Comparator.comparing(src -> src.getSort() == null ? 1 : src.getSort()));
        List<String> assessNameList = settingAssessDOS.stream().map(SettingAssessDO::getAssessName)
                .collect(Collectors.toList());
        //查询可用状态的国调考核名称
        List<SettingCompositeAccuracyDO> accuracyDOS = settingCompositeAccuracyDAO
                .selectListByStartEndValid(startDate, endDate, caliberId);
        List<String> compositeNameList = accuracyDOS.stream().map(SettingCompositeAccuracyDO::getAccuracyName)
                .collect(Collectors.toList());
        compositeNameList.addAll(assessNameList);
        return compositeNameList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<AssessSettingDTO> findAssessList(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate) {
        //查询可用状态的考核点配置
        List<SettingAssessDO> settingAssessDOS = settingAssessDAO
                .selectListByStartEndValid(startDate, endDate, caliberId, true);
        settingAssessDOS.sort(Comparator.comparing(src -> src.getSort() == null ? 1 : src.getSort()));
        List<AssessSettingDTO> assessSettingDTOS = settingAssessDOS.stream().map(
                assess -> {
                    AssessSettingDTO assessSettingDTO = new AssessSettingDTO();
                    assessSettingDTO.setId(assess.getId());
                    assessSettingDTO.setName(assess.getAssessName());
                    assessSettingDTO.setStartTime(assess.getStartTime());
                    assessSettingDTO.setEndTime(assess.getEndTime());
                    return assessSettingDTO;
                }
        ).collect(Collectors.toList());
        return assessSettingDTOS.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<AccuracyDistributionDTO> findAccuracyDistribution(String cityId, String caliberId, String algorithmId,
                                                                  String accuracyName, Date startDate, Date endDate, String batchIds, Integer day) throws Exception {
        List<AccuracyAssessDTO> assessDTOS = this
                .findAccuracyDTO(cityId, caliberId, algorithmId, startDate, endDate, batchIds, day);
        if (CollectionUtils.isEmpty(assessDTOS)) {
            return null;
        }
        List<AccuracyAssessUnitDTO> assessUnitList = new ArrayList<>();
        assessDTOS.forEach(assess -> {
            List<AccuracyAssessUnitDTO> list = assess.getAssessUnitList();
            if (list != null) {
                assessUnitList.addAll(list);
            }
        });

        List<AccuracyDistributionDTO> accuracyDistributionDTOS = new ArrayList<>();
        //过滤后的准确率集合
        List<BigDecimal> collect = null;
        List<AccuracyAssessUnitDTO> assessDTOList;
        //名称筛选考核点准确率列表
        assessDTOList = assessUnitList.stream().filter(t -> t.getAssessName().equals(accuracyName))
                .collect(Collectors.toList());
        //考核点准确率列表没有时，进一步筛选综合准确率列表；
        if (CollectionUtils.isEmpty(assessDTOList)) {
            List<CompositeAccuracyUnitDTO> lists = new ArrayList<>();
            assessDTOS.forEach(one -> {
                List<CompositeAccuracyUnitDTO> compositeAccuracyList = one.getCompositeAccuracyList();
                if (!CollectionUtils.isEmpty(compositeAccuracyList)) {
                    lists.addAll(compositeAccuracyList);
                }
            });
            List<CompositeAccuracyUnitDTO> unitDTOS = lists.stream()
                    .filter(t -> t.getAccuracyName().equals(accuracyName))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unitDTOS)) {
                return null;
            } else {
                collect = unitDTOS.stream().map(CompositeAccuracyUnitDTO::getAccuracy)
                        .collect(Collectors.toList());
            }
        } else {
            collect = assessDTOList.stream().map(AccuracyAssessUnitDTO::getAssessAccuracy)
                    .collect(Collectors.toList());
        }

        List<AccuracyDistributionDTO> accuracyDistributionDTOList = getAccuracyDistributionDTOList(collect);
        accuracyDistributionDTOS.addAll(accuracyDistributionDTOList);
        return accuracyDistributionDTOS.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 统计一天预测结果情况
     */
    public <T extends BaseLoadIntervalDO> List<AccuracyAssessDO> statistics(T intervalDO, BaseLoadFcCityDO fc,
                                                                            List<SettingAssessDO> settingAssessList, BaseStatisticsCityDayFcDO statisticsCityDayFcDO) {
        if (CollectionUtils.isEmpty(settingAssessList)) {
            return null;
        }
        List<AccuracyAssessDO> resultList = new ArrayList<>();
        settingAssessList.forEach(src -> {
            AccuracyAssessDO result = new AccuracyAssessDO();
            BeanUtils.copyProperties(fc, result, "id");
            result.setAssessName(src.getAssessName());
            result.setAssessType(src.getType());
            result.setCreatetime(fc.getCreatetime());
            try {
                BigDecimal accuracy = statisticsAccuracy(src, fc, intervalDO, statisticsCityDayFcDO);
                if (ObjectUtil.isNotNull(accuracy) && accuracy.compareTo(BigDecimal.ZERO) < 0){
                    accuracy = new BigDecimal(0);
                }
                result.setAccuracy(accuracy);
            } catch (Exception e) {
                e.printStackTrace();
            }
            resultList.add(result);
        });
        return resultList;
    }

    /**
     * 获取 时间段内的最大值 or最小值
     *
     * @param dataList 数据列表
     * @param startTime 开始时间 00:15
     * @param endTime 结束时间 00:15
     * @return >=开始时间  <=结束时间范围的极值；
     */
    private BigDecimal getMaxOrMinFromTimeQuantum(List<BigDecimal> dataList, String startTime, String endTime,
        AssessEnum type) {
        List<String> columns = ColumnUtil.getColumns(96,
            Constants.LOAD_CURVE_START_WITH_ZERO, false);
        startTime = startTime.replace(":", "");
        endTime = endTime.replace(":", "");
        List<BigDecimal> decimalsHis = dataList.subList(columns.indexOf(startTime), columns.indexOf(endTime) + 1);
        if (AssessEnum.MAX.equals(type)) {
            return decimalsHis.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
        } else {
            return decimalsHis.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
        }
    }

    private BigDecimal getMaxOrMinFromTimeQuantumByData(BaseLoadIntervalDO data, String startTime,
        String endTime,
        AssessEnum type) {
        List<BigDecimal> dataList = data.getHisLoadList();
        List<String> allTimeListBySecond =data.getAllTimeList();
        List<BigDecimal> decimalsHis = dataList.subList(allTimeListBySecond.indexOf(startTime + ":00"),
            allTimeListBySecond.indexOf(endTime + ":00") + 1);
        if (AssessEnum.MAX.equals(type)) {
            return decimalsHis.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
        } else {
            return decimalsHis.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
        }
    }

    private <T extends BaseLoadIntervalDO> BigDecimal statisticsAccuracy(SettingAssessDO assess,
                                                                         BaseLoadFcCityDO fcBatchDO, T intervalDO,
                                                                         BaseStatisticsCityDayFcDO statisticsCityDayFcDO) throws Exception {
        List<BigDecimal> fcLoad = BasePeriodUtils.toList(fcBatchDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        ;
        List<String> columns = ColumnUtil.getColumns(96,
                Constants.LOAD_CURVE_START_WITH_ZERO, false);
        List<String> allTimeListBySecond = intervalDO.getAllTimeList();
        Integer assessType = assess.getType();
        String startTime = assess.getStartTime().replace(":", "");
        String endTime = assess.getEndTime().replace(":", "");
        List<BigDecimal> hisLoadList = intervalDO.getHisLoadList();
        List<BigDecimal> decimalsHis = null;
        List<BigDecimal> decimalsFc = null;
        try {
            if ("2400".equals(endTime)) {
                decimalsHis = hisLoadList
                        .subList(allTimeListBySecond.indexOf(assess.getStartTime() + ":00"),
                                hisLoadList.size());
                decimalsFc = fcLoad.subList(columns.indexOf(startTime), fcLoad.size());
            } else {
                decimalsHis = hisLoadList
                        .subList(allTimeListBySecond.indexOf(assess.getStartTime() + ":00"),
                                allTimeListBySecond.indexOf(assess.getEndTime() + ":00") + 1);
                decimalsFc = fcLoad.subList(columns.indexOf(startTime), columns.indexOf(endTime) + 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //最大值准确率
        if (AssessEnum.MAX.getType().equals(assessType)) {
            BigDecimal maxHis;
            maxHis = decimalsHis.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                    .orElse(new BigDecimal(0));
            BigDecimal maxFc = decimalsFc.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                    .orElse(new BigDecimal(0));
            return getDailyLoadAccuracy(maxHis, maxFc);
        }//最小值准确率
        else if (AssessEnum.MIN.getType().equals(assessType)) {
            BigDecimal minHis;
            minHis = decimalsHis.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                    .orElse(new BigDecimal(0));
            BigDecimal minFc = decimalsFc.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                    .orElse(new BigDecimal(0));
            return getDailyLoadAccuracy(minHis, minFc);
        } //平均值准确率
        else if (AssessEnum.AVG.getType().equals(assessType)) {
            //20240624客户需求：考核点设置为全天时段，且考核类型【平均值】时，从批次准确率计算好的准确率获取（根本区别为该计算方式基于96点负荷）
            if (decimalsFc.size() == 96) {
                return statisticsCityDayFcDO.getAccuracy();
            } else {
                List<BigDecimal> dataList = getLoadAccuracyBySecondHisAndFc96(intervalDO,
                        fcLoad, startTime, endTime);
                //每个点的准确率求平均；
                return dataList.stream().map(f -> f == null ? BigDecimal.ZERO : f)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(dataList.size()), 4, BigDecimal.ROUND_HALF_UP);
            }

        }
        return null;
    }

    /**
     * 根据逐5s历史数据和96点预测数据计算平均准确率；会根据15分钟时刻筛选出5s数据中的对应时刻
     */
    public List<BigDecimal> getLoadAccuracyBySecondHisAndFc96(BaseLoadIntervalDO intervalDO,
        List<BigDecimal> fcListSrc, String startTime, String endTime) {
        String str = intervalDO.getAllTimeList().get(0);
        boolean secondFlag = true;
        if (str.length() < 6) {
            secondFlag = false;
        }
        List<String> allTimeListBySecond = intervalDO.getAllTimeList();
        List<String> allTimeListByMinSrc = ColumnUtil.getColumns(96,
            Constants.LOAD_CURVE_START_WITH_ZERO, false);
        List<String> allTimeListByMin = allTimeListByMinSrc
            .subList(allTimeListByMinSrc.indexOf(startTime), allTimeListByMinSrc.indexOf(endTime) + 1);
        List<BigDecimal> fcList = fcListSrc
            .subList(allTimeListByMinSrc.indexOf(startTime), allTimeListByMinSrc.indexOf(endTime) + 1);
        Map<String, BigDecimal> hisMapBySecond = new HashedMap<>();
        Map<String, BigDecimal> fc96 = new HashedMap<>();
        List<BigDecimal> hisListByInterval = intervalDO.getHisLoadList();
        for (int i = 0; i < allTimeListBySecond.size(); i++) {
            hisMapBySecond.put(allTimeListBySecond.get(i), hisListByInterval.get(i));
        }
        for (int i = 0; i < allTimeListByMin.size(); i++) {
            fc96.put(allTimeListByMin.get(i), fcList.get(i));
        }
        Map<String, BigDecimal> result = new HashedMap<>();
        for (Map.Entry<String, BigDecimal> entry : fc96.entrySet()) {
            String key =
                entry.getKey().substring(0, 2) + ":" + entry.getKey().substring(2, 4) + (secondFlag ? ":00" : "");
            BigDecimal bigDecimaBySecond = hisMapBySecond.get(key);
            if (bigDecimaBySecond != null) {
                result.put(entry.getKey(), getDailyLoadAccuracy(bigDecimaBySecond, entry.getValue()));
            }
        }
        List<BigDecimal> list = new ArrayList<>(result.values());
        return list;
    }


    /**
     * 判断极值点时间是否属于某个时间段；
     *
     * @param startTime 开始时间 00:15
     * @param endTime 结束时间 00:15
     * @param extremumTime 极值点出现时间；可能不是整点时刻，例如：01:16:05  注意：我方计算数据精度到分钟，采集的特性数据到秒；这里为了兼容旧版本，判断一下时分秒长度，是hh:mm还是hh:mm:ss
     */
    private boolean checkExtremumTimeEncircle(String startTime, String endTime, String extremumTime) {
        String day = DateUtil.formateDate(new Date());
        Date startDateTime = DateUtils.string2Date(day + " " + startTime + ":00", DateFormatType.DATE_FORMAT_STR);
        Date endDateTime = DateUtils.string2Date(day + " " + endTime + ":00", DateFormatType.DATE_FORMAT_STR);
        Date check;
        //hh:mm:ss
        if (extremumTime.split(":").length > 2) {
            check = DateUtils.string2Date(day + " " + extremumTime, DateFormatType.DATE_FORMAT_STR);
        } else {
            check = DateUtils.string2Date(day + " " + extremumTime + ":00", DateFormatType.DATE_FORMAT_STR);
        }
        if (check.after(startDateTime) && check.before(endDateTime)) {
            return true;
        }
        return false;
    }

    /**
     * @param his 实际
     * @param fc 预测
     */
    private BigDecimal getDailyLoadAccuracy(BigDecimal his, BigDecimal fc) {
        if (fc == null || his == null) {
            return null;
        }
        //做差
        BigDecimal subtract = his.subtract(fc);
        //求绝对值
        //相除后求绝对值
        BigDecimal divide = (subtract.divide(his, 4, BigDecimal.ROUND_HALF_UP)).abs();
        BigDecimal result = BigDecimal.ONE.subtract(divide.abs());
        return result;
    }

    private List<AccuracyDistributionDTO> getAccuracyDistributionDTOList(List<BigDecimal> list) {
        List<AccuracyDistributionDTO> accuracyDistributionDTOS = new ArrayList<>();
        for (AccuracyRange value : AccuracyRange.values()) {
            AccuracyDistributionDTO accuracyDistributionDTO;
            if (("80").equals(value.getMin())) {
                accuracyDistributionDTO = getAccuracyDistributionDTO(list,
                    new BigDecimal(value.max).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP),
                    new BigDecimal(value.min).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP), false, false);
            } else if (("80").equals(value.getMax())) {
                accuracyDistributionDTO = getAccuracyDistributionDTO(list,
                    new BigDecimal(value.max).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP),
                    new BigDecimal(value.min).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP), false, true);
            } else {
                accuracyDistributionDTO = getAccuracyDistributionDTO(list,
                    new BigDecimal(value.max).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP),
                    new BigDecimal(value.min).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP), true, false);
            }
            accuracyDistributionDTOS.add(accuracyDistributionDTO);
        }
        return accuracyDistributionDTOS;
    }

    /**
     * 获取准确率分布对象数据
     *
     * @param list 准确率集合
     * @param max 上限
     * @param min 下限
     * @param minIsEq 前闭后开
     * @param maxIsEq 前开后闭
     */
    private AccuracyDistributionDTO getAccuracyDistributionDTO(List<BigDecimal> list, BigDecimal max, BigDecimal min,
        boolean minIsEq, boolean maxIsEq) {
        AccuracyDistributionDTO accuracyDistributionDTO = new AccuracyDistributionDTO();
        int length = 0;
        StringBuffer range = new StringBuffer();
        if (minIsEq) {
            length = list.stream().filter(t -> t.compareTo(min) == 1 && t.compareTo(max) == -1 || t.compareTo(min) == 0)
                .toArray().length;
            range.append("[" + min.multiply(HUNDRED) + "%," + max.multiply(HUNDRED) + "%)");
        } else if (maxIsEq) {
            length = list.stream().filter(t -> t.compareTo(min) == 1 && t.compareTo(max) == -1 || t.compareTo(max) == 0)
                .toArray().length;
            range.append("(" + min.multiply(HUNDRED) + "%," + max.multiply(HUNDRED) + "%]");
        } else {
            length = list.stream().filter(t -> t.compareTo(min) == 1 && t.compareTo(max) == -1).toArray().length;
            range.append("(" + min.multiply(HUNDRED) + "%," + max.multiply(HUNDRED) + "%)");
        }
        accuracyDistributionDTO.setAccuracyRange(range.toString());
        accuracyDistributionDTO.setStatisticalValue(length);
        accuracyDistributionDTO.setProportion(
                new BigDecimal(length).multiply(HUNDRED).divide(new BigDecimal(list.size()), 2, BigDecimal.ROUND_HALF_UP)
                        .setScale(2) + "");
        return accuracyDistributionDTO;
    }

    @Override
    public List<AccuracyAssessDO> findAccuracyListByCondition(List<String> cityIds, String caliberId, String algorithmId, Date startDate, Date endDate) {
        return accuracyAssessDAO.findAccuracyListByCondition(cityIds, caliberId, algorithmId, startDate, endDate);
    }

    @Override
    public void doSaveOrUpdateBatch(List<AccuracyAssessDO> accuracyAssessDOS) {
        accuracyAssessDOS.forEach(accuracyAssessDO -> {
            try {
                accuracyAssessDAO.saveOrUpdateEntityByTemplate(accuracyAssessDO);
            } catch (Exception e) {
                log.error("保存考核点异常", e);
            }
        });

    }

    @Override
    public AccessStatisResultDTO getAccessLoad(List<BigDecimal> loadList, SettingAssessDO assess, List<String> columns) {
        String startTime = assess.getStartTime().replace(":", "");
        String endTime = assess.getEndTime().replace(":", "");
        Integer assessType = assess.getType();
        Integer startIndex = columns.indexOf(startTime);
        Integer endIndex = -1;
        try {
            if ("2400".equals(endTime)) {
                endIndex = loadList.size();
            } else {
                endIndex = columns.indexOf(endTime) + 1;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        AccessStatisResultDTO result = new AccessStatisResultDTO();
        if (AssessEnum.MAX.getType().equals(assessType)) {
            BigDecimal max = null;
            for (int i = startIndex; i < endIndex; i++) {
                if (max == null || (loadList.get(i) != null && loadList.get(i).compareTo(max) > 0)) {
                    max = loadList.get(i);
                    result.setResult(max);
                    result.setTime(columns.get(i));
                }
            }
        }//最小值准确率
        else if (AssessEnum.MIN.getType().equals(assessType)) {
            BigDecimal min = null;
            for (int i = startIndex; i < endIndex; i++) {
                if (min == null || (loadList.get(i) != null && loadList.get(i).compareTo(min) < 0)) {
                    min = loadList.get(i);
                    result.setResult(min);
                    result.setTime(columns.get(i));
                }
            }
        } //平均值准确率
        else if (AssessEnum.AVG.getType().equals(assessType)) {
            List<BigDecimal> decimalsHis = loadList.subList(startIndex, endIndex);
            BigDecimal load = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(decimalsHis);
            result.setResult(load);
        }
        return result;
    }

    @Override
    public List<String> findAssessNameList(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate) {
        //查询可用状态的考核点名称
        List<SettingAssessDO> settingAssessDOS = settingAssessDAO
                .selectListByStartEndValid(startDate, endDate, caliberId, true);
        settingAssessDOS.sort(Comparator.comparing(src -> src.getSort() == null ? 1 : src.getSort()));
        List<SettingAssessDO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(settingAssessDOS)) {
            for (SettingAssessDO settingAssessDO : settingAssessDOS) {
                for (SettingAssessEnum value : SettingAssessEnum.values()) {
                    if (value.getName().equals(settingAssessDO.getAssessName())) {
                        settingAssessDO.setSort(Integer.valueOf(value.getId()));
                        result.add(settingAssessDO);
                    }
                }
            }
        }
        List<SettingAssessDO> collect = result.stream().sorted(Comparator.comparing(SettingAssessDO::getSort)).collect(Collectors.toList());
        List<String> assessNameList = collect.stream().map(SettingAssessDO::getAssessName).collect(Collectors.toList());
        return assessNameList.stream().distinct().collect(Collectors.toList());
    }
}
