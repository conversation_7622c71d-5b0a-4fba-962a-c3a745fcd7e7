package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcBmBatchService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBmBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcBmBatchDAO;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Service("weatherCityFcBmBatchService")
public class WeatherCityFcBmBatchServiceImpl implements WeatherCityFcBmBatchService {

    @Autowired
    WeatherCityFcBmBatchDAO weatherCityFcBmBatchDAO;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Override
    public void doSaveOrUpdate(WeatherCityFcBmBatchDO weatherCityFcBmBatchDO) {
        List<WeatherCityFcBmBatchDO> all = weatherCityFcBmBatchDAO.findAll(
                JpaWrappers.<WeatherCityFcBmBatchDO>lambdaQuery()
                        .eq(WeatherCityFcBmBatchDO::getCityId, weatherCityFcBmBatchDO.getCityId())
                        .eq(WeatherCityFcBmBatchDO::getType, weatherCityFcBmBatchDO.getType())
                        .eq(WeatherCityFcBmBatchDO::getDate, weatherCityFcBmBatchDO.getDate())
                        .eq(WeatherCityFcBmBatchDO::getBatchId, weatherCityFcBmBatchDO.getBatchId()));
        if (CollectionUtils.isEmpty(all)) {
            weatherCityFcBmBatchDAO.save(weatherCityFcBmBatchDO);
        } else {
            weatherCityFcBmBatchDO.setId(all.get(0).getId());
            weatherCityFcBmBatchDAO.saveOrUpdateByTemplate(weatherCityFcBmBatchDO);
        }
    }

    @SneakyThrows
    @Override
    public void insertYesterday24HourData(WeatherCityFcBmBatchDO weatherCityFcBmBatchDO) {
        Date yesterday = DateUtils.addDays(weatherCityFcBmBatchDO.getDate(), -1);
        WeatherCityFcBmBatchDO yesterdayDO = weatherCityFcBmBatchDAO.findOne(
                JpaWrappers.<WeatherCityFcBmBatchDO>lambdaQuery()
                        .eq(WeatherCityFcBmBatchDO::getCityId, weatherCityFcBmBatchDO.getCityId())
                        .eq(WeatherCityFcBmBatchDO::getType, weatherCityFcBmBatchDO.getType())
                        .eq(WeatherCityFcBmBatchDO::getDate, yesterday)
                        .eq(WeatherCityFcBmBatchDO::getBatchId, weatherCityFcBmBatchDO.getBatchId()));

        if (yesterdayDO == null) {
            return;
        }
        yesterdayDO.setT2400(weatherCityFcBmBatchDO.getT0000());
        BigDecimal t2300 = yesterdayDO.getT2300();
        BigDecimal t2400 = weatherCityFcBmBatchDO.getT0000();
        if (t2400 != null) {
            BigDecimal difference = t2400.subtract(t2300);
            BigDecimal t2315 = t2300.add(difference.multiply(new BigDecimal("0.25")));
            BigDecimal t2330 = t2300.add(difference.multiply(new BigDecimal("0.5")));
            BigDecimal t2345 = t2300.add(difference.multiply(new BigDecimal("0.75")));
            yesterdayDO.setT2315(t2315);
            yesterdayDO.setT2330(t2330);
            yesterdayDO.setT2345(t2345);
        }
        List<BigDecimal> weatherList = yesterdayDO.getWeatherList();
        ColumnUtil.supplimentPoit(weatherList);
        Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(weatherList, Constants.LOAD_CURVE_START_WITH_ZERO);
        BasePeriodUtils.setAllFiled(yesterdayDO, decimalMap);
        weatherCityFcBmBatchDAO.saveOrUpdateByTemplate(yesterdayDO);
    }

    @Override
    public List<WeatherCityFcBmBatchDO> findBatchWeatherFcByHour(String cityId, Date date, Integer type, String hour) {
        // 1. 获取前一天日期
        String dateStr = com.tsieframework.core.base.format.datetime.DateUtils.date2String(
                com.tsieframework.core.base.format.datetime.DateUtils.addDays(date, -1),
                DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR
        );

        // 2. 构造目标 orderId 前缀
        String targetOrderIdPrefix = dateStr + hour;

        // 3. 查询数据
        List<WeatherCityFcBmBatchDO> weatherCityFcDOList;
        if (Constants.PROVINCE_ID.equals(cityId)) {
            // 如果 cityId 为 1，查询所有城市的数据
            weatherCityFcDOList = this.findWeatherCityFcDO(null, type, date, null); // 查询所有 type
        } else {
            // 否则，查询指定城市的数据
            weatherCityFcDOList = this.findWeatherCityFcDO(cityId, type, date, null); // 查询所有 type
        }

        // 4. 按 cityId 和 type 分组
        Map<String, Map<Integer, List<WeatherCityFcBmBatchDO>>> cityAndTypeGroupMap = weatherCityFcDOList.stream()
                .collect(Collectors.groupingBy(
                        WeatherCityFcBmBatchDO::getCityId,
                        Collectors.groupingBy(WeatherCityFcBmBatchDO::getType)
                ));

        // 5. 过滤每个城市和 type 的数据
        List<WeatherCityFcBmBatchDO> result = new ArrayList<>();
        for (Map.Entry<String, Map<Integer, List<WeatherCityFcBmBatchDO>>> cityEntry : cityAndTypeGroupMap.entrySet()) {
            Map<Integer, List<WeatherCityFcBmBatchDO>> typeGroupMap = cityEntry.getValue();

            for (Map.Entry<Integer, List<WeatherCityFcBmBatchDO>> typeEntry : typeGroupMap.entrySet()) {
                List<WeatherCityFcBmBatchDO> cityDataList = typeEntry.getValue();

                // 过滤逻辑：筛选出 orderId 小于 targetOrderIdPrefix 的记录，并按 orderId 降序排序取第一条
                WeatherCityFcBmBatchDO latestRecord = cityDataList.stream()
                        .filter(data -> {
                            String orderId = data.getBatchId();
                            return orderId.compareTo(targetOrderIdPrefix) <= 0;
                        })
                        .max(Comparator.comparing(WeatherCityFcBmBatchDO::getBatchId))
                        .orElse(null);

                if (latestRecord != null) {
                    result.add(latestRecord);
                }
            }
        }

        // 6. 如果是省份 ID，统计全省数据
        if (Constants.PROVINCE_ID.equals(cityId)) {
            List<WeatherCityFcDO> weatherCityFcDOS = weatherCityFcService.statProvinceWeather(result);
            result = weatherCityFcDOS.stream().map(src -> {
                WeatherCityFcBmBatchDO cityFcMeteoBatchDO = new WeatherCityFcBmBatchDO();
                BeanUtils.copyProperties(src, cityFcMeteoBatchDO);
                return cityFcMeteoBatchDO;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<WeatherCityFcBmBatchDO> findBatchWeatherFcByHourDate(String cityId, Date startDate, Date endDate, Integer type, String hour) {
        List<WeatherCityFcBmBatchDO> weatherCityFcDOList;
        if (Constants.PROVINCE_ID.equals(cityId)) {
            weatherCityFcDOList = this.findWeatherCityFcDOSDate(Arrays.asList("2","4","7","9","10","12"), type, startDate, endDate, null); // 查询所有 type
        } else {
            weatherCityFcDOList = this.findWeatherCityFcDODate(cityId, type, startDate, endDate,null); // 查询所有 type
        }
        Map<String, List<WeatherCityFcBmBatchDO>> cityAndTypeGroupMap = weatherCityFcDOList.stream().collect(Collectors.groupingBy(t -> t.getCityId() + "-" + t.getType() + "-" + com.tsieframework.core.base.format.datetime.DateUtils.date2String(t.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)));
        List<WeatherCityFcBmBatchDO> result = new ArrayList<>();
        for (Map.Entry<String, List<WeatherCityFcBmBatchDO>> stringListEntry : cityAndTypeGroupMap.entrySet()) {
            List<WeatherCityFcBmBatchDO> value = stringListEntry.getValue();
            if (!CollectionUtils.isEmpty(value)) {
                for (Map.Entry<java.sql.Date, List<WeatherCityFcBmBatchDO>> dateListEntry : value.stream().collect(Collectors.groupingBy(t -> t.getDate())).entrySet()) {
                    java.sql.Date key = dateListEntry.getKey();
                    String yesDate = com.tsieframework.core.base.format.datetime.DateUtils.date2String(com.tsieframework.core.base.format.datetime.DateUtils.addDays(key, -1), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                    String keyOrder = yesDate + hour;
                    List<WeatherCityFcBmBatchDO> value1 = dateListEntry.getValue();
                    WeatherCityFcBmBatchDO latestRecord = value1.stream()
                            .filter(data -> {
                                String orderId = data.getBatchId();
                                return orderId.compareTo(keyOrder) < 0;
                            })
                            .max(Comparator.comparing(WeatherCityFcBmBatchDO::getBatchId))
                            .orElse(null);
                    if (latestRecord != null) {
                        result.add(latestRecord);
                    }
                }

            }
        }
        if (Constants.PROVINCE_ID.equals(cityId)) {
            List<WeatherCityFcDO> weatherCityFcDOS = weatherCityFcService.statProvinceWeather(result);
            result = weatherCityFcDOS.stream().map(src -> {
                WeatherCityFcBmBatchDO cityFcMeteoBatchDO = new WeatherCityFcBmBatchDO();
                BeanUtils.copyProperties(src, cityFcMeteoBatchDO);
                return cityFcMeteoBatchDO;
            }).collect(Collectors.toList());
        }
        return result;
    }

    public List<WeatherCityFcBmBatchDO> findWeatherCityFcDO(String cityId, Integer type, Date date, String orderId) {
        return weatherCityFcBmBatchDAO.findAll(
                JpaWrappers.<WeatherCityFcBmBatchDO>lambdaQuery()
                        .eq(cityId != null, WeatherCityFcBmBatchDO::getCityId, cityId)
                        .eq(type != null, WeatherCityFcBmBatchDO::getType, type)
                        .eq(WeatherCityFcBmBatchDO::getDate, date)
                        .eq(orderId != null, WeatherCityFcBmBatchDO::getBatchId, orderId));
    }

    public List<WeatherCityFcBmBatchDO> findWeatherCityFcDODate(String cityId, Integer type, Date startDate, Date endDate, String orderId) {
        return weatherCityFcBmBatchDAO.findAll(
                JpaWrappers.<WeatherCityFcBmBatchDO>lambdaQuery()
                        .eq(cityId != null, WeatherCityFcBmBatchDO::getCityId, cityId)
                        .eq(type != null, WeatherCityFcBmBatchDO::getType, type)
                        .ge(WeatherCityFcBmBatchDO::getDate, startDate)
                        .le(WeatherCityFcBmBatchDO::getDate, endDate)
                        .eq(orderId != null, WeatherCityFcBmBatchDO::getBatchId, orderId));
    }

    public List<WeatherCityFcBmBatchDO> findWeatherCityFcDOSDate(List<String> cityIds, Integer type, Date startDate, Date endDate, String orderId) {
        return weatherCityFcBmBatchDAO.findAll(
                JpaWrappers.<WeatherCityFcBmBatchDO>lambdaQuery()
                        .in(cityIds != null, WeatherCityFcBmBatchDO::getCityId, cityIds)
                        .ge(WeatherCityFcBmBatchDO::getDate, startDate)
                        .le(WeatherCityFcBmBatchDO::getDate, endDate)
                        .eq(type != null, WeatherCityFcBmBatchDO::getType, type)
        );
    }
}
