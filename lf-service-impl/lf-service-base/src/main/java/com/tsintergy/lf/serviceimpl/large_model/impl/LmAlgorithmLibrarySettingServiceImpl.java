package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsintergy.lf.serviceapi.large_model.api.LmAlgorithmLibrarySettingService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmAlgorithmLibrarySettingDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmAlgorithmLibrarySettingDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 13:29
 **/
@Service("lmAlgorithmLibrarySettingService")
public class LmAlgorithmLibrarySettingServiceImpl implements LmAlgorithmLibrarySettingService {

    @Autowired
    private LmAlgorithmLibrarySettingDAO lmAlgorithmLibrarySettingDAO ;
    @Override
    public List<LmAlgorithmLibrarySettingDO> findAll() {
        return lmAlgorithmLibrarySettingDAO.findAll();
    }
}
