package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.large_model.api.LmParameterDetailsService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmParameterDetailsDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmParameterDetailsDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 14:05
 **/
@Service("lmParameterDetailsService")
public class LmParameterDetailsServiceImpl implements LmParameterDetailsService {
    @Autowired
    private LmParameterDetailsDAO lmParameterDetailsDAO;

    @Override
    public List<LmParameterDetailsDO> findByAlgorithm(String algorithmName) {
        return lmParameterDetailsDAO.findAll(JpaWrappers.<LmParameterDetailsDO>lambdaQuery()
                .eq(LmParameterDetailsDO::getAlgorithmName, algorithmName));
    }

    @Override
    public void saveOrUpdateBatch(List<LmParameterDetailsDO> lmParameterDetailsDOS) {
        lmParameterDetailsDAO.saveOrUpdateBatch(lmParameterDetailsDOS);
    }
}
