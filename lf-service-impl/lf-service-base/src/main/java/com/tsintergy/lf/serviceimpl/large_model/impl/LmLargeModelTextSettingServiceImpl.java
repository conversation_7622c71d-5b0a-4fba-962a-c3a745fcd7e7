package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.large_model.api.LmLargeModelTextSettingService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLargeModelTextSettingDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmLargeModelTextSettingDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LmLargeModelTextSettingServiceImpl implements LmLargeModelTextSettingService {

    @Autowired
    LmLargeModelTextSettingDAO lmLargeModelTextSettingDAO;

    @Override
    public LmLargeModelTextSettingDO getByField(String field) {
        return lmLargeModelTextSettingDAO.findOne(JpaWrappers.<LmLargeModelTextSettingDO>lambdaQuery()
                .eq(LmLargeModelTextSettingDO::getField, field));
    }
}
