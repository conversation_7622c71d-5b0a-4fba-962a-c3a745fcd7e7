
package com.tsintergy.lf.serviceimpl.load.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.DateScopeTypeEnum;
import com.tsintergy.lf.core.enums.FactoryDataEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.base.api.BaseIndustryInfoService;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseIndustryInfoDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryDayHisService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryMonthHisService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryQuarterHisService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryYearHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryDayHisServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryMonthHisServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryQuarterHisServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryYearHisServiceDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.BaseLoadIntervalDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.trade.api.IndustryCityLoadDayHisClctService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.IndustryCityLoadDayHisClctDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHis288DAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: LoadCityHisServiceImpl.java, v 0.1 2018-01-31 10:50:38 tao Exp $$
 */

@Service("loadCityHisService")
public class LoadCityHisServiceImpl extends BaseServiceImpl implements LoadCityHisService {

    private static final Logger logger = LogManager.getLogger(LoadCityHisServiceImpl.class);

    // 解析excel 固定从0000点开始到2400
    private static final Boolean EXCEL_LOAD_CURVE_START_WITH_ZERO = true;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityHis288DAO loadCityHis288DAO;

    @Autowired
    LoadCityHisClctService loadCityHisClctService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    AlgorithmService algorithmService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    LoadCityHisDAO loadCityHisDAO;

    @Autowired
    private LoadCityFcDAO loadCityFcDAO;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    LoadCityHisMapper loadCityHisMapper;

    @Autowired
    IndustryCityLoadDayHisClctService industryCityLoadDayHisClctService;

    @Autowired
    LoadFeatureIndustryDayHisService loadFeatureIndustryDayHisService;

    @Autowired
    LoadFeatureIndustryMonthHisService loadFeatureIndustryMonthHisService;

    @Autowired
    LoadFeatureIndustryQuarterHisService loadFeatureIndustryQuarterHisService;

    @Autowired
    LoadFeatureIndustryYearHisService loadFeatureIndustryYearHisService;

    @Autowired
    BaseIndustryInfoService baseIndustryInfoService;

    @Override
    public List<LoadCityHisDO> findLoadCityHisDOS(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        return loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public DataPackage queryLoadCityHisDO(DBQueryParam param) throws Exception {
        try {
            return loadCityHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO doCreate(LoadCityHisDO vo) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public void doRemoveLoadCityHisDO(LoadCityHisDO vo) throws Exception {
        try {
            loadCityHisDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public void doRemoveLoadCityHisDOByPK(Serializable pk) throws Exception {
        try {
            loadCityHisDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO doUpdateLoadCityHisDO(LoadCityHisDO vo) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO findLoadCityHisDOByPk(Serializable pk) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public List<LoadCityHisDO> getLoadCityHisDOSByCityIds(List<String> cityIds, Date startDate, Date endDate, String caliberId) {
        return loadCityHisDAO.getLoadCityHisDOSByCityIds(cityIds, startDate, endDate, caliberId);
    }

    @Override
    public List<CityValueDTO> find24LoadCityByDateAndCityIds(Date date, List<String> cityIds, String caliberId)
        throws Exception {
        List<CityValueDTO> cityValueDTOS = new ArrayList<CityValueDTO>();
        try {
            List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOSByCityIds(cityIds, date, date, caliberId);
            if (LoadCityHisDOS == null || LoadCityHisDOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706");
            } else {
                for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                    CityValueDTO cityValueDTO = new CityValueDTO();
                    cityValueDTO.setCityId(LoadCityHisDO.getCityId());
                    cityValueDTO.setCity(cityService.findCityById(LoadCityHisDO.getCityId()).getCity());
                    cityValueDTO.setValue(
                        BasePeriodUtils.toList(LoadCityHisDO, 24, true)); // 转24点负荷数据
                    cityValueDTOS.add(cityValueDTO);
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e);
        }
        return cityValueDTOS;

    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        return BasePeriodUtils
            .toList(LoadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(Date date, String cityId, String caliberId, String type)
        throws Exception {
        //历史曲线
        if (type.equals("1")){
            Date hisDate = DateUtils.addDays(date, 0);
            List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, hisDate, hisDate, caliberId);
            if (LoadCityHisDOS.size() < 1) {
                return new ArrayList<>();
            }
            return BasePeriodUtils
                .toList(LoadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        }else{
            List<LoadCityFcDO> reportLoadCityFcDOs = loadCityFcDAO.getReportLoadCityFcDOs(cityId, date, date, "", "");
            if (reportLoadCityFcDOs.size() < 1) {
                return new ArrayList<>();
            }
            return BasePeriodUtils
                .toList(reportLoadCityFcDOs.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        }
    }

    @Override
    public List<BigDecimal> findLoadCityHis288DO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHis288DO> LoadCityHisDOS = loadCityHis288DAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        return BasePeriodUtils.toList(LoadCityHisDOS.get(0), 288, Constants.LOAD_CURVE_START_WITH_ZERO);

    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        List<LoadCityHisDO> powerLoadHisCityClctDOS = null;
        try {
            powerLoadHisCityClctDOS = this.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        } catch (Exception e) {
            logger.error("历史出力查询异常...", e);
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
        List<BigDecimal> result = new ArrayList<>();
        List nullList = new ArrayList() {
            {
                for (int i = 0; i < 96; i++) {
                    add(null);
                }
            }
        };
        if (!CollectionUtils.isEmpty(powerLoadHisCityClctDOS) && powerLoadHisCityClctDOS.size() > 0) {
            Map<Date, LoadCityHisDO> mapData = powerLoadHisCityClctDOS.stream()
                .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (o, n) -> n));
            List<Date> listDate = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : listDate) {
                LoadCityHisDO hisCityClctDO = mapData.get(date);
                if (hisCityClctDO == null) {
                    result.addAll(nullList);
                } else {
                    result.addAll(BasePeriodUtils
                        .toList(hisCityClctDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO));
                }
            }
        }
        return result;
    }

    @Override
    public List<LoadCityHisDO> find24LoadCityHisDO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        return LoadCityHisDOS;
    }


    @Override
    public List<LoadHisDataDTO> findLoadCityVOsByCityId(Date startDate, Date endDate, String cityId, String caliberId)
        throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        Map<String, String> cityMap = cityService.findAllCitys().stream()
            .collect(Collectors.toMap(t -> t.getId(), t -> t.getCity()));
        List<LoadHisDataDTO> loadCommonDTOS = new ArrayList<LoadHisDataDTO>(10);
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            LoadHisDataDTO loadCommonDTO = new LoadHisDataDTO();
            loadCommonDTO.setId(LoadCityHisDO.getId());
            loadCommonDTO.setDate(LoadCityHisDO.getDate());
            loadCommonDTO.setData(BasePeriodUtils
                .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            loadCommonDTO.setWeek(DateUtil.getWeek(LoadCityHisDO.getDate()));
            loadCommonDTO.setCity(cityMap.get(LoadCityHisDO.getCityId()));
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }

    @Override
    public void doUpdateIndustryLoadCityHisDO(List<LoadHisDataDTO> loadHisDataDTO) throws Exception {
        for (LoadHisDataDTO hisDataDTO : loadHisDataDTO) {
            IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO = new IndustryCityLoadDayHisClctDO();
            List<BigDecimal> data = hisDataDTO.getData();
            industryCityLoadDayHisClctDO.setTradeCode(hisDataDTO.getIndustryNo());
            industryCityLoadDayHisClctDO.setCityId(cityService.findCityByName(hisDataDTO.getCity()).getId());
            industryCityLoadDayHisClctDO.setDate(new java.sql.Date(hisDataDTO.getDate().getTime()));
            industryCityLoadDayHisClctDO.setTradeCodeDsc(hisDataDTO.getIndustryName());
            BasePeriodUtils.setAllFiled(industryCityLoadDayHisClctDO,
                    ColumnUtil.listToMap(data, Constants.LOAD_CURVE_START_WITH_ZERO));
            industryCityLoadDayHisClctService.doUpdateIndustryLoadCityHisDO(industryCityLoadDayHisClctDO);
        }
    }

    @Override
    public void doUpdateFactoryLoadCityHisDO(List<LoadHisDataDTO> loadHisDataDTO) throws Exception {

    }

    @Override
    public void doUpdateFactoryPowerCityHisDO(List<PowerCityDataDTO> loadHisDataDTO) throws Exception {

    }

    @Override
    public void doUpdateIndustryPowerCityHisDO(List<PowerCityDataDTO> loadHisDataDTO) throws Exception {

    }

    @Override
    public List<LoadHisDataDTO> findIndustryLoadCityVOsByCityId(Date startDate, Date endDate, String cityId,
                                                                List<String> industryIds) throws Exception {
        List<LoadHisDataDTO> loadCommonDTOS = new ArrayList<>();
        Map<String, List<BaseIndustryInfoDO>> collect = baseIndustryInfoService.findIndustryInfoByLevel().stream().collect(Collectors.groupingBy(BaseIndustryInfoDO::getCode));
        List<IndustryCityLoadDayHisClctDO> industryCityLoadDayHisClctDOS = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, industryIds, startDate, endDate);
        CityDO cityById = cityService.findCityById(cityId);
        if (!CollectionUtils.isEmpty(industryCityLoadDayHisClctDOS)) {
            Map<java.sql.Date, List<IndustryCityLoadDayHisClctDO>> collectData = industryCityLoadDayHisClctDOS.stream().collect(Collectors.groupingBy(IndustryCityLoadDayHisClctDO::getDate));
            DateUtil.getListBetweenDay(startDate, endDate).forEach(date -> {
                industryIds.forEach(industryId -> {
                    LoadHisDataDTO loadFeatureExtendDTO = new LoadHisDataDTO();
                    loadFeatureExtendDTO.setId(UUID.randomUUID().toString().replace("-", ""));
                    loadFeatureExtendDTO.setCity(cityById.getCity());
                    loadFeatureExtendDTO.setDate(date);
                    loadFeatureExtendDTO.setWeek(DateUtil.getWeek(date));
                    List<BaseIndustryInfoDO> baseIndustryInfoDOS = collect.get(industryId);
                    loadFeatureExtendDTO.setIndustryName(baseIndustryInfoDOS.get(0).getName());
                    loadFeatureExtendDTO.setIndustryNo(industryId);
                    List<IndustryCityLoadDayHisClctDO> industryData = collectData.get(date);
                    if (!CollectionUtils.isEmpty(industryData)) {
                        Map<String, List<IndustryCityLoadDayHisClctDO>> collect1 = industryData.stream().collect(Collectors.groupingBy(t -> t.getTradeCode()));
                        List<IndustryCityLoadDayHisClctDO> industryCityLoadDayHisClctDOS1 = collect1.get(industryId);
                        if (!CollectionUtils.isEmpty(industryCityLoadDayHisClctDOS1)) {
                            loadFeatureExtendDTO.setData(industryCityLoadDayHisClctDOS1.get(0).getloadList());
                        }
                    }
                    loadCommonDTOS.add(loadFeatureExtendDTO);
                });
            });
        }
        return loadCommonDTOS;
    }

    @Override
    public List<LoadHisDataDTO> findFactoryLoadCityVOsByCityId(Date startDate, Date endDate, String cityId, String factory) throws Exception {
        List<LoadHisDataDTO> loadCommonDTOS = new ArrayList<>();
        CityDO cityById = cityService.findCityById(cityId);
        DateUtil.getListBetweenDay(startDate, endDate).forEach(date -> {
            LoadHisDataDTO loadFeatureExtendDTO = new LoadHisDataDTO();
            loadFeatureExtendDTO.setId(UUID.randomUUID().toString().replace("-", ""));
            loadFeatureExtendDTO.setCity(cityById.getCity());
            loadFeatureExtendDTO.setDate(date);
            loadFeatureExtendDTO.setWeek(DateUtil.getWeek(date));
            List<BigDecimal> bigDecimal = new ArrayList<>();
            for (int i = 0; i < 96; i++) {
                bigDecimal.add(BigDecimal.valueOf(i));
            }
            loadFeatureExtendDTO.setData(bigDecimal);
            for (FactoryDataEnum value : FactoryDataEnum.values()) {
                if (value.getId().equals(factory)) {
                    loadFeatureExtendDTO.setFactoryName(value.getName() + "测试厂电");
                    loadFeatureExtendDTO.setFactoryType(value.getName());
                }
            }
            loadCommonDTOS.add(loadFeatureExtendDTO);
        });
        return loadCommonDTOS;
    }

    @Override
    public List<LoadFeatureExtendDTO> findIndustryFeatureCityVOsByCityId(Date startDate, Date endDate, String cityId,
                                                                         List<String> industryIds) throws Exception {
        List<LoadFeatureExtendDTO> result = new ArrayList<>();
        List<LoadFeatureIndustryDayHisServiceDO> loadFeatureIndustryListByCodeList = loadFeatureIndustryDayHisService.getLoadFeatureIndustryListByCodeList(startDate, endDate, cityId, industryIds);
        Map<String, List<BaseIndustryInfoDO>> collect = baseIndustryInfoService.findIndustryInfoByLevel().stream().collect(Collectors.groupingBy(BaseIndustryInfoDO::getCode));
        CityDO cityById = cityService.findCityById(cityId);
        if (!CollectionUtils.isEmpty(loadFeatureIndustryListByCodeList)) {
            Map<Date, List<LoadFeatureIndustryDayHisServiceDO>> collect1 = loadFeatureIndustryListByCodeList.stream().collect(Collectors.groupingBy(LoadFeatureIndustryDayHisServiceDO::getDate));
            DateUtil.getListBetweenDay(startDate, endDate).forEach(date -> {
                industryIds.forEach(industryId -> {
                    LoadFeatureExtendDTO loadFeatureExtendDTO = new LoadFeatureExtendDTO();
                    loadFeatureExtendDTO.setId(UUID.randomUUID().toString().replace("-", ""));
                    loadFeatureExtendDTO.setCity(cityById.getCity());
                    loadFeatureExtendDTO.setDate(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                    loadFeatureExtendDTO.setWeek(DateUtil.getWeek(date));
                    loadFeatureExtendDTO.setIndustryName(collect.get(industryId).get(0).getName());
                    loadFeatureExtendDTO.setIndustryNo(industryId);
                    List<LoadFeatureIndustryDayHisServiceDO> loadFeatureIndustryDayHisServiceDOS = collect1.get(date);
                    if (!CollectionUtils.isEmpty(loadFeatureIndustryDayHisServiceDOS)) {
                        Map<String, List<LoadFeatureIndustryDayHisServiceDO>> collect2 = loadFeatureIndustryDayHisServiceDOS.stream().collect(Collectors.groupingBy(LoadFeatureIndustryDayHisServiceDO::getType));
                        List<LoadFeatureIndustryDayHisServiceDO> loadFeatureIndustryDayHisServiceDOS1 = collect2.get(industryId);
                        if (!CollectionUtils.isEmpty(loadFeatureIndustryDayHisServiceDOS1)) {
                            loadFeatureExtendDTO.setAveLoad(loadFeatureIndustryDayHisServiceDOS1.get(0).getAveLoad());
                            loadFeatureExtendDTO.setAvgLoad(loadFeatureIndustryDayHisServiceDOS1.get(0).getAveLoad());
                            loadFeatureExtendDTO.setEnergy(loadFeatureIndustryDayHisServiceDOS1.get(0).getEnergy());
                            loadFeatureExtendDTO.setDifferent(loadFeatureIndustryDayHisServiceDOS1.get(0).getDifferent());
                            loadFeatureExtendDTO.setMaxLoad(loadFeatureIndustryDayHisServiceDOS1.get(0).getMaxLoad());
                            loadFeatureExtendDTO.setMinLoad(loadFeatureIndustryDayHisServiceDOS1.get(0).getMinLoad());
                            if (loadFeatureExtendDTO.getMaxLoad() != null && loadFeatureExtendDTO.getDifferent() != null) {
                                BigDecimal divide = loadFeatureExtendDTO.getDifferent().divide(loadFeatureExtendDTO.getMaxLoad(), 4, BigDecimal.ROUND_HALF_UP);
                                loadFeatureExtendDTO.setGradient(divide.divide(BigDecimal.valueOf(100)));
                            }
                        }
                    }
                    result.add(loadFeatureExtendDTO);
                });
            });
        }
        return result;
    }

    @Override
    public List<PowerCityDataDTO> findPowerIndustryCityDataDTO(String startDate, String endDate, String cityId, String dateType, String industryId) throws Exception {
        List<PowerCityDataDTO> result = new ArrayList<>();
        BaseIndustryInfoDO industryInfoById = baseIndustryInfoService.findIndustryInfoById(industryId);
        CityDO cityById = cityService.findCityById(cityId);
        PowerCityDataDTO powerCityDataDTO = new PowerCityDataDTO();
        powerCityDataDTO.setName(cityById.getCity());
        powerCityDataDTO.setIndustryName(industryInfoById.getName());
        List<PowerCityValueDTO> powerCityValues = new ArrayList<>();
        if (DateScopeTypeEnum.DAY.getId().equals(dateType)) {
            List<LoadFeatureIndustryDayHisServiceDO> loadFeatureIndustryDayHisServiceList = loadFeatureIndustryDayHisService.getLoadFeatureIndustryDayHisServiceList(DateUtil.getDate(startDate, "yyyy-MM-dd"), DateUtil.getDate(endDate, "yyyy-MM-dd"), cityId, industryId);
            Map<String, List<LoadFeatureIndustryDayHisServiceDO>> collect;
            if (!CollectionUtils.isEmpty(loadFeatureIndustryDayHisServiceList)) {
                collect = loadFeatureIndustryDayHisServiceList.stream().collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
            } else {
                collect = new HashMap<>();
            }
            DateUtil.getListBetweenDay(startDate, endDate).forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date);
                List<LoadFeatureIndustryDayHisServiceDO> industryCityLoadDayHisClctDOS = collect.get(date);
                if (!CollectionUtils.isEmpty(industryCityLoadDayHisClctDOS)) {
                    powerCityValueDTO.setValue(industryCityLoadDayHisClctDOS.get(0).getEnergy());
                }
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        } else if (DateScopeTypeEnum.MONTH.getId().equals(dateType)) {
            List<LoadFeatureIndustryMonthHisServiceDO> loadFeatureIndustryMonthHisServiceList = loadFeatureIndustryMonthHisService.getLoadFeatureIndustryMonthHisServiceList(cityId, industryId, startDate, endDate);
            Map<String, List<LoadFeatureIndustryMonthHisServiceDO>> collect;
            if (!CollectionUtils.isEmpty(loadFeatureIndustryMonthHisServiceList)) {
                collect = loadFeatureIndustryMonthHisServiceList.stream().collect(Collectors.groupingBy(t -> t.getYear() + "-" + t.getMonth()));
            } else {
                collect = new HashMap<>();
            }
            List<String> yearMonthList = DateUtil.getYearMonthList(startDate, endDate);
            yearMonthList.forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date);
                List<LoadFeatureIndustryMonthHisServiceDO> loadFeatureIndustryMonthHisServiceDOS = collect.get(date);
                if (!CollectionUtils.isEmpty(loadFeatureIndustryMonthHisServiceDOS)) {
                    powerCityValueDTO.setValue(loadFeatureIndustryMonthHisServiceDOS.get(0).getEnergy());
                }
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        } else if (DateScopeTypeEnum.QUARTER.getId().equals(dateType)) {
            List<LoadFeatureIndustryQuarterHisServiceDO> loadFeatureIndustryQuarterHisServiceList = loadFeatureIndustryQuarterHisService.getLoadFeatureIndustryQuarterHisServiceAllList(cityId, industryId, startDate.substring(0, 4), endDate.substring(0, 4));
            Map<String, List<LoadFeatureIndustryQuarterHisServiceDO>> collect;
            if (!CollectionUtils.isEmpty(loadFeatureIndustryQuarterHisServiceList)) {
                collect = loadFeatureIndustryQuarterHisServiceList.stream().collect(Collectors.groupingBy(t -> t.getYear() + "-" + t.getQuarter()));
            } else {
                collect = new HashMap<>();
            }
            DateUtil.getTargetQuarterList(startDate, endDate).forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date.split("-")[0] + "-Q" + date.split("-")[1]);
                List<LoadFeatureIndustryQuarterHisServiceDO> loadFeatureIndustryQuarterHisServiceDOS = collect.get(date);
                if (!CollectionUtils.isEmpty(loadFeatureIndustryQuarterHisServiceDOS)) {
                    powerCityValueDTO.setValue(loadFeatureIndustryQuarterHisServiceDOS.get(0).getEnergy());
                }
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        } else if (DateScopeTypeEnum.YEAR.getId().equals(dateType)) {
            List<LoadFeatureIndustryYearHisServiceDO> loadFeatureIndustryYearHisServiceList = loadFeatureIndustryYearHisService.getLoadFeatureIndustryYearHisServiceList(cityId, industryId, startDate, endDate);
            Map<String, List<LoadFeatureIndustryYearHisServiceDO>> collect;
            if (!CollectionUtils.isEmpty(loadFeatureIndustryYearHisServiceList)) {
                collect = loadFeatureIndustryYearHisServiceList.stream().collect(Collectors.groupingBy(t -> t.getYear()));
            } else {
                collect = new HashMap<>();
            }
            DateUtil.getYearList(startDate, endDate).forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date);
                List<LoadFeatureIndustryYearHisServiceDO> loadFeatureIndustryYearHisServiceDOS = collect.get(date);
                if (!CollectionUtils.isEmpty(loadFeatureIndustryYearHisServiceDOS)) {
                    powerCityValueDTO.setValue(loadFeatureIndustryYearHisServiceDOS.get(0).getEnergy());
                }
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        }
        result.add(powerCityDataDTO);
        return result;
    }

    @Override
    public List<PowerCityDataDTO> findPowerFactoryCityDataDTO(String startDate, String endDate, String cityId, String dateType, String factoryId) throws Exception {
        List<PowerCityDataDTO> result = new ArrayList<>();
        CityDO cityById = cityService.findCityById(cityId);
        PowerCityDataDTO powerCityDataDTO = new PowerCityDataDTO();
        powerCityDataDTO.setName(cityById.getCity());
        for (FactoryDataEnum value : FactoryDataEnum.values()) {
            if (value.getId().equals(factoryId)) {
                powerCityDataDTO.setFactoryType(value.getName());
                powerCityDataDTO.setFactoryName(value.getName() + "测试厂电");
            }
        }
        List<PowerCityValueDTO> powerCityValues = new ArrayList<>();
        if (DateScopeTypeEnum.DAY.getId().equals(dateType)) {
            DateUtil.getListBetweenDay(startDate, endDate).forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date);
                powerCityValueDTO.setValue(BigDecimal.valueOf(2352.25));
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        } else if (DateScopeTypeEnum.MONTH.getId().equals(dateType)) {
            List<String> yearMonthList = DateUtil.getYearMonthList(startDate, endDate);
            yearMonthList.forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date);
                powerCityValueDTO.setValue(BigDecimal.valueOf(3416.48));
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        } else if (DateScopeTypeEnum.QUARTER.getId().equals(dateType)) {
            DateUtil.getTargetQuarterList(startDate, endDate).forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date.split("-")[0] + "-Q" + date.split("-")[1]);
                powerCityValueDTO.setValue(BigDecimal.valueOf(4416.48));
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        } else if (DateScopeTypeEnum.YEAR.getId().equals(dateType)) {
            DateUtil.getYearList(startDate, endDate).forEach(date -> {
                PowerCityValueDTO powerCityValueDTO = new PowerCityValueDTO();
                powerCityValueDTO.setDateStr(date);
                powerCityValueDTO.setValue(BigDecimal.valueOf(5150.05));
                powerCityValues.add(powerCityValueDTO);
                powerCityDataDTO.setPowerCityValues(powerCityValues);
            });
        }
        result.add(powerCityDataDTO);
        return result;
    }


    @Override
    public List<LoadCommonDTO<Date>> findLoadCityVOsByCityIdAndDates(String cityId, List<Date> dates, String caliberId)
        throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOSByDates(cityId, dates, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706", "");
        }
        List<LoadCommonDTO<Date>> loadCommonDTOS = new ArrayList<LoadCommonDTO<Date>>(10);
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            LoadCommonDTO<Date> loadCommonDTO = new LoadCommonDTO<Date>();
            loadCommonDTO.setName(LoadCityHisDO.getDate());
            loadCommonDTO.setData(BasePeriodUtils
                .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }

    @Override
    public List<LoadCityHisDO> findLoadCityDOsByCityIdInDates(String cityId, List<Date> dates, String caliberId)
        throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOSByDates(cityId, dates, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("01C20180002");
        } else {
            return LoadCityHisDOS;
        }
    }


    @Override
    public CityValueDTO find24LoadCityBetweenDate(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        try {
            List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
            if (LoadCityHisDOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706", "");
            } else {
                CityValueDTO cityValueDTO = new CityValueDTO();
                cityValueDTO.setCityId(LoadCityHisDOS.get(0).getCityId());
                cityValueDTO.setCity(cityService.findCityById(cityValueDTO.getCityId()).getCity());
                List<BigDecimal> values = new ArrayList<BigDecimal>();
                for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                    values.addAll(BasePeriodUtils.toList(LoadCityHisDO, 24, Constants.LOAD_CURVE_START_WITH_ZERO));
                }
                cityValueDTO.setValue(values);
                return cityValueDTO;
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003", e);
        }
    }

    @Override
    public List<LoadCityHisDO> doUpdateLoadCityHisDO(List<LoadHisDTO> loadHisDTOS, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = new ArrayList<LoadCityHisDO>();
        for (LoadHisDTO loadHisDTO : loadHisDTOS) {
            LoadCityHisDO LoadCityHisDO = new LoadCityHisDO();
            LoadCityHisDO.setId(loadHisDTO.getId());
            LoadCityHisDO.setCityId(loadHisDTO.getCityId());
            LoadCityHisDO.setCaliberId(caliberId);
            LoadCityHisDO.setDate(new java.sql.Date(loadHisDTO.getDate().getTime()));
//            LoadCityHisDO.setAllFied(loadHisDTO.getData());
            BasePeriodUtils.setAllFiled(LoadCityHisDO,
                ColumnUtil.listToMap(loadHisDTO.getData(), Constants.LOAD_CURVE_START_WITH_ZERO));
            LoadCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            LoadCityHisDOS.add(this.doUpdateLoadCityHisDO(LoadCityHisDO));
        }
        return LoadCityHisDOS;
    }

    @Override
    public List<LoadCityHisDO> doUpdateLoadCityHisDOFromExcel(List<List<List<String>>> dataList)
        throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = new ArrayList<LoadCityHisDO>();
        List<List<String>> rows = dataList.get(0);
        for (int i = 1; i < rows.size(); i++) {
            LoadCityHisDO excelLoadCityHisDO = new LoadCityHisDO();
            excelLoadCityHisDO.setCityId(cityService.findCityByName(rows.get(i).get(0)).getId());
            excelLoadCityHisDO.setDate(java.sql.Date.valueOf(rows.get(i).get(1)));
            CaliberDO caliberVO = caliberService.findCaliberDOByName(rows.get(i).get(2));
            if (null != caliberVO) {
                excelLoadCityHisDO.setCaliberId(caliberVO.getId());
            } else {
                throw TsieExceptionUtils.newBusinessException("01C20180008");
            }
            LoadCityHisDO LoadCityHisDO = loadCityHisDAO
                .getLoadCityHisDOByOneDate(excelLoadCityHisDO.getCityId(), excelLoadCityHisDO.getDate(),
                    excelLoadCityHisDO.getCaliberId());
            if (LoadCityHisDO == null) {
                LoadCityHisDO = new LoadCityHisDO();
                LoadCityHisDO.setCityId(excelLoadCityHisDO.getCityId());
                LoadCityHisDO.setCaliberId(excelLoadCityHisDO.getCaliberId());
                LoadCityHisDO.setDate(excelLoadCityHisDO.getDate());
            }

                List<BigDecimal> list = new ArrayList<BigDecimal>();
                for (String data : rows.get(i).subList(4, rows.get(i).size())) {
                    list.add(new BigDecimal(data));
                }
                BasePeriodUtils
                    .setAllFiled(LoadCityHisDO, ColumnUtil.listToMap(list.subList(0,list.size() - 1), EXCEL_LOAD_CURVE_START_WITH_ZERO));
                LoadCityHisDO.setT2400(list.get(list.size() - 1));

            if (StringUtils.isEmpty(LoadCityHisDO.getId())) {// 插入
                LoadCityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                LoadCityHisDOS.add(this.doCreate(LoadCityHisDO));
            } else {
                LoadCityHisDOS.add(this.doUpdateLoadCityHisDO(LoadCityHisDO));
            }

        }
        return LoadCityHisDOS;
    }


    public Workbook getWorkbok(InputStream in, File file) throws IOException {
        Workbook wb = null;
        if (file.getName().endsWith("xlsx")) {
            wb = new XSSFWorkbook(in);
        }
        return wb;
    }

    private Object getValue(Cell cell) {
        Object obj = null;
        switch (cell.getCellTypeEnum()) {
            case BOOLEAN:
                obj = cell.getBooleanCellValue();
                break;
            case ERROR:
                obj = cell.getErrorCellValue();
                break;
            case NUMERIC:
                obj = cell.getNumericCellValue();
                break;
            case STRING:
                obj = cell.getStringCellValue();
                break;
            default:
                break;
        }
        return obj;
    }

    /**
     * 功能描述: <br>
     * <p>
     * 获取文件夹下所有的文件名
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/9/18 9:03
     */
    public ArrayList<String> getFiles(String path) {
        ArrayList<String> files = new ArrayList<String>();
        File file = new File(path);
        File[] tempList = file.listFiles();

        for (int i = 0; i < tempList.length; i++) {
            if (tempList[i].isFile()) {
                files.add(tempList[i].toString());
                System.out.println(tempList[i].toString());
            }
        }
        return files;
    }


    @Override
    public List<LoadCityHisDO> getLoadCityHisDOS(String cityId, String caliberId, Date startDate, Date endDate)
        throws Exception {
        return loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public void doInsertOrUpdate(LoadCityHisDO LoadCityHisDO) throws Exception {
        LoadCityHisDO LoadCityHisDOTemp = loadCityHisDAO
            .getLoadCityHisDOByOneDate(LoadCityHisDO.getCityId(), LoadCityHisDO.getDate(), LoadCityHisDO.getCaliberId());
        if (LoadCityHisDOTemp == null) {
            loadCityHisDAO.createAndFlush(LoadCityHisDO);
            return;
        }
        loadCityHisDAO.getSession().flush();
        loadCityHisDAO.getSession().clear();
        LoadCityHisDO.setId(LoadCityHisDOTemp.getId());
        LoadCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisDAO.updateAndFlush(LoadCityHisDO);
    }

    @Override
    public void doInsertOrUpdateBatch(List<LoadCityHisDO> LoadCityHisDOS) throws Exception {
        for (LoadCityHisDO loadCityHisDO : LoadCityHisDOS) {
            this.doInsertOrUpdate(loadCityHisDO);
        }
    }


    /**
     * 预测查询--负荷预测
     */
    @Override
    public LoadQueryDTO findLoad(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId)
        throws Exception {
        //真实数据
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        //查询上报数据
        List<LoadCityFcDO> LoadCityFcDOS = loadCityFcService.findReportLoadFc(cityId, caliberId, startDate, endDate);
        //查询算法的数据
        List<LoadCityFcDO> fcVOS = loadCityFcService
            .findFcByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate);
        if (LoadCityHisDOS.size() < 1 && LoadCityFcDOS.size() < 1 && fcVOS.size() < 1) {
            return null;
        }
        //分别转map
        Map<Date, LoadCityHisDO> realMap = LoadCityHisDOS.stream()
            .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, LoadCityFcDO> reportMap = LoadCityFcDOS.stream()
            .collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, LoadCityFcDO> fcMap = fcVOS.stream()
            .collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> real = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        List<BigDecimal> report = new ArrayList<>();
        LoadQueryDTO queryDTO = new LoadQueryDTO();
        for (Date date : dateList) {
            LoadCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                real.addAll(BasePeriodUtils
                    .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                real.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
            LoadCityFcDO reportVo = reportMap.get(date);
            report.addAll(getList(reportVo));
            LoadCityFcDO fcVO = fcMap.get(date);
            fc.addAll(getList(fcVO));
        }
        queryDTO.setFc(fc);
        queryDTO.setReal(real);
        queryDTO.setReport(report);
        queryDTO.setAlgorithmName(algorithmService.findAlgorithmVOByPk(algorithmId).getAlgorithmCn());
        return queryDTO;
    }

    @Override
    public List<CityLoadDTO> find24CityLoadDTO(Date today, List<String> cityIds, String caliberId) throws Exception {
        Map<String, LoadCityHisDO> todayHisLoadsMap = getLoadCityHisDOMap(today, cityIds, caliberId);
        Map<String, LoadCityHisDO> yesterdayLoadsMap = getLoadCityHisDOMap(DateUtils.addDays(today, -1), cityIds,
            caliberId);
        Map<String, LoadCityFcDO> todayFcMap = getLoadCityFcDOMap(today, cityIds, caliberId);
        Map<String, WeatherCityHisDO> todayTemperaturesHisVOMap = getWeatherCityVOMap(today, cityIds);
        Map<String, String> cityMap = cityService.findAllCitys().stream()
            .collect(Collectors.toMap(CityDO::getId, CityDO::getCity));
        List<CityLoadDTO> cityLoadDTOS = new ArrayList<>();
        for (String cityId : cityIds) {
            CityLoadDTO cityLoadDTO = new CityLoadDTO();
            cityLoadDTO.setCityName(cityMap.get(cityId));
            setTodayHisLoads(todayHisLoadsMap, cityId, cityLoadDTO);
            setYesterdayHisLoads(yesterdayLoadsMap, cityId, cityLoadDTO);
            setTodayFcLoads(todayFcMap, cityId, cityLoadDTO, today, caliberId);
            setTodayTemperatures(todayTemperaturesHisVOMap, cityId, cityLoadDTO);
            cityLoadDTOS.add(cityLoadDTO);
        }
        return cityLoadDTOS;
    }

    @Override
    public List<AreaLoadRateDTO> find24AreaLoadRateDTO(Date date, String caliberId) throws Exception {
        Map<String, String> cityMap = cityService.findAllCitys().stream()
            .filter(cityVO -> StringUtils.isNotBlank(cityVO.getArea()))
            .collect(Collectors.toMap(CityDO::getId, CityDO::getArea));
        List<LoadCityHisDO> LoadCityHisDOS = this.findLoadCityHisDO(date, caliberId);
        if (!CollectionUtils.isEmpty(LoadCityHisDOS)) {
            Map<String, List<BigDecimal>> areaLoadsMap = new HashMap<>();
            List<BigDecimal> provinceLoads = null;
            for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                if (LoadCityHisDO.getCityId().equals(CityConstants.PROVINCE_ID)) {
                    provinceLoads = BasePeriodUtils
                        .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, true);
                    continue;
                }
                CalcAreaLoads(cityMap, areaLoadsMap, LoadCityHisDO);
            }

            if (!areaLoadsMap.isEmpty()) {
                List<AreaLoadRateDTO> areaLoadRateDTOS = new ArrayList<>();
                Set<String> areaNames = areaLoadsMap.keySet();
                Iterator<String> iterator = areaNames.iterator();
                while (iterator.hasNext()) {
                    String areaName = iterator.next();
                    List<BigDecimal> areaLoads = areaLoadsMap.get(areaName);
                    AreaLoadRateDTO areaLoadRateDTO = new AreaLoadRateDTO();
                    areaLoadRateDTO.setAreaName(areaName);
                    List<BigDecimal> areaLoadsRate = DataUtil
                        .listDivide(areaLoads, provinceLoads);
                    areaLoadRateDTO.setLoadRates(areaLoadsRate);
                    areaLoadRateDTOS.add(areaLoadRateDTO);
                }
                return areaLoadRateDTOS;
            }
        }

        return null;
    }

    @Override
    public Date24LoadDTO findDate24LoadDTOS(Date date, String cityId, String caliberId) throws Exception {
        LoadCityHisDO LoadCityHisDO = null;
        List<LoadCityHisDO> LoadCityHisDOs = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (!CollectionUtils.isEmpty(LoadCityHisDOs)) {
            LoadCityHisDO = LoadCityHisDOs.get(0);
        }
        LoadCityFcDO LoadCityFcDOS = loadCityFcDAO.getReportLoadCityFcDO(cityId, caliberId, date);
        Date24LoadDTO date24LoadDTO = new Date24LoadDTO();
        date24LoadDTO.setCityId(cityId);
        if (LoadCityFcDOS != null) {
            date24LoadDTO.setFcLoads(BasePeriodUtils
                .toList(LoadCityFcDOS, Constants.LOAD_CURVE_POINT_NUM_24, true));
        }
        if (LoadCityHisDO != null) {
            date24LoadDTO.setHisLoads(BasePeriodUtils
                .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, true));
        }
        return date24LoadDTO;
    }

    @Override
    public LoadCityHisDO findLoadOfBaseDay(Date forecastDay, String caliberId, String cityId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(forecastDay.getTime()))
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .addOrderByDesc("date");

        return (LoadCityHisDO) loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas().get(0);
    }

    @Override
    public LoadCityHisDO getLoadCityHisDO(String cityId, String caliberId, Date date) throws Exception {
        return loadCityHisDAO.getLoadCityHisDOByOneDate(cityId, date, caliberId);
    }

    @Override
    public List<LoadCityHisDO> queryLoadCityHisDOList(List<Date> showDates, String cityId, String caliberId) {
        return loadCityHisMapper.selectList(
            new QueryWrapper<LoadCityHisDO>().lambda()
                .eq(StringUtils.isNotBlank(cityId), LoadCityHisDO::getCityId, cityId)
                .eq(StringUtils.isNotBlank(caliberId), LoadCityHisDO::getCaliberId, caliberId)
                .in(!CollectionUtils.isEmpty(showDates), LoadCityHisDO::getDate, showDates)
        );
    }

    @Override
    public List<BaseLoadIntervalDO> selectListData(String cityId, String caliberId, Date startDate,
                                                   Date endDate) throws Exception {
        List<BaseLoadIntervalDO> resultList = new ArrayList<>();
        List<LoadCityHisDO> hisDOS = findLoadCityHisDOS(cityId, startDate, endDate, caliberId);
        for (LoadCityHisDO his : hisDOS) {
            BaseLoadIntervalDO interval = new BaseLoadIntervalDO();
            BeanUtils.copyProperties(his, interval);
            interval.setAllTimeList(
                    DateUtil.getAllTimeListByInterval(Constants.MINUTE, 15, Constants.LOAD_CURVE_START_WITH_ZERO));
            interval.setHisLoadList(BasePeriodUtils
                    .toList(his, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            resultList.add(interval);
        }
        return resultList;
    }

    @Override
    public List<CurveViewDTO> findCurveViewDTOS(Date date, String caliberId, String cityId) throws Exception {
        List<CurveViewDTO> result = new ArrayList<>();

        //今日预测负荷
        LoadCityFcDO loadCityFcValue = loadCityFcService.find96LoadCityFcValue(date, caliberId, cityId);
        List<BigDecimal> todayFcLoads = BasePeriodUtils
            .toList(loadCityFcValue, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> todayFc = getBigDecimals(todayFcLoads);
        result.add(new CurveViewDTO(CurveViewDTO.TODAY_FC, todayFc));

        //今日历史负荷
        List<BigDecimal> todayHisLoads = this.find96LoadCityHisValue(date, caliberId, cityId);
        List<BigDecimal> todayHis = getBigDecimals(todayHisLoads);
        result.add(new CurveViewDTO(CurveViewDTO.TODAY_HIS, todayHis));

        //昨日历史负荷
        List<BigDecimal> yesterHisLoads = this
            .find96LoadCityHisValue(DateUtils.addDays(date, -1), caliberId, cityId);
        List<BigDecimal> yesterHis = getBigDecimals(yesterHisLoads);
        result.add(new CurveViewDTO(CurveViewDTO.YESTARDAY_HIS, yesterHis));

        //预测温度
        List<BigDecimal> temperatureFcs = weatherCityFcService
            .find96WeatherCityFcValue(date, cityId, WeatherEnum.TEMPERATURE.getType());
        if (CollectionUtils.isEmpty(temperatureFcs)) {
            temperatureFcs = ColumnUtil
                .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, null);
        }
        result.add(new CurveViewDTO(CurveViewDTO.TEMPERATURE_FC, temperatureFcs));

        //历史温度
        List<BigDecimal> temperatureHis = weatherCityHisService
            .find96WeatherCityHisValue(date, cityId, WeatherEnum.TEMPERATURE.getType());
        if (CollectionUtils.isEmpty(temperatureHis)) {
            temperatureHis = ColumnUtil
                .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, null);
        }
        result.add(new CurveViewDTO(CurveViewDTO.TEMPERATURE_HIS, temperatureHis));

        return result;
    }

    private List<BigDecimal> getBigDecimals(List<BigDecimal> yesterHisLoads) {
        List<BigDecimal> yesterHis = null;
        if (CollectionUtils.isEmpty(yesterHisLoads)) {
            yesterHis = ColumnUtil
                .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, null);
        } else {
            yesterHis = new ArrayList<>();
            for (BigDecimal yesterHisLoad : yesterHisLoads) {
                yesterHis.add(yesterHisLoad);
            }
        }
        return yesterHis;
    }

    private List<BigDecimal> find96LoadCityHisValue(Date date, String caliberId, String cityId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.StringEqualTo, "cityId", cityId);
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
        if (!CollectionUtils.isEmpty(LoadCityHisDOS)) {
            return BasePeriodUtils
                .toList(LoadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    private void CalcAreaLoads(Map<String, String> cityMap, Map<String, List<BigDecimal>> areaLoadsMap,
        LoadCityHisDO LoadCityHisDO) {
        String areaName = cityMap.get(LoadCityHisDO.getCityId());
        if (StringUtils.isNotBlank(areaName)) {
            List<BigDecimal> loads = areaLoadsMap.get(areaName);
            if (CollectionUtils.isEmpty(loads)) {
                loads = BasePeriodUtils
                    .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, true);
            } else {
                loads = DataUtil.listAdd(loads, BasePeriodUtils
                    .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, true));
            }
            areaLoadsMap.put(areaName, loads);
        }
    }

    private void setTodayHisLoads(Map<String, LoadCityHisDO> todayHisLoadsMap, String cityId, CityLoadDTO cityLoadDTO) {
        if (todayHisLoadsMap != null && !todayHisLoadsMap.isEmpty()) {
            LoadCityHisDO todayHisLoad = todayHisLoadsMap.get(cityId);
            if (todayHisLoad != null) {
                List<BigDecimal> todayHisLoads = BasePeriodUtils
                    .toList(todayHisLoad, Constants.LOAD_CURVE_POINT_NUM_24, true);
                cityLoadDTO.setTodayHisLoads(todayHisLoads);
            }
        }
    }

    private void setYesterdayHisLoads(Map<String, LoadCityHisDO> yesterdayHisLoadsMap, String cityId,
        CityLoadDTO cityLoadDTO) {
        if (yesterdayHisLoadsMap != null && !yesterdayHisLoadsMap.isEmpty()) {
            LoadCityHisDO yesterdayHisLoad = yesterdayHisLoadsMap.get(cityId);
            if (yesterdayHisLoad != null) {
                List<BigDecimal> todayHisLoads = BasePeriodUtils
                    .toList(yesterdayHisLoad, Constants.LOAD_CURVE_POINT_NUM_24, true);
                cityLoadDTO.setYesterdayHisLoads(todayHisLoads);
            }
        }
    }

    private void setTodayFcLoads(Map<String, LoadCityFcDO> todayFcMap, String cityId, CityLoadDTO cityLoadDTO,
        Date today, String caliberId)
        throws Exception {
        if (todayFcMap != null && !todayFcMap.isEmpty()) {
            LoadCityFcDO todayFcLoad = todayFcMap.get(cityId);
            if (todayFcLoad != null) {
                List<BigDecimal> todayFcLoads = BasePeriodUtils
                    .toList(todayFcLoad, Constants.LOAD_CURVE_POINT_NUM_24, true);
                cityLoadDTO.setTodayFcLoads(todayFcLoads);
            }
            //如果上报的数据没有
            else {
                List<LoadCityFcDO> loadCityFcDO = this.loadCityFcService
                    .listLoadCityFc(cityId, caliberId, today, today, null);
                if (!CollectionUtils.isEmpty(loadCityFcDO)) {
                    //step1：返回系统推荐算法的数据
                    List<LoadCityFcDO> collect = loadCityFcDO.stream().filter(LoadCityFcDO::getRecommend)
                        .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        List<BigDecimal> todayFcLoads = BasePeriodUtils
                            .toList(collect.get(0), Constants.LOAD_CURVE_POINT_NUM_24,
                                true);
                        cityLoadDTO.setTodayFcLoads(todayFcLoads);
                    }
                    //step2:随便返回一个预测的数据
                    else {
                        List<BigDecimal> todayFcLoads = BasePeriodUtils
                            .toList(loadCityFcDO.get(0), Constants.LOAD_CURVE_POINT_NUM_24,
                                true);
                        cityLoadDTO.setTodayFcLoads(todayFcLoads);
                    }
                }
            }
        }
    }

    private void setTodayTemperatures(Map<String, WeatherCityHisDO> todayTemperaturesHisVOMap, String cityId,
        CityLoadDTO cityLoadDTO) {
        if (todayTemperaturesHisVOMap != null && !todayTemperaturesHisVOMap.isEmpty()) {
            WeatherCityHisDO todayTemperaturesHisVO = todayTemperaturesHisVOMap.get(cityId);
            if (todayTemperaturesHisVO != null) {
                List<BigDecimal> todayTemperatures = BasePeriodUtils
                    .toList(todayTemperaturesHisVO, Constants.LOAD_CURVE_POINT_NUM_24,
                        true);
                cityLoadDTO.setTemperatures(todayTemperatures);
            }
        }
    }

    private Map<String, WeatherCityHisDO> getWeatherCityVOMap(Date date, List<String> cityIds) {
        Map<String, WeatherCityHisDO> todayWeatherMap = null;
        try {
            List<WeatherCityHisDO> todayWeathers = weatherCityHisService
                .findWeatherCityHisDOSByCityIds(cityIds, WeatherEnum.TEMPERATURE.getType(), date);
            if (!CollectionUtils.isEmpty(todayWeathers)) {
                todayWeatherMap = todayWeathers.stream()
                    .collect(Collectors.toMap(WeatherCityHisDO::getCityId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("查询历史气象异常...", e);
        }
        return todayWeatherMap;
    }

    private Map<String, LoadCityFcDO> getLoadCityFcDOMap(Date today, List<String> cityIds, String caliberId)
        throws Exception {
        Map<String, LoadCityFcDO> loadFcMap = new HashMap<>();
        List<LoadCityFcDO> loadCityFcDOList = loadCityFcService.findLoadCityFc(cityIds, today, today, caliberId);
        if (!CollectionUtils.isEmpty(loadCityFcDOList)) {
            Map<String, List<LoadCityFcDO>> cityMap = loadCityFcDOList.stream()
                .collect(Collectors.groupingBy(LoadCityFcDO::getCityId));
            for (String cityId : cityIds) {
                List<LoadCityFcDO> loadCityFcDOS = cityMap.get(cityId);
                if (!CollectionUtils.isEmpty(loadCityFcDOS)) {
                    LoadCityFcDO loadCityFcDO = loadCityFcDOS.get(0);
                    for (LoadCityFcDO fcDO : loadCityFcDOS) {
                        if (fcDO.getRecommend() == true) {
                            loadCityFcDO = fcDO;
                        }
                        if (fcDO.getReport() == true) {
                            loadCityFcDO = fcDO;
                            break;
                        }
                    }
                    loadFcMap.put(cityId,loadCityFcDO);
                }
            }
        }
        return loadFcMap;
    }

    private Map<String, LoadCityHisDO> getLoadCityHisDOMap(Date today, List<String> cityIds, String caliberId) {
        Map<String, LoadCityHisDO> hisLoadMap = null;
        try {
            List<LoadCityHisDO> todayHisLoads = findLoadCityHisDO(today, cityIds, caliberId);
            if (!CollectionUtils.isEmpty(todayHisLoads)) {
                hisLoadMap = todayHisLoads.stream()
                    .collect(Collectors.toMap(LoadCityHisDO::getCityId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("查询历史负荷异常...", e);
        }
        return hisLoadMap;
    }

    public List<LoadCityHisDO> findLoadCityHisDO(Date date, List<String> cityIds, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringIsIn, "cityId", cityIds)
            .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();

    }

    public List<LoadCityHisDO> findLoadCityHisDO(Date date, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();

    }


    private List<BigDecimal> getList(LoadCityFcDO LoadCityFcDO) throws Exception {
        if (LoadCityFcDO != null) {
            return BasePeriodUtils
                .toList(LoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        } else {
            return LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM);
        }
    }
}
