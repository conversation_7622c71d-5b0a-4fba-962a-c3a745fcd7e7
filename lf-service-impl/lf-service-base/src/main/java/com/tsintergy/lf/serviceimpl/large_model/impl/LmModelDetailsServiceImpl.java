package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaQueryWrapper;
import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.large_model.api.LmModelDetailsService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmModelDetailsDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmModelDetailsDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 13:45
 **/
@Service("lmModelDetailsService")
public class LmModelDetailsServiceImpl implements LmModelDetailsService {

    @Autowired
    LmModelDetailsDAO lmModelDetailsDAO;
    @Override
    public LmModelDetailsDO findByAlgorithm(String algorithmName) {
        return lmModelDetailsDAO.findOne(JpaWrappers.lambdaQuery(LmModelDetailsDO.class)
                .eq(LmModelDetailsDO::getAlgorithmName, algorithmName));
    }
}
