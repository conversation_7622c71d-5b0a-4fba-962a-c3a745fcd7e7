package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.large_model.api.LmStatisticsCityNear15DayFcService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmStatisticsCityNear15DayFcDO;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmStatisticsCityNear15DayFcDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmStatisticsCityNear15DayFcDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 11:18
 **/
@Service("lmStatisticsCityNear15DayFcService")
public class LmStatisticsCityNear15DayFcServiceImpl implements LmStatisticsCityNear15DayFcService {

    @Autowired
    private LmStatisticsCityNear15DayFcDAO lmStatisticsCityNear15DayFcDAO;
    @Override
    public void saveOrUpdateBatch(List<LmStatisticsCityNear15DayFcDO> statisticsCityDayFcDOList) {
        lmStatisticsCityNear15DayFcDAO.saveOrUpdateBatchByTemplate(statisticsCityDayFcDOList);
    }

    @Override
    public List<LmStatisticsCityNear15DayFcDO> find(String cityId, String caliberId, Date date, Integer type) {
        return lmStatisticsCityNear15DayFcDAO.findAll(JpaWrappers.<LmStatisticsCityNear15DayFcDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), LmStatisticsCityNear15DayFcDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LmStatisticsCityNear15DayFcDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(type), LmStatisticsCityNear15DayFcDO::getType, type)
                .eq(LmStatisticsCityNear15DayFcDO::getDate, date));
    }
}
