package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsintergy.lf.core.enums.SettingAccuracyAssessEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAlgorithmService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.BatchDataFilterService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.*;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyAssessDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyCompositeDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcBatchDAO;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("accuracyAlgorithmService")
public class AccuracyAlgorithmServiceImpl implements AccuracyAlgorithmService {

    @Autowired
    AlgorithmService algorithmService;

    @Resource
    private AccuracyCompositeDAO accuracyCompositeDAO;

    @Autowired
    BatchDataFilterService batchDataFilterService;

    @Autowired
    AccuracyAssessDAO accuracyAssessDAO;

    @Autowired
    StatisticsCityDayFcBatchDAO statisticsCityDayFcBatchDAO;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    SettingAssessService settingAssessService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    private static final String COMP_ACCURACY = "综合准确率（%）";

    private static final String COMP_OTHER_ACCURACY = "综合准确率";

    private static final String POINT_ACCURACY = "96点预测准确率（%）";

    private static final String BATCH_ID = "1";

    private static final String MOON_LOAD = "日午间低谷";

    private static final String MAX_LOAD_NAME = "日最大负荷";

    private static final String EVENING_LOAD = "日夜间低谷";

    private static final String BG_LOAD = "日保供";
    private static final String BG_TIME_LOAD = "日保供时段";

    private static final String HIS_LOAD = "实际负荷";

    private static final String TEM = "气温";

    private static final String EVENING_PEEK = "晚高峰";

    private static final List<Integer> DAYS_LIST = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);

    @Override
    public List<AccuracyAlgorithmDataDTO> getAlgorithmListData(String cityId, Date startDate, Date endDate, List<String> algorithmIds, String caliberId)
            throws Exception {
        List<AccuracyAlgorithmDataDTO> result = new ArrayList<>();
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        Boolean type = true;
        if (startDate.compareTo(endDate) == 0) {
            type = false;
        }
        Map<String, String> algorithmNameMap = algorithmService.getAllAlgorithms().stream().collect(
                Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        // 查询上午第一批次
        List<SettingAssessDO> assessByDataList = settingAssessService.findAssessByDataList(
                DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0],
                DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[1], caliberId, null);
        Map<String, List<SettingAssessDO>> collectName = new HashMap<>();
        if (!CollectionUtils.isEmpty(assessByDataList)) {
            collectName = assessByDataList.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
        }
        // 查询过滤综合准确率
        AccuracyAlgorithmDataDTO accuracyAlgorithmDataDTO = new AccuracyAlgorithmDataDTO();
        accuracyAlgorithmDataDTO.setName(COMP_ACCURACY);
        List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyCompositeDAO.selectListByAlgorithmId(cityId, caliberId, algorithmIds, startDate, endDate, null);
        Map<Integer, List<AccuracyCompositeDO>> integerListMap = batchDataFilterService.filterCompositeByListBatchId(
                accuracyCompositeDOS, BATCH_ID, DAYS_LIST);
        List<AccuracyValueDataDTO> accuracyValueDataDTOList = new ArrayList<>();
        for (String id : algorithmIds) {
            AccuracyValueDataDTO accuracyValueDataDTO = new AccuracyValueDataDTO();
            accuracyValueDataDTO.setAlgorithmName(algorithmNameMap.get(id));
            List<AccuracyAlgorithmValueDataDTO> accuracyAlgorithmValueDataDTOS = new ArrayList<>();
            for (int i = 1; i < 11; i++) {
                AccuracyAlgorithmValueDataDTO accuracyAlgorithmValueDataDTO = new AccuracyAlgorithmValueDataDTO();
                List<AccuracyCompositeDO> accuracyCompositeDOS1 = integerListMap.get(i);
                if (!CollectionUtils.isEmpty(accuracyCompositeDOS1)) {
                    Map<String, List<AccuracyCompositeDO>> collect = accuracyCompositeDOS1.stream()
                            .collect(Collectors.groupingBy(t -> t.getAlgorithmId()));
                    List<AccuracyCompositeDO> accuracyCompositeDOS2 = collect.get(id);
                    if (!CollectionUtils.isEmpty(accuracyCompositeDOS2)) {
                        BigDecimal reduce = accuracyCompositeDOS2.stream().filter(t -> t.getAccuracy() != null)
                                .map(AccuracyCompositeDO::getAccuracy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal divide = reduce.divide(BigDecimal.valueOf(accuracyCompositeDOS2.size()), 4,
                                RoundingMode.HALF_UP);
                        Date date = DateUtils.addDays(startDate, -i);
                        if (type) {
                            accuracyAlgorithmValueDataDTO.setDateStr("D-" + i);
                        } else {
                            accuracyAlgorithmValueDataDTO.setDateStr("D-"  + i + "（" + DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd") + "）");
                        }
                        accuracyAlgorithmValueDataDTO.setValue(divide.multiply(BigDecimal.valueOf(100)));
                    }
                }
                accuracyAlgorithmValueDataDTOS.add(accuracyAlgorithmValueDataDTO);
            }
            accuracyValueDataDTO.setAccuracyAlgorithmValueDataDTOS(accuracyAlgorithmValueDataDTOS);
            accuracyValueDataDTOList.add(accuracyValueDataDTO);
        }
        accuracyAlgorithmDataDTO.setAccuracyValueDataDTOList(accuracyValueDataDTOList);
        result.add(accuracyAlgorithmDataDTO);

        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        Map<String, List<LoadCityHisDO>> yyyyMMdd1 = new HashMap<>();
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            yyyyMMdd1 = loadCityHisDOS.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
        }
        List<LoadCityFcBatchDO> loadFcByDateAlgorithmId = loadCityFcBatchService.findByAllAlgorithmCondition(cityId,
                startDate, endDate, caliberId, algorithmIds);
        Map<Integer, List<LoadCityFcBatchDO>> integerListMap2 = batchDataFilterService.filterCityBatchByListBatchId(
                loadFcByDateAlgorithmId, BATCH_ID, DAYS_LIST);
        // 保供时段计算
        List<AccuracyValueDataDTO> accuracyValueDataDTOList2 = new ArrayList<>();
        AccuracyAlgorithmDataDTO accuracyAlgorithmDataDTO1 = new AccuracyAlgorithmDataDTO();
        accuracyAlgorithmDataDTO1.setName(BG_LOAD);
        for (String id : algorithmIds) {
            AccuracyValueDataDTO accuracyValueDataDTO = new AccuracyValueDataDTO();
            accuracyValueDataDTO.setAlgorithmName(algorithmNameMap.get(id));
            List<AccuracyAlgorithmValueDataDTO> accuracyAlgorithmValueDataDTOS = new ArrayList<>();
            for (int i = 1; i < 11; i++) {
                List<LoadCityFcBatchDO> loadCityFcDOS1 = integerListMap2.get(i);
                Map<String, List<LoadCityFcBatchDO>> collect1 = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityFcDOS1)) {
                    collect1 = loadCityFcDOS1.stream()
                            .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd") + "-" + t.getAlgorithmId()));
                }
                AccuracyAlgorithmValueDataDTO accuracyAlgorithmValueDataDTO = new AccuracyAlgorithmValueDataDTO();
                List<AccuracyAssessDO> accuracyAssessDOS1 = new ArrayList<>();
                List<SettingAssessDO> setAssessDO = collectName.get(BG_LOAD);
                if (CollectionUtils.isEmpty(setAssessDO)) {
                    setAssessDO = collectName.get(BG_TIME_LOAD);
                }
                if (!CollectionUtils.isEmpty(setAssessDO)) {
                    SettingAssessDO settingAssessDO = setAssessDO.get(0);
                    for (Date date : listBetweenDay) {
                        String dateToStrFORMAT = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
                        List<LoadCityFcBatchDO> loadCityFcBatchDOS = collect1.get(dateToStrFORMAT + "-" + id);
                        List<LoadCityHisDO> loadCityHisDOS1 = yyyyMMdd1.get(DateUtil.getDateToStrFORMAT(date, "yyyyMMdd"));
                        if (settingAssessDO.getStartTime() == null || "".equals(settingAssessDO.getStartTime())) {
                            if (!CollectionUtils.isEmpty(loadCityFcBatchDOS) && !CollectionUtils.isEmpty(loadCityHisDOS1)) {
                                List<BigDecimal> bigDecimals1 = loadCityFcBatchDOS.get(0).getloadList();
                                List<BigDecimal> bigDecimals2 = loadCityHisDOS1.get(0).getloadList();
                                BigDecimal bigDecimal = BigDecimalFunctions.listMin(bigDecimals1);
                                BigDecimal bigDecimal1 = BigDecimalFunctions.listMin(bigDecimals2);
                                BigDecimal divide = BigDecimal.ONE.subtract(bigDecimal.subtract(bigDecimal1).abs()
                                        .divide(bigDecimal1, 4, RoundingMode.HALF_UP));
                                AccuracyAssessDO accuracyAssessDO = new AccuracyAssessDO();
                                accuracyAssessDO.setAccuracy(divide);
                                accuracyAssessDO.setAlgorithmId(id);
                                accuracyAssessDO.setAssessName(BG_LOAD);
                                accuracyAssessDOS1.add(accuracyAssessDO);
                            }
                        } else {
                            if (!CollectionUtils.isEmpty(loadCityFcBatchDOS) && !CollectionUtils.isEmpty(loadCityHisDOS1)) {
                                BigDecimal bigDecimals = countLoadDataDTO(
                                        loadCityFcBatchDOS.get(0).getloadList(), loadCityHisDOS1.get(0).getloadList(),
                                        settingAssessDO.getStartTime(), settingAssessDO.getEndTime());
                                AccuracyAssessDO accuracyAssessDO = new AccuracyAssessDO();
                                accuracyAssessDO.setAlgorithmId(id);
                                accuracyAssessDO.setAssessName(BG_LOAD);
                                accuracyAssessDO.setAccuracy(bigDecimals);
                                accuracyAssessDOS1.add(accuracyAssessDO);
                            }
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(accuracyAssessDOS1)) {
                    Map<String, List<AccuracyAssessDO>> collect = accuracyAssessDOS1.stream()
                            .collect(Collectors.groupingBy(t->t.getAlgorithmId() + "-" + t.getAssessName()));
                    String key = id + "-" + BG_LOAD;
                    List<AccuracyAssessDO> accuracyAssessDOS2 = collect.get(key);
                    if (CollectionUtils.isEmpty(accuracyAssessDOS2)) {
                        accuracyAssessDOS2 = collect.get(id + "-" + BG_TIME_LOAD);
                    }
                    if (!CollectionUtils.isEmpty(accuracyAssessDOS2)) {
                        BigDecimal reduce = accuracyAssessDOS2.stream().filter(t -> t.getAccuracy() != null)
                                .map(AccuracyAssessDO::getAccuracy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal divide = reduce.divide(BigDecimal.valueOf(accuracyAssessDOS2.size()), 4,
                                RoundingMode.HALF_UP);
                        Date date = DateUtils.addDays(startDate, -i);
                        if (type) {
                            accuracyAlgorithmValueDataDTO.setDateStr("D-" + i);
                        } else {
                            accuracyAlgorithmValueDataDTO.setDateStr("D-"  + i + "（" + DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd") + "）");
                        }
                        accuracyAlgorithmValueDataDTO.setValue(divide.multiply(BigDecimal.valueOf(100)));
                    }
                }
                accuracyAlgorithmValueDataDTOS.add(accuracyAlgorithmValueDataDTO);
            }
            accuracyValueDataDTO.setAccuracyAlgorithmValueDataDTOS(accuracyAlgorithmValueDataDTOS);
            accuracyValueDataDTOList2.add(accuracyValueDataDTO);
        }
        accuracyAlgorithmDataDTO1.setAccuracyValueDataDTOList(accuracyValueDataDTOList2);
        result.add(accuracyAlgorithmDataDTO1);
        // 查询过滤考核点准确率
        List<AccuracyAssessDO> accuracyAssessDOS = accuracyAssessDAO.selectListByAlgorithmIds(cityId, caliberId,
                algorithmIds, startDate, endDate);
        Map<Integer, List<AccuracyAssessDO>> integerListMap1 = batchDataFilterService.filterAssessByLisyBatchId(
                accuracyAssessDOS, BATCH_ID, DAYS_LIST);
        for (SettingAccuracyAssessEnum value : SettingAccuracyAssessEnum.values()) {
            if (value.getDataName().equals(BG_LOAD) || value.getDataName().equals(BG_TIME_LOAD)) {
                continue;
            }
            AccuracyAlgorithmDataDTO accuracyAlgorithmDataDTO2 = new AccuracyAlgorithmDataDTO();
            accuracyAlgorithmDataDTO2.setName(value.getDataName());
            List<AccuracyValueDataDTO> accuracyValueDataDTOList1 = new ArrayList<>();
            for (String id : algorithmIds) {
                AccuracyValueDataDTO accuracyValueDataDTO = new AccuracyValueDataDTO();
                accuracyValueDataDTO.setAlgorithmName(algorithmNameMap.get(id));
                List<AccuracyAlgorithmValueDataDTO> accuracyAlgorithmValueDataDTOS = new ArrayList<>();
                for (int i = 1; i < 11; i++) {
                    AccuracyAlgorithmValueDataDTO accuracyAlgorithmValueDataDTO = new AccuracyAlgorithmValueDataDTO();
                    List<AccuracyAssessDO> accuracyAssessDOS1 = integerListMap1.get(i);
                    Map<String, List<AccuracyAssessDO>> collectAssess = new HashMap<>();
                    if (!CollectionUtils.isEmpty(accuracyAssessDOS1)) {
                        collectAssess = accuracyAssessDOS1.stream()
                                .collect(Collectors.groupingBy(t -> t.getAssessName()));
                    }
                    List<AccuracyAssessDO> accuracyAssessDOS2 = collectAssess.get(value.getDataName());
                    if (CollectionUtils.isEmpty(accuracyAssessDOS2) && value.getDataName().equals(SettingAccuracyAssessEnum.MAX_LOAD.getDataName())) {
                        accuracyAssessDOS2 = collectAssess.get(MAX_LOAD_NAME);
                    }
                    if (!CollectionUtils.isEmpty(accuracyAssessDOS2)) {
                        Map<String, List<AccuracyAssessDO>> collect = accuracyAssessDOS2.stream()
                                .collect(Collectors.groupingBy(t -> t.getAlgorithmId()));
                        List<AccuracyAssessDO> statisticsCityDayFcBatchDOS2 = collect.get(id);
                        if (!CollectionUtils.isEmpty(statisticsCityDayFcBatchDOS2)) {
                            BigDecimal reduce = statisticsCityDayFcBatchDOS2.stream().filter(t -> t.getAccuracy() != null)
                                    .map(AccuracyAssessDO::getAccuracy)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal divide = reduce.divide(BigDecimal.valueOf(statisticsCityDayFcBatchDOS2.size()), 4,
                                    RoundingMode.HALF_UP);
                            Date date = DateUtils.addDays(startDate, -i);
                            if (type) {
                                accuracyAlgorithmValueDataDTO.setDateStr("D-" + i);
                            } else {
                                accuracyAlgorithmValueDataDTO.setDateStr("D-"  + i + "（" + DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd") + "）");
                            }
                            accuracyAlgorithmValueDataDTO.setValue(divide.multiply(BigDecimal.valueOf(100)));
                        }
                    }
                    accuracyAlgorithmValueDataDTOS.add(accuracyAlgorithmValueDataDTO);
                }
                accuracyValueDataDTO.setAccuracyAlgorithmValueDataDTOS(accuracyAlgorithmValueDataDTOS);
                accuracyValueDataDTOList1.add(accuracyValueDataDTO);
            }
            accuracyAlgorithmDataDTO2.setAccuracyValueDataDTOList(accuracyValueDataDTOList1);
            result.add(accuracyAlgorithmDataDTO2);
        }
        // 查询96点预测准确率
        AccuracyAlgorithmDataDTO accuracyAlgorithmDataDTO2 = new AccuracyAlgorithmDataDTO();
        accuracyAlgorithmDataDTO2.setName(POINT_ACCURACY);
        List<AccuracyValueDataDTO> accuracyValueDataDTOList1 = new ArrayList<>();
        List<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchDOS =
                statisticsCityDayFcBatchDAO.getStatisticsCityDayFcBatchDOs(cityId, caliberId, null, null, startDate, endDate);
        Map<Integer, List<StatisticsCityDayFcBatchDO>> integerListMap3 = batchDataFilterService.filterStatisticsByListBatchId(
                statisticsCityDayFcBatchDOS, BATCH_ID, DAYS_LIST);
        for (String id : algorithmIds) {
            AccuracyValueDataDTO accuracyValueDataDTO = new AccuracyValueDataDTO();
            accuracyValueDataDTO.setAlgorithmName(algorithmNameMap.get(id));
            List<AccuracyAlgorithmValueDataDTO> accuracyAlgorithmValueDataDTOS = new ArrayList<>();
            for (int i = 1; i < 11; i++) {
                AccuracyAlgorithmValueDataDTO accuracyAlgorithmValueDataDTO = new AccuracyAlgorithmValueDataDTO();
                List<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchDOS1 = integerListMap3.get(i);
                if (!CollectionUtils.isEmpty(statisticsCityDayFcBatchDOS1)) {
                    Map<String, List<StatisticsCityDayFcBatchDO>> collect = statisticsCityDayFcBatchDOS1.stream()
                            .collect(Collectors.groupingBy(t -> t.getAlgorithmId()));
                    List<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchDOS2 = collect.get(id);
                    if (!CollectionUtils.isEmpty(statisticsCityDayFcBatchDOS2)) {
                        BigDecimal reduce = statisticsCityDayFcBatchDOS2.stream().filter(t -> t.getAccuracy() != null)
                                .map(StatisticsCityDayFcBatchDO::getAccuracy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal divide = reduce.divide(BigDecimal.valueOf(statisticsCityDayFcBatchDOS2.size()), 4,
                                RoundingMode.HALF_UP);
                        Date date = DateUtils.addDays(startDate, -i);
                        if (type) {
                            accuracyAlgorithmValueDataDTO.setDateStr("D-" + i);
                        } else {
                            accuracyAlgorithmValueDataDTO.setDateStr("D-"  + i + "（" + DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd") + "）");
                        }
                        accuracyAlgorithmValueDataDTO.setValue(divide.multiply(BigDecimal.valueOf(100)));
                    }
                }
                accuracyAlgorithmValueDataDTOS.add(accuracyAlgorithmValueDataDTO);
            }
            accuracyValueDataDTO.setAccuracyAlgorithmValueDataDTOS(accuracyAlgorithmValueDataDTOS);
            accuracyValueDataDTOList1.add(accuracyValueDataDTO);
        }
        accuracyAlgorithmDataDTO2.setAccuracyValueDataDTOList(accuracyValueDataDTOList1);
        result.add(accuracyAlgorithmDataDTO2);
        return result;
    }

    @Override
    public List<AccuracyDetailValueDataDTO> getAlgorithmListDetailData(String cityId, Date startDate, Date endDate,
                                                                       List<String> algorithmIds, String caliberId, String days) throws Exception {
        List<AccuracyDetailValueDataDTO> result = new ArrayList<>();
        Map<String, String> algorithmNameMap = algorithmService.getAllAlgorithms().stream().collect(
                Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        // 查询上午第一批次
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<SettingAssessDO> assessByDataList = settingAssessService.findAssessByDataList(
                DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0],
                DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[1], caliberId, null);
        Map<String, List<SettingAssessDO>> collectName = new HashMap<>();
        if (!CollectionUtils.isEmpty(assessByDataList)) {
            collectName = assessByDataList.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
        }
        // 日综合准确率
        List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyCompositeDAO.selectListByAlgorithmId(cityId, caliberId, algorithmIds, startDate, endDate, null);
        List<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchDOS =
                statisticsCityDayFcBatchDAO.getStatisticsCityDayFcBatchDOs(cityId, caliberId, null, null, startDate, endDate);
        // 考核点实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        // 考核点预测负荷
        List<LoadCityFcBatchDO> loadFcByDateAlgorithmId = loadCityFcBatchService.findByAllAlgorithmCondition(cityId,
                startDate, endDate, caliberId, algorithmIds);
        Map<Integer, List<LoadCityFcBatchDO>> integerListMap = batchDataFilterService.filterCityBatchByListBatchId(
                loadFcByDateAlgorithmId, BATCH_ID, DAYS_LIST);
        Map<String, List<LoadCityHisDO>> yyyyMMdd1 = new HashMap<>();
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            yyyyMMdd1 = loadCityHisDOS.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
        }
        for (String id : algorithmIds) {
            for (Date date : listBetweenDay) {
                AccuracyDetailValueDataDTO accuracyDetailValueDataDTO = new AccuracyDetailValueDataDTO();
                accuracyDetailValueDataDTO.setAlgorithmName(algorithmNameMap.get(id));
                accuracyDetailValueDataDTO.setDateStr(DateUtil.getDateToStrFORMAT(DateUtils.addDays(date, - Integer.parseInt(days)), "yyyy-MM-dd"));
                String key = id + "-" + DateUtil.getDateToStrFORMAT(date, "yyyyMMdd");
                List<AccuracyCompositeDO> accuracyCompositeDOS1 = batchDataFilterService.filterCompositeByBatchId(
                        accuracyCompositeDOS, BATCH_ID, Integer.parseInt(days));
                if (!CollectionUtils.isEmpty(accuracyCompositeDOS1)) {
                    Map<String, List<AccuracyCompositeDO>> collect = accuracyCompositeDOS1.stream()
                            .collect(Collectors.groupingBy(t -> t.getAlgorithmId() + "-" + DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
                    List<AccuracyCompositeDO> accuracyCompositeDOS2 = collect.get(key);
                    if (!CollectionUtils.isEmpty(accuracyCompositeDOS2)) {
                        BigDecimal accuracy = accuracyCompositeDOS2.get(0).getAccuracy();
                        accuracyDetailValueDataDTO.setDayAccuracy(accuracy);
                    }
                }
                List<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchDOS1 = batchDataFilterService.filterStatisticsByBatchId(
                        statisticsCityDayFcBatchDOS, BATCH_ID, Integer.parseInt(days));
                if (!CollectionUtils.isEmpty(statisticsCityDayFcBatchDOS1)) {
                    Map<String, List<StatisticsCityDayFcBatchDO>> yyyyMMdd = statisticsCityDayFcBatchDOS1.stream()
                            .collect(Collectors.groupingBy(
                                    t -> t.getAlgorithmId() + "-" + DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
                    List<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchDOS2 = yyyyMMdd.get(key);
                    if (!CollectionUtils.isEmpty(statisticsCityDayFcBatchDOS2)) {
                        BigDecimal accuracy = statisticsCityDayFcBatchDOS2.get(0).getAccuracy();
                        accuracyDetailValueDataDTO.setPointsAccuracy(accuracy);
                    }
                }
                List<LoadCityFcBatchDO> loadCityFcDOS1 = integerListMap.get(Integer.parseInt(days));
                Map<String, List<LoadCityFcBatchDO>> collect = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityFcDOS1)) {
                    collect = loadCityFcDOS1.stream()
                            .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd") + "-" + t.getAlgorithmId()));
                }
                List<SettingAssessDO> lMin = collectName.get(MOON_LOAD);
                List<SettingAssessDO> eMin = collectName.get(EVENING_LOAD);
                List<LoadCityFcBatchDO> loadCityFcDOS = collect.get(DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd") + "-" + id);
                if (!CollectionUtils.isEmpty(loadCityFcDOS)) {
                    List<BigDecimal> bigDecimals = loadCityFcDOS.get(0).getloadList();
                    BigDecimal bigDecimal = BigDecimalFunctions.listMax(bigDecimals);
                    BigDecimal bigDecimal1 = BigDecimalFunctions.listMin(bigDecimals);
                    accuracyDetailValueDataDTO.setFcMaxLoad(bigDecimal);
                    // 午间最小负荷
                    if (!CollectionUtils.isEmpty(lMin)) {
                        SettingAssessDO settingAssessDO = lMin.get(0);
                        if (settingAssessDO.getStartTime() == null || "".equals(settingAssessDO.getStartTime())) {
                            accuracyDetailValueDataDTO.setFcMinimumLoad(bigDecimal1);
                        } else {
                            BigDecimal bigDecimal2 = countLoad(bigDecimals, settingAssessDO.getStartTime(),
                                    settingAssessDO.getEndTime());
                            accuracyDetailValueDataDTO.setFcMinimumLoad(bigDecimal2);
                        }
                    }
                    // 夜间最小负荷
                    if (!CollectionUtils.isEmpty(eMin)) {
                        SettingAssessDO settingAssessDO = eMin.get(0);
                        if (settingAssessDO.getStartTime() == null || "".equals(settingAssessDO.getStartTime())) {
                            accuracyDetailValueDataDTO.setFcMinimumNightLoad(bigDecimal1);
                        } else {
                            BigDecimal bigDecimal2 = countLoad(bigDecimals, settingAssessDO.getStartTime(),
                                    settingAssessDO.getEndTime());
                            accuracyDetailValueDataDTO.setFcMinimumNightLoad(bigDecimal2);
                        }
                    }
                }
                List<LoadCityHisDO> loadCityHisDOS1 = yyyyMMdd1.get(DateUtil.getDateToStrFORMAT(date, "yyyyMMdd"));
                if (!CollectionUtils.isEmpty(loadCityHisDOS1)) {
                    List<BigDecimal> bigDecimals = loadCityHisDOS1.get(0).getloadList();
                    BigDecimal bigDecimal = BigDecimalFunctions.listMax(bigDecimals);
                    BigDecimal bigDecimal1 = BigDecimalFunctions.listMin(bigDecimals);
                    accuracyDetailValueDataDTO.setHisMaxLoad(bigDecimal);
                    // 午间最小负荷
                    if (!CollectionUtils.isEmpty(lMin)) {
                        SettingAssessDO settingAssessDO = lMin.get(0);
                        if (settingAssessDO.getStartTime() == null || "".equals(settingAssessDO.getStartTime())) {
                            accuracyDetailValueDataDTO.setHisMinimumLoad(bigDecimal1);
                        } else {
                            BigDecimal bigDecimal2 = countLoad(bigDecimals, settingAssessDO.getStartTime(),
                                    settingAssessDO.getEndTime());
                            accuracyDetailValueDataDTO.setHisMinimumLoad(bigDecimal2);
                        }
                    }
                    // 夜间最小负荷
                    if (!CollectionUtils.isEmpty(eMin)) {
                        SettingAssessDO settingAssessDO = eMin.get(0);
                        if (settingAssessDO.getStartTime() == null || "".equals(settingAssessDO.getStartTime())) {
                            accuracyDetailValueDataDTO.setHisMinimumNightLoad(bigDecimal1);
                        } else {
                            BigDecimal bigDecimal2 = countLoad(bigDecimals, settingAssessDO.getStartTime(),
                                    settingAssessDO.getEndTime());
                            accuracyDetailValueDataDTO.setHisMinimumNightLoad(bigDecimal2);
                        }
                    }
                }
                if (accuracyDetailValueDataDTO.getFcMaxLoad() != null && accuracyDetailValueDataDTO.getHisMaxLoad() != null) {
                    BigDecimal subtract = accuracyDetailValueDataDTO.getFcMaxLoad()
                            .subtract(accuracyDetailValueDataDTO.getHisMaxLoad()).abs();
                    if (accuracyDetailValueDataDTO.getHisMaxLoad().compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal divide = subtract.divide(accuracyDetailValueDataDTO.getHisMaxLoad(), 4, RoundingMode.HALF_UP);
                        BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                        accuracyDetailValueDataDTO.setMaxLoadAccuracy(bigDecimal);
                    }
                }
                if (accuracyDetailValueDataDTO.getFcMinimumLoad() != null && accuracyDetailValueDataDTO.getHisMinimumLoad() != null) {
                    BigDecimal subtract = accuracyDetailValueDataDTO.getFcMinimumLoad()
                            .subtract(accuracyDetailValueDataDTO.getHisMinimumLoad()).abs();
                    if (accuracyDetailValueDataDTO.getHisMinimumLoad().compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal divide = subtract.divide(accuracyDetailValueDataDTO.getHisMinimumLoad(), 4, RoundingMode.HALF_UP);
                        BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                        accuracyDetailValueDataDTO.setFcMinimumLoadAccuracy(bigDecimal);
                    }
                }
                if (accuracyDetailValueDataDTO.getFcMinimumNightLoad() != null && accuracyDetailValueDataDTO.getHisMinimumNightLoad() != null) {
                    BigDecimal subtract = accuracyDetailValueDataDTO.getFcMinimumNightLoad()
                            .subtract(accuracyDetailValueDataDTO.getHisMinimumNightLoad()).abs();
                    if (accuracyDetailValueDataDTO.getHisMinimumNightLoad().compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal divide = subtract.divide(accuracyDetailValueDataDTO.getHisMinimumNightLoad(), 4, RoundingMode.HALF_UP);
                        BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                        accuracyDetailValueDataDTO.setMinimumNightLoadAccuracy(bigDecimal);
                    }
                }
                List<SettingAssessDO> bgMin = collectName.get(BG_LOAD);
                if (CollectionUtils.isEmpty(bgMin)) {
                    bgMin = collectName.get(BG_TIME_LOAD);
                }
                if (!CollectionUtils.isEmpty(bgMin)) {
                    if (!CollectionUtils.isEmpty(loadCityFcDOS) && !CollectionUtils.isEmpty(loadCityHisDOS1)  ) {
                        countLoadData(loadCityFcDOS.get(0).getloadList(), loadCityHisDOS1.get(0).getloadList(),
                                bgMin.get(0).getStartTime(), bgMin.get(0).getEndTime(), accuracyDetailValueDataDTO);
                    }
                }
                result.add(accuracyDetailValueDataDTO);
            }
        }
        return result;
    }

    @Override
    public List<AccuracyAlgorithmCurveDataDTO> getAlgorithmListDetailCurveData(String cityId, Date startDate,
                                                                               Date endDate, List<String> algorithmIds, String caliberId, String days) throws Exception {
        List<AccuracyAlgorithmCurveDataDTO> result = new ArrayList<>();
        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityId, 2, startDate,
                endDate);
        Map<String, List<WeatherCityHisDO>> yyyyMMdd = new HashMap<>();
        if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
            yyyyMMdd = weatherCityHisDOs.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
        }
        Map<String, String> algorithmNameMap = algorithmService.getAllAlgorithms().stream().collect(
                Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        // 考核点实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        // 考核点预测负荷
        List<LoadCityFcBatchDO> loadFcByDateAlgorithmId = loadCityFcBatchService.findByAllAlgorithmCondition(cityId,
                startDate, endDate, caliberId, algorithmIds);
        Map<String, List<LoadCityHisDO>> yyyyMMdd1 = new HashMap<>();
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            yyyyMMdd1 = loadCityHisDOS.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
        }
        Map<Integer, List<LoadCityFcBatchDO>> integerListMap = batchDataFilterService.filterCityBatchByListBatchId(
                loadFcByDateAlgorithmId, BATCH_ID, DAYS_LIST);
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        for (String id : algorithmIds) {
            AccuracyAlgorithmCurveDataDTO accuracyAlgorithmCurveDataDTO = new AccuracyAlgorithmCurveDataDTO();
            accuracyAlgorithmCurveDataDTO.setName(algorithmNameMap.get(id));
            List<AccuracyAlgorithmCurveValueDTO> accuracyAlgorithmCurveValueDTOList = new ArrayList<>();
            for (Date date : listBetweenDay) {
                AccuracyAlgorithmCurveValueDTO accuracyAlgorithmCurveValueDTO = new AccuracyAlgorithmCurveValueDTO();
                accuracyAlgorithmCurveValueDTO.setDateStr(DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd"));
                String key = id + "-" + DateUtil.getDateToStrFORMAT(date, "yyyyMMdd");
                List<LoadCityFcBatchDO> loadCityFcDOS1 = integerListMap.get(Integer.parseInt(days));
                Map<String, List<LoadCityFcBatchDO>> collect = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityFcDOS1)) {
                    collect = loadCityFcDOS1.stream()
                            .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd") + "-" + t.getAlgorithmId()));
                }
                List<LoadCityFcBatchDO> loadCityFcDOS = collect.get(DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd") + "-" + id);
                if (!CollectionUtils.isEmpty(loadCityFcDOS)) {
                    List<BigDecimal> bigDecimals = loadCityFcDOS.get(0).getloadList();
                    accuracyAlgorithmCurveValueDTO.setBigDecimalList(bigDecimals);
                }
                accuracyAlgorithmCurveValueDTOList.add(accuracyAlgorithmCurveValueDTO);
            }
            accuracyAlgorithmCurveDataDTO.setAccuracyAlgorithmCurveValueDTOList(accuracyAlgorithmCurveValueDTOList);
            result.add(accuracyAlgorithmCurveDataDTO);
        }
        AccuracyAlgorithmCurveDataDTO accuracyAlgorithmCurveDataDTO = new AccuracyAlgorithmCurveDataDTO();
        accuracyAlgorithmCurveDataDTO.setName(HIS_LOAD);
        List<AccuracyAlgorithmCurveValueDTO> accuracyAlgorithmCurveValueDTOList = new ArrayList<>();
        for (Date date : listBetweenDay) {
            AccuracyAlgorithmCurveValueDTO accuracyAlgorithmCurveValueDTO = new AccuracyAlgorithmCurveValueDTO();
            accuracyAlgorithmCurveValueDTO.setDateStr(DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd"));
            String key = DateUtil.getDateToStrFORMAT(date, "yyyyMMdd");
            List<LoadCityHisDO> loadCityHisDOS1 = yyyyMMdd1.get(key);
            if (!CollectionUtils.isEmpty(loadCityHisDOS1)) {
                List<BigDecimal> bigDecimals = loadCityHisDOS1.get(0).getloadList();
                accuracyAlgorithmCurveValueDTO.setBigDecimalList(bigDecimals);
            }
            accuracyAlgorithmCurveValueDTOList.add(accuracyAlgorithmCurveValueDTO);
        }
        accuracyAlgorithmCurveDataDTO.setAccuracyAlgorithmCurveValueDTOList(accuracyAlgorithmCurveValueDTOList);
        result.add(accuracyAlgorithmCurveDataDTO);
        AccuracyAlgorithmCurveDataDTO accuracyAlgorithmCurveDataDTO1 = new AccuracyAlgorithmCurveDataDTO();
        accuracyAlgorithmCurveDataDTO1.setName(TEM);
        List<AccuracyAlgorithmCurveValueDTO> accuracyAlgorithmCurveValueDTOList1 = new ArrayList<>();
        for (Date date : listBetweenDay) {
            AccuracyAlgorithmCurveValueDTO accuracyAlgorithmCurveValueDTO = new AccuracyAlgorithmCurveValueDTO();
            accuracyAlgorithmCurveValueDTO.setDateStr(DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd"));
            String key = DateUtil.getDateToStrFORMAT(date, "yyyyMMdd");
            List<WeatherCityHisDO> weatherCityHisDOS = yyyyMMdd.get(key);
            if (!CollectionUtils.isEmpty(weatherCityHisDOS)) {
                List<BigDecimal> bigDecimals = weatherCityHisDOS.get(0).getWeatherList();
                accuracyAlgorithmCurveValueDTO.setBigDecimalList(bigDecimals);
            }
            accuracyAlgorithmCurveValueDTOList1.add(accuracyAlgorithmCurveValueDTO);
        }
        accuracyAlgorithmCurveDataDTO1.setAccuracyAlgorithmCurveValueDTOList(accuracyAlgorithmCurveValueDTOList1);
        result.add(accuracyAlgorithmCurveDataDTO1);
        return result;
    }

    /*public BigDecimal countLoadDataFeatureDTO(List<BigDecimal> fcBigDecimals, List<BigDecimal> hisBigDecimals, String startTime,
                                              String endTime) {
        BigDecimal bigLoad = BigDecimal.ZERO;
        int a = 0;
        List<AccuracyDetailDataDTO> accuracyDetailDataDTOList = new ArrayList<>();
        int timePoint = DateUtil.getTimePoint(startTime.replaceAll(":", ""), 96);
        int timePoint1 = DateUtil.getTimePoint(endTime.replaceAll(":", ""), 96);
        AccuracyDetailDataDTO accuracyDetailDataDTO = new AccuracyDetailDataDTO();
        accuracyDetailDataDTO.setName(EVENING_PEEK + startTime);
        accuracyDetailDataDTO.setFcLoad(fcBigDecimals.get(timePoint - 1));
        accuracyDetailDataDTO.setHisLoad(hisBigDecimals.get(timePoint - 1));
        if (accuracyDetailDataDTO.getFcLoad() != null && accuracyDetailDataDTO.getHisLoad()!= null) {
            BigDecimal subtract = accuracyDetailDataDTO.getFcLoad()
                    .subtract(accuracyDetailDataDTO.getHisLoad()).abs();
            if (accuracyDetailDataDTO.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal divide = subtract.divide(accuracyDetailDataDTO.getHisLoad(), 4, RoundingMode.HALF_UP);
                BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                accuracyDetailDataDTO.setAccuracy(bigDecimal);
                bigLoad = bigLoad.add(bigDecimal);
                a = a + 1;
            }
        }
        accuracyDetailDataDTOList.add(accuracyDetailDataDTO);
        for (int i = 1; i < timePoint1 - timePoint; i++) {
            BigDecimal bigDecimal1 = fcBigDecimals.get(timePoint + i - 1);
            BigDecimal bigDecimal2 = hisBigDecimals.get(timePoint + i - 1);
            String hHmmTime = DateUtil.getHHmmTime("0015", 2, timePoint + i);
            AccuracyDetailDataDTO accuracyDetailDataDTO1 = new AccuracyDetailDataDTO();
            accuracyDetailDataDTO1.setName(EVENING_PEEK + hHmmTime);
            accuracyDetailDataDTO1.setFcLoad(bigDecimal1);
            accuracyDetailDataDTO1.setHisLoad(bigDecimal2);
            if (accuracyDetailDataDTO1.getFcLoad() != null && accuracyDetailDataDTO1.getHisLoad()!= null) {
                BigDecimal subtract = accuracyDetailDataDTO1.getFcLoad()
                        .subtract(accuracyDetailDataDTO1.getHisLoad()).abs();
                if (accuracyDetailDataDTO1.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal divide = subtract.divide(accuracyDetailDataDTO1.getHisLoad(), 4, RoundingMode.HALF_UP);
                    BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                    accuracyDetailDataDTO1.setAccuracy(bigDecimal);
                    bigLoad = bigLoad.add(bigDecimal);
                    a = a + 1;
                }
            }
            accuracyDetailDataDTOList.add(accuracyDetailDataDTO1);
        }
        AccuracyDetailDataDTO accuracyDetailDataDTO1 = new AccuracyDetailDataDTO();
        accuracyDetailDataDTO1.setName(EVENING_PEEK + endTime);
        accuracyDetailDataDTO1.setFcLoad(fcBigDecimals.get(timePoint1 - 1));
        accuracyDetailDataDTO1.setHisLoad(hisBigDecimals.get(timePoint1 - 1));
        if (accuracyDetailDataDTO1.getFcLoad() != null && accuracyDetailDataDTO1.getHisLoad()!= null) {
            BigDecimal subtract = accuracyDetailDataDTO1.getFcLoad()
                    .subtract(accuracyDetailDataDTO1.getHisLoad()).abs();
            if (accuracyDetailDataDTO1.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal divide = subtract.divide(accuracyDetailDataDTO1.getHisLoad(), 4, RoundingMode.HALF_UP);
                BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                accuracyDetailDataDTO1.setAccuracy(bigDecimal);
            }
        }
        accuracyDetailDataDTOList.add(accuracyDetailDataDTO1);
        if (!CollectionUtils.isEmpty(accuracyDetailDataDTOList)) {
            BigDecimal reduce = accuracyDetailDataDTOList.stream().filter(t -> t.getFcLoad() != null)
                    .min(Comparator.comparing(AccuracyDetailDataDTO::getFcLoad)).get().getFcLoad();
            BigDecimal reduce1 = accuracyDetailDataDTOList.stream().filter(t -> t.getHisLoad() != null)
                    .min(Comparator.comparing(AccuracyDetailDataDTO::getHisLoad)).get().getHisLoad();
            return BigDecimal.ONE.subtract(reduce.subtract(reduce1).abs().divide(reduce1, 4, RoundingMode.HALF_UP));
        } else {
            return null;
        }
    }*/

    public BigDecimal countLoadDataDTO(List<BigDecimal> fcBigDecimals, List<BigDecimal> hisBigDecimals, String startTime,
                                       String endTime) {
        BigDecimal bigLoad = BigDecimal.ZERO;
        int a = 0;
        List<AccuracyDetailDataDTO> accuracyDetailDataDTOList = new ArrayList<>();
        int timePoint = DateUtil.getTimePoint(startTime.replaceAll(":", ""), 96);
        int timePoint1 = DateUtil.getTimePoint(endTime.replaceAll(":", ""), 96);
        AccuracyDetailDataDTO accuracyDetailDataDTO = new AccuracyDetailDataDTO();
        accuracyDetailDataDTO.setName(EVENING_PEEK + startTime);
        accuracyDetailDataDTO.setFcLoad(fcBigDecimals.get(timePoint - 1));
        accuracyDetailDataDTO.setHisLoad(hisBigDecimals.get(timePoint - 1));
        if (accuracyDetailDataDTO.getFcLoad() != null && accuracyDetailDataDTO.getHisLoad()!= null) {
            BigDecimal subtract = accuracyDetailDataDTO.getFcLoad()
                    .subtract(accuracyDetailDataDTO.getHisLoad()).abs();
            if (accuracyDetailDataDTO.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal divide = subtract.divide(accuracyDetailDataDTO.getHisLoad(), 4, RoundingMode.HALF_UP);
                BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                accuracyDetailDataDTO.setAccuracy(bigDecimal);
                bigLoad = bigLoad.add(bigDecimal);
                a = a + 1;
            }
        }
        accuracyDetailDataDTOList.add(accuracyDetailDataDTO);
        for (int i = 1; i < timePoint1 - timePoint; i++) {
            BigDecimal bigDecimal1 = fcBigDecimals.get(timePoint + i - 1);
            BigDecimal bigDecimal2 = hisBigDecimals.get(timePoint + i - 1);
            String hHmmTime = DateUtil.getHHmmTime("0015", 2, timePoint + i);
            AccuracyDetailDataDTO accuracyDetailDataDTO1 = new AccuracyDetailDataDTO();
            accuracyDetailDataDTO1.setName(EVENING_PEEK + hHmmTime);
            accuracyDetailDataDTO1.setFcLoad(bigDecimal1);
            accuracyDetailDataDTO1.setHisLoad(bigDecimal2);
            if (accuracyDetailDataDTO1.getFcLoad() != null && accuracyDetailDataDTO1.getHisLoad()!= null) {
                BigDecimal subtract = accuracyDetailDataDTO1.getFcLoad()
                        .subtract(accuracyDetailDataDTO1.getHisLoad()).abs();
                if (accuracyDetailDataDTO1.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal divide = subtract.divide(accuracyDetailDataDTO1.getHisLoad(), 4, RoundingMode.HALF_UP);
                    BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                    accuracyDetailDataDTO1.setAccuracy(bigDecimal);
                    bigLoad = bigLoad.add(bigDecimal);
                    a = a + 1;
                }
            }
            accuracyDetailDataDTOList.add(accuracyDetailDataDTO1);
        }
        AccuracyDetailDataDTO accuracyDetailDataDTO1 = new AccuracyDetailDataDTO();
        accuracyDetailDataDTO1.setName(EVENING_PEEK + endTime);
        accuracyDetailDataDTO1.setFcLoad(fcBigDecimals.get(timePoint1 - 1));
        accuracyDetailDataDTO1.setHisLoad(hisBigDecimals.get(timePoint1 - 1));
        if (accuracyDetailDataDTO1.getFcLoad() != null && accuracyDetailDataDTO1.getHisLoad()!= null) {
            BigDecimal subtract = accuracyDetailDataDTO1.getFcLoad()
                    .subtract(accuracyDetailDataDTO1.getHisLoad()).abs();
            if (accuracyDetailDataDTO1.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal divide = subtract.divide(accuracyDetailDataDTO1.getHisLoad(), 4, RoundingMode.HALF_UP);
                BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                accuracyDetailDataDTO1.setAccuracy(bigDecimal);
            }
        }
        accuracyDetailDataDTOList.add(accuracyDetailDataDTO1);
        if (!CollectionUtils.isEmpty(accuracyDetailDataDTOList)) {
            BigDecimal reduce = accuracyDetailDataDTOList.stream().filter(t -> t.getAccuracy() != null)
                    .map(AccuracyDetailDataDTO::getAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add);
            return reduce.divide(BigDecimal.valueOf(accuracyDetailDataDTOList.size()), 4, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }

    public void countLoadData(List<BigDecimal> fcBigDecimals, List<BigDecimal> hisBigDecimals, String startTime,
                              String endTime, AccuracyDetailValueDataDTO accuracyDetailValueDataDTO) {
        BigDecimal bigLoad = BigDecimal.ZERO;
        int a = 0;
        List<AccuracyDetailDataDTO> accuracyDetailDataDTOList = new ArrayList<>();
        int timePoint = DateUtil.getTimePoint(startTime.replaceAll(":", ""), 96);
        int timePoint1 = DateUtil.getTimePoint(endTime.replaceAll(":", ""), 96);
        AccuracyDetailDataDTO accuracyDetailDataDTO = new AccuracyDetailDataDTO();
        accuracyDetailDataDTO.setName(EVENING_PEEK + startTime);
        accuracyDetailDataDTO.setFcLoad(fcBigDecimals.get(timePoint - 1));
        accuracyDetailDataDTO.setHisLoad(hisBigDecimals.get(timePoint - 1));
        if (accuracyDetailDataDTO.getFcLoad() != null && accuracyDetailDataDTO.getHisLoad()!= null) {
            BigDecimal subtract = accuracyDetailDataDTO.getFcLoad()
                    .subtract(accuracyDetailDataDTO.getHisLoad()).abs();
            if (accuracyDetailDataDTO.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal divide = subtract.divide(accuracyDetailDataDTO.getHisLoad(), 4, RoundingMode.HALF_UP);
                BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                accuracyDetailDataDTO.setAccuracy(bigDecimal);
                bigLoad = bigLoad.add(bigDecimal);
                a = a + 1;
            }
        }
        accuracyDetailDataDTOList.add(accuracyDetailDataDTO);
        for (int i = 1; i < timePoint1 - timePoint; i++) {
            BigDecimal bigDecimal1 = fcBigDecimals.get(timePoint + i - 1);
            BigDecimal bigDecimal2 = hisBigDecimals.get(timePoint + i - 1);
            String hHmmTime = DateUtil.getHHmmTime("0015", 2, timePoint + i);
            AccuracyDetailDataDTO accuracyDetailDataDTO1 = new AccuracyDetailDataDTO();
            accuracyDetailDataDTO1.setName(EVENING_PEEK + hHmmTime);
            accuracyDetailDataDTO1.setFcLoad(bigDecimal1);
            accuracyDetailDataDTO1.setHisLoad(bigDecimal2);
            if (accuracyDetailDataDTO1.getFcLoad() != null && accuracyDetailDataDTO1.getHisLoad()!= null) {
                BigDecimal subtract = accuracyDetailDataDTO1.getFcLoad()
                        .subtract(accuracyDetailDataDTO1.getHisLoad()).abs();
                if (accuracyDetailDataDTO1.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal divide = subtract.divide(accuracyDetailDataDTO1.getHisLoad(), 4, RoundingMode.HALF_UP);
                    BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                    accuracyDetailDataDTO1.setAccuracy(bigDecimal);
                    bigLoad = bigLoad.add(bigDecimal);
                    a = a + 1;
                }
            }
            accuracyDetailDataDTOList.add(accuracyDetailDataDTO1);
        }
        AccuracyDetailDataDTO accuracyDetailDataDTO1 = new AccuracyDetailDataDTO();
        accuracyDetailDataDTO1.setName(EVENING_PEEK + endTime);
        accuracyDetailDataDTO1.setFcLoad(fcBigDecimals.get(timePoint1 - 1));
        accuracyDetailDataDTO1.setHisLoad(hisBigDecimals.get(timePoint1 - 1));
        if (accuracyDetailDataDTO1.getFcLoad() != null && accuracyDetailDataDTO1.getHisLoad()!= null) {
            BigDecimal subtract = accuracyDetailDataDTO1.getFcLoad()
                    .subtract(accuracyDetailDataDTO1.getHisLoad()).abs();
            if (accuracyDetailDataDTO1.getHisLoad().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal divide = subtract.divide(accuracyDetailDataDTO1.getHisLoad(), 4, RoundingMode.HALF_UP);
                BigDecimal bigDecimal = BigDecimal.ONE.subtract(divide);
                accuracyDetailDataDTO1.setAccuracy(bigDecimal);
                bigLoad = bigLoad.add(bigDecimal);
                a = a + 1;
            }
        }
        accuracyDetailDataDTOList.add(accuracyDetailDataDTO1);
        // 综合准确率
        AccuracyDetailDataDTO accuracyDetailDataDTO2 = new AccuracyDetailDataDTO();
        accuracyDetailDataDTO2.setName(COMP_OTHER_ACCURACY);
        if (bigLoad.compareTo(BigDecimal.ZERO)!= 0 && a != 0) {
            accuracyDetailDataDTO2.setAccuracy(bigLoad.divide(BigDecimal.valueOf(a), 4, RoundingMode.HALF_UP));
        }
        accuracyDetailDataDTOList.add(accuracyDetailDataDTO2);
        accuracyDetailValueDataDTO.setAccuracyDetailDataDTOList(accuracyDetailDataDTOList);
    }

    public BigDecimal countLoad(List<BigDecimal> bigDecimals, String startTime, String endTime) {
        BigDecimal load = null;
        List<BigDecimal> loadList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bigDecimals)) {
            int timePoint = DateUtil.getTimePoint(startTime.replaceAll(":", ""), 96);
            int timePoint1 = DateUtil.getTimePoint(endTime.replaceAll(":", ""), 96);
            loadList.add(bigDecimals.get(timePoint-1));
            for (int i = 1; i < timePoint1 - timePoint; i++) {
                loadList.add(bigDecimals.get(timePoint + i -1));
            }
            loadList.add(bigDecimals.get(timePoint1-1));
        }
        if (!CollectionUtils.isEmpty(loadList)) {
            load = BigDecimalFunctions.listMin(loadList);
        }
        return load;
    }
}
