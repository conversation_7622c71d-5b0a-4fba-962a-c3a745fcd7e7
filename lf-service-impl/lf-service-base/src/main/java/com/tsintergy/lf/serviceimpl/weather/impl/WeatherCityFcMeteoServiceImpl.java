package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStatService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcMeteoDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcMeteoDAO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Slf4j
@Service("weatherCityFcMeteoService")
public class WeatherCityFcMeteoServiceImpl implements WeatherCityFcMeteoService {

    @Autowired
    private WeatherCityFcMeteoDAO weatherCityFcMeteoDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    private WeatherStatService weatherStatService;

    @Override
    public List<WeatherCityFcMeteoDO> getListByCondition(String cityId, Integer type, Date startDate, Date endDate) {
        return weatherCityFcMeteoDAO.findAll(
                JpaWrappers.<WeatherCityFcMeteoDO>lambdaQuery()
                        .eq(cityId != null, WeatherCityFcMeteoDO::getCityId, cityId)
                        .eq(type != null, WeatherCityFcMeteoDO::getType, type)
                        .ge(WeatherCityFcMeteoDO::getDate, new java.sql.Date(startDate.getTime()))
                        .le(WeatherCityFcMeteoDO::getDate, new java.sql.Date(endDate.getTime())));
    }

    @Override
    public void doSaveOrUpdate(WeatherCityFcMeteoDO weatherCityFcMeteoDO) {
        List<WeatherCityFcMeteoDO> all = weatherCityFcMeteoDAO.findAll(
                JpaWrappers.<WeatherCityFcMeteoDO>lambdaQuery()
                        .eq(WeatherCityFcMeteoDO::getCityId, weatherCityFcMeteoDO.getCityId())
                        .eq(WeatherCityFcMeteoDO::getType, weatherCityFcMeteoDO.getType())
                        .eq(WeatherCityFcMeteoDO::getDate, weatherCityFcMeteoDO.getDate()));
        if (CollectionUtils.isEmpty(all)) {
            weatherCityFcMeteoDAO.save(weatherCityFcMeteoDO);
        } else {
            weatherCityFcMeteoDO.setId(all.get(0).getId());
            weatherCityFcMeteoDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityFcMeteoDAO.saveOrUpdateByTemplate(weatherCityFcMeteoDO);
        }
    }

    @SneakyThrows
    @Override
    public void insertYesterday24HourData(WeatherCityFcMeteoDO weatherCityFcMeteoDO) {
        Date yesterday = DateUtils.addDays(weatherCityFcMeteoDO.getDate(), -1);
        WeatherCityFcMeteoDO yesterdayDO = weatherCityFcMeteoDAO.findOne(
                JpaWrappers.<WeatherCityFcMeteoDO>lambdaQuery()
                        .eq(WeatherCityFcMeteoDO::getCityId, weatherCityFcMeteoDO.getCityId())
                        .eq(WeatherCityFcMeteoDO::getType, weatherCityFcMeteoDO.getType())
                        .eq(WeatherCityFcMeteoDO::getDate, yesterday));
        if (yesterdayDO == null) {
            return;
        }
        yesterdayDO.setT2400(weatherCityFcMeteoDO.getT0000());

        BigDecimal t2300 = yesterdayDO.getT2300();
        BigDecimal t2400 = weatherCityFcMeteoDO.getT0000();
        if (t2400 != null) {
            BigDecimal difference = t2400.subtract(t2300);
            BigDecimal t2315 = t2300.add(difference.multiply(new BigDecimal("0.25")));
            BigDecimal t2330 = t2300.add(difference.multiply(new BigDecimal("0.5")));
            BigDecimal t2345 = t2300.add(difference.multiply(new BigDecimal("0.75")));
            yesterdayDO.setT2315(t2315);
            yesterdayDO.setT2330(t2330);
            yesterdayDO.setT2345(t2345);
        }
        List<BigDecimal> weatherList = yesterdayDO.getWeatherList();
        ColumnUtil.supplimentPoit(weatherList);
        Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(weatherList, Constants.LOAD_CURVE_START_WITH_ZERO);
        BasePeriodUtils.setAllFiled(yesterdayDO, decimalMap);
        yesterdayDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        weatherCityFcMeteoDAO.saveOrUpdateByTemplate(yesterdayDO);
    }

    @SneakyThrows
    @Override
    public void statMeteoProvinceFcWeather(Date startDate, Date endDate) {
        List<WeatherCityFcMeteoDO> weatherCityFcMeteoDOS = this.getListByCondition(null, null, startDate, startDate);
        List<WeatherCityFcMeteoDO> meteoDOS = weatherStatService.calcProvinceWeather(weatherCityFcMeteoDOS, WeatherCityFcMeteoDO.class);
        if (CollectionUtils.isNotEmpty(meteoDOS)) {
            for (WeatherCityFcMeteoDO weatherCityFcMeteoDO : meteoDOS) {
                this.doSaveOrUpdate(weatherCityFcMeteoDO);
                this.insertYesterday24HourData(weatherCityFcMeteoDO);
            }
        }
    }
}
