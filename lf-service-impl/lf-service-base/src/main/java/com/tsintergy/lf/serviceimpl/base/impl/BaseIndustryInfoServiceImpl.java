package com.tsintergy.lf.serviceimpl.base.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.base.api.BaseIndustryInfoService;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseIndustryInfoDO;
import com.tsintergy.lf.serviceimpl.base.dao.BaseIndustryInfoDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("baseIndustryInfoService")
public class BaseIndustryInfoServiceImpl implements BaseIndustryInfoService {

    @Autowired
    BaseIndustryInfoDAO baseIndustryInfoDAO;

    @Override
    public List<BaseIndustryInfoDO> findIndustryInfoByLevel() {
        List<BaseIndustryInfoDO> all = baseIndustryInfoDAO.findAll(JpaWrappers.<BaseIndustryInfoDO>lambdaQuery());
        return all;
    }

    @Override
    public BaseIndustryInfoDO findIndustryInfoById(String id) {
        BaseIndustryInfoDO one = baseIndustryInfoDAO.findOne(JpaWrappers.<BaseIndustryInfoDO>lambdaQuery().eq(BaseIndustryInfoDO::getCode, id));
        return one;
    }

    @Override
    public void saveIndustryInfo(List<BaseIndustryInfoDO> baseIndustryInfoDOList) {
        for (BaseIndustryInfoDO baseIndustryInfoDO : baseIndustryInfoDOList) {
            baseIndustryInfoDAO.save(baseIndustryInfoDO);
        }
    }
}
