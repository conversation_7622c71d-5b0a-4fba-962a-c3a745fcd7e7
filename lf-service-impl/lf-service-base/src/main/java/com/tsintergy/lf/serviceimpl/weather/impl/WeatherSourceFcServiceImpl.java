package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.enums.WeatherSourceAlgorithmEnum;
import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessUnitDTO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.BatchDataFilterService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBmBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcCopy1DO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcMeteoBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.common.util.WeatherCalcUtil;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyAssessDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyCompositeDAO;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  Description:  <br>     <AUTHOR>  @create 2021/7/27  @since 1.0.0 
 */
@Service("weatherSourceFcService")
public class WeatherSourceFcServiceImpl implements WeatherSourceFcService {

    @Autowired
    private CityService cityService;


    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcMeteoBatchService weatherCityFcMeteoBatchService;

    @Autowired
    private WeatherCityFcBmBatchService weatherCityFcBmBatchService;

    @Autowired
    private SettingAssessService settingAssessService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private AccuracyAssessDAO accuracyAssessDAO;

    @Autowired
    private AccuracyCompositeDAO accuracyCompositeDAO;

    @Autowired
    private BatchDataFilterService batchDataFilterService;

    private static final Map<String, List<String>> algorithmIdMap = new HashMap<>();

    private static final Map<String, String> weatherMap = new HashMap<>();

    private static final Map<String, List<String>> weatherAlgorithmMap = new HashMap<>();

    static {
        algorithmIdMap.put("10", Arrays.asList("10", "1000"));
        algorithmIdMap.put("20", Arrays.asList("20", "1002"));
        algorithmIdMap.put("813", Arrays.asList("813", "814", "816"));

        weatherMap.put("10", "气象局预测气象");
        weatherMap.put("1000", "EC气象源预测气象");
        weatherMap.put("20", "气象局预测气象");
        weatherMap.put("1002", "EC气象源预测气象");
        weatherMap.put("813", "气象局预测气象");
        weatherMap.put("814", "EC气象源预测气象");
        weatherMap.put("816", "BM气象源预测气象");

        weatherAlgorithmMap.put("1", Arrays.asList("10", "20", "813"));
        weatherAlgorithmMap.put("3", Arrays.asList("1000", "1002", "814"));
        weatherAlgorithmMap.put("4", Arrays.asList("816"));
    }

    @Override
    public List<WeatherFcDTO> findSourceWeatherPoint(List<String> cityIdS, List<String> sources, Date startDate,
                                                     Date endDate, String type, String hour, Integer weatherType) throws Exception {
        List<WeatherFcDTO> weatherFcDTOS = new ArrayList<>();
        for (String cityId : cityIdS) {
            CityDO city = cityService.findCityById(cityId);
            cityId = cityService.findWeatherCityId(cityId);
            List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
            if (type != null && type.equals(ParamConstants.HIS)) {
                if (!(sources.contains(ParamConstants.HIS))) {
                    sources.add(ParamConstants.HIS);
                }
            }
            List<WeatherCityHisDO> allHisWeatherAll = weatherCityHisService
                    .findWeatherCityHisDOs(cityId, weatherType, startDate, endDate);
            List<WeatherCityFcCopy1DO> allFcWeatherAll = weatherCityFcService
                    .findBatchWeatherFcByHourData(cityId, startDate, endDate, weatherType, hour);
            List<WeatherCityFcMeteoBatchDO> allMeteoWeatherAll = weatherCityFcMeteoBatchService
                    .findBatchWeatherFcByHourDate(cityId, startDate, endDate, weatherType, hour);
            List<WeatherCityFcBmBatchDO> allBmWeatherAll = weatherCityFcBmBatchService
                    .findBatchWeatherFcByHourDate(cityId, startDate, endDate, weatherType, hour);
            Map<java.sql.Date, List<WeatherCityHisDO>> collectHis = new HashMap<>();
            if (!CollectionUtils.isEmpty(allHisWeatherAll)) {
                collectHis = allHisWeatherAll.stream().collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
            }
            Map<java.sql.Date, List<WeatherCityFcCopy1DO>> collectFc = new HashMap<>();
            if (!CollectionUtils.isEmpty(allFcWeatherAll)) {
                collectFc = allFcWeatherAll.stream().collect(Collectors.groupingBy(WeatherCityFcCopy1DO::getDate));
            }
            Map<java.sql.Date, List<WeatherCityFcMeteoBatchDO>> collectMeteo = new HashMap<>();
            if (!CollectionUtils.isEmpty(allMeteoWeatherAll)) {
                collectMeteo = allMeteoWeatherAll.stream().collect(Collectors.groupingBy(WeatherCityFcMeteoBatchDO::getDate));
            }
            Map<java.sql.Date, List<WeatherCityFcBmBatchDO>> collectBm = new HashMap<>();
            if (!CollectionUtils.isEmpty(allBmWeatherAll)) {
                collectBm = allBmWeatherAll.stream().collect(Collectors.groupingBy(WeatherCityFcBmBatchDO::getDate));
            }
            for (Date date : dates) {
                List<WeatherCityHisDO> allHisWeather = collectHis.get(date);
                List<WeatherCityFcCopy1DO> allFcWeather = collectFc.get(date);
                List<WeatherCityFcMeteoBatchDO> allMeteoWeather = collectMeteo.get(date);
                List<WeatherCityFcBmBatchDO> allBmWeather = collectBm.get(date);
                for (String source : sources) {
                    if (source.equals(WeatherSourceEnum.HIS.getCode())) {
                        if (CollectionUtils.isEmpty(allHisWeather)) {
                            continue;
                        }
                        List<WeatherFcDTO> hisDTOs = allHisWeather.stream()
                                .filter(hisDO -> WeatherNewEnum.contains(hisDO.getType()))
                                .map(hisDO -> {
                                    WeatherFcDTO weatherFcDTO = new WeatherFcDTO();
                                    weatherFcDTO.setCityId(hisDO.getCityId());
                                    weatherFcDTO.setCityName(city.getCity());
                                    weatherFcDTO.setDate(date);
                                    weatherFcDTO.setSource(source);
                                    weatherFcDTO.setType(String.valueOf(hisDO.getType()));
                                    List<BigDecimal> list = BasePeriodUtils
                                            .toList(hisDO, Constants.WEATHER_CURVE_POINT_NUM,
                                                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                                    weatherFcDTO.setPoint(list);
                                    weatherFcDTO.setSourceName("气象局实际气象");
                                    return weatherFcDTO;
                                })
                                .collect(Collectors.toList());
                        weatherFcDTOS.addAll(hisDTOs);
                    } else if (source.equals(WeatherSourceEnum.FC.getCode())) {
                        if (CollectionUtils.isEmpty(allFcWeather)) {
                            continue;
                        }
                        List<WeatherFcDTO> fcDTOs = allFcWeather.stream()
                                .filter(fcDO -> WeatherNewEnum.contains(fcDO.getType()))
                                .map(fcDO -> {
                                    WeatherFcDTO weatherFcDTO = new WeatherFcDTO();
                                    weatherFcDTO.setCityId(fcDO.getCityId());
                                    weatherFcDTO.setCityName(city.getCity());
                                    weatherFcDTO.setDate(date);
                                    weatherFcDTO.setSource(source);
                                    weatherFcDTO.setType(String.valueOf(fcDO.getType()));
                                    List<BigDecimal> pointList = BasePeriodUtils
                                            .toList(fcDO, Constants.WEATHER_CURVE_POINT_NUM,
                                                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                                    weatherFcDTO.setPoint(pointList);
                                    weatherFcDTO.setSourceName("气象局预测气象");
                                    return weatherFcDTO;
                                })
                                .collect(Collectors.toList());
                        weatherFcDTOS.addAll(fcDTOs);
                    } else if (source.equals(WeatherSourceEnum.METEO.getCode())) {
                        if (CollectionUtils.isEmpty(allMeteoWeather)) {
                            continue;
                        }
                        List<WeatherFcDTO> meteoDTOs = allMeteoWeather.stream()
                                .filter(meteoDO -> WeatherNewEnum.contains(meteoDO.getType()))
                                .map(meteoDO -> {
                                    WeatherFcDTO weatherFcDTO = new WeatherFcDTO();
                                    weatherFcDTO.setCityId(meteoDO.getCityId());
                                    weatherFcDTO.setCityName(city.getCity());
                                    weatherFcDTO.setDate(date);
                                    weatherFcDTO.setSource(source);
                                    weatherFcDTO.setType(String.valueOf(meteoDO.getType()));
                                    List<BigDecimal> list = BasePeriodUtils.toList(meteoDO,
                                            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                                    weatherFcDTO.setPoint(list);
                                    weatherFcDTO.setSourceName("EC气象源预测气象");
                                    return weatherFcDTO;
                                })
                                .collect(Collectors.toList());
                        weatherFcDTOS.addAll(meteoDTOs);
                    } else if (source.equals(WeatherSourceEnum.BM.getCode())) {
                        if (CollectionUtils.isEmpty(allBmWeather)) {
                            continue;
                        }
                        List<WeatherFcDTO> bmDTOs = allBmWeather.stream()
                                .filter(meteoDO -> WeatherNewEnum.contains(meteoDO.getType()))
                                .map(meteoDO -> {
                                    WeatherFcDTO weatherFcDTO = new WeatherFcDTO();
                                    weatherFcDTO.setCityId(meteoDO.getCityId());
                                    weatherFcDTO.setCityName(city.getCity());
                                    weatherFcDTO.setDate(date);
                                    weatherFcDTO.setSource(source);
                                    weatherFcDTO.setType(String.valueOf(meteoDO.getType()));
                                    List<BigDecimal> list = BasePeriodUtils.toList(meteoDO,
                                            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                                    weatherFcDTO.setPoint(list);
                                    weatherFcDTO.setSourceName("BM气象源预测气象");
                                    return weatherFcDTO;
                                })
                                .collect(Collectors.toList());
                        weatherFcDTOS.addAll(bmDTOs);
                    }
                }
            }
        }
        return weatherFcDTOS;
    }

    @Override
    public List<WeatherFcFeatureDTO> findSourceWeatherFeature(List<String> cityIdS, List<String> sources,
                                                              Date startDate, Date endDate, String hour) throws Exception {
        List<WeatherFcFeatureDTO> WeatherFcFeatureDTOs = new ArrayList<>();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (String city : cityIdS) {
            CityDO cityDO = cityService.findCityById(city);
            city = cityService.findWeatherCityId(city);
            if (!(sources.contains(ParamConstants.HIS))) {
                sources.add(ParamConstants.HIS);
            }
            for (Date date : dates) {
                Map<String, List<BigDecimal>> hisMap = this.getHisMap(city, date);
                for (String source : sources) {
                    List<String> list = new ArrayList<>();
                    list.add(source);
                    List<String> cityS = new ArrayList<>();
                    cityS.add(city);
                    List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(cityS, list, date, date, null, hour, null);
                    if (weatherFcDTOList.size() > 0) {
                        WeatherFcFeatureDTO weatherFcFeatureDTO = new WeatherFcFeatureDTO();
                        weatherFcFeatureDTO.setCityId(city);
                        weatherFcFeatureDTO.setCityName(cityDO.getCity());
                        weatherFcFeatureDTO.setDate(date);
                        weatherFcFeatureDTO.setSource(source);
                        for (WeatherFcDTO weatherFcDTO : weatherFcDTOList) {
                            weatherFcFeatureDTO.setSourceName(weatherFcDTO.getSourceName());
                            if (weatherFcDTO.getPoint() != null && weatherFcDTO.getPoint().size() > 0) {
                                if (WeatherEnum.HUMIDITY.getType() == Integer.parseInt(weatherFcDTO.getType())) {
                                    Map<String, BigDecimal> map = BasePeriodUtils.getMaxMinAvg(weatherFcDTO.getPoint(), 2);
                                    if (map.size() > 0) {
                                        weatherFcFeatureDTO.setHumidityMax(map.get("max"));
                                        weatherFcFeatureDTO.setHumidityMin(map.get("min"));
                                        weatherFcFeatureDTO.setHumidityAvg(map.get("avg"));
                                    }
                                    Map<String, BigDecimal> diffMap = PeriodDataUtil.getDiffMap(weatherFcDTO.getPoint(), hisMap.get(weatherFcDTO.getType()));
                                    weatherFcFeatureDTO.setHumidityMaxDiff(diffMap.get(Constants.MAX_DIFF));
                                    weatherFcFeatureDTO.setMinHumidityDiff(diffMap.get(Constants.MIN_VALUE_DIFF));
                                    weatherFcFeatureDTO.setMaxHumidityDiff(diffMap.get(Constants.MAX_VALUE_DIFF));
                                }
                                if (WeatherEnum.TEMPERATURE.getType() == Integer.parseInt(weatherFcDTO.getType())) {
                                    Map<String, BigDecimal> map = BasePeriodUtils.getMaxMinAvg(weatherFcDTO.getPoint(), 2);
                                    if (map.size() > 0) {
                                        weatherFcFeatureDTO.setTemperatureAvg(map.get("avg"));
                                        weatherFcFeatureDTO.setTemperatureMax(map.get("max"));
                                        weatherFcFeatureDTO.setTemperatureMin(map.get("min"));
                                    }
                                    Map<String, BigDecimal> diffMap = PeriodDataUtil.getDiffMap(weatherFcDTO.getPoint(), hisMap.get(weatherFcDTO.getType()));
                                    weatherFcFeatureDTO.setTempMaxDiff(diffMap.get(Constants.MAX_DIFF));
                                    weatherFcFeatureDTO.setMinTempDiff(diffMap.get(Constants.MIN_VALUE_DIFF));
                                    weatherFcFeatureDTO.setMaxTempDiff(diffMap.get(Constants.MAX_VALUE_DIFF));
                                }
                                if (WeatherEnum.WINDSPEED.getType() == Integer.parseInt(weatherFcDTO.getType())) {
                                    Map<String, BigDecimal> map = BasePeriodUtils.getMaxMinAvg(weatherFcDTO.getPoint(), 2);
                                    if (map.size() > 0) {
                                        weatherFcFeatureDTO.setSpeedMax(map.get("max"));
                                        weatherFcFeatureDTO.setSpeedMin(map.get("min"));
                                        weatherFcFeatureDTO.setSpeedAvg(map.get("avg"));
                                    }
                                    Map<String, BigDecimal> diffMap = PeriodDataUtil.getDiffMap(weatherFcDTO.getPoint(), hisMap.get(weatherFcDTO.getType()));
                                    weatherFcFeatureDTO.setSpeedMaxDiff(diffMap.get(Constants.MAX_DIFF));
                                    weatherFcFeatureDTO.setMinSpeedDiff(diffMap.get(Constants.MIN_VALUE_DIFF));
                                    weatherFcFeatureDTO.setMaxSpeedDiff(diffMap.get(Constants.MAX_VALUE_DIFF));
                                }
                                if (WeatherEnum.RAINFALL.getType() == Integer.parseInt(weatherFcDTO.getType())) {
                                    BigDecimal total = BigDecimalUtils.addAllValue(weatherFcDTO.getPoint());
                                    weatherFcFeatureDTO.setAccumulatePrecipitation(total);
                                }
                            }
                        }
                        WeatherFcFeatureDTOs.add(weatherFcFeatureDTO);
                    }
                }
            }
        }
        return WeatherFcFeatureDTOs;
    }

    @Override
    public List<WeatherSourceCurveDTO> findSourceWeatherCurve(String cityId, List<String> sources, Date date, String hour, String type) throws Exception {
        List<WeatherSourceCurveDTO> result = new ArrayList<>();
        if (sources.contains(WeatherSourceEnum.HIS.getCode())) {
            sources.remove(WeatherSourceEnum.HIS.getCode());
        }
        List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(Arrays.asList(cityId), sources, date,
                date, null, hour, Integer.parseInt(type));
        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityService.findWeatherCityId(cityId), Integer.parseInt(type), date, date);
        if (CollectionUtils.isNotEmpty(weatherFcDTOList)) {
            weatherFcDTOList.stream().collect(Collectors.groupingBy(t->t.getSource())).forEach((k,v)->{
                WeatherSourceCurveDTO weatherSourceCurveDTO = new WeatherSourceCurveDTO();
                weatherSourceCurveDTO.setSource(k);
                weatherSourceCurveDTO.setSourceName(v.get(0).getSourceName());
                weatherSourceCurveDTO.setFcWeatherList(v.get(0).getPoint());
                if (CollectionUtils.isNotEmpty(weatherCityHisDOs)) {
                    weatherSourceCurveDTO.setHisWeatherList(weatherCityHisDOs.get(0).getWeatherList());
                    List<BigDecimal> bigDecimals = WeatherCalcUtil.calcAccuracy(weatherCityHisDOs.get(0).getWeatherList(), v.get(0).getPoint());
                    weatherSourceCurveDTO.setAccuracyList(bigDecimals);
                }
                result.add(weatherSourceCurveDTO);
            });
        }
        return result;
    }

    @Override
    public List<WeatherSourcePeriodDTO> findSourceWeatherPeriod(String cityId, List<String> sources, Date date, String hour, String type) throws Exception {
        Map<String, WeatherSourcePeriodDTO> map = new HashMap<>();
        map.put("1", new WeatherSourcePeriodDTO("1", "96点准确率"));
        map.put("2", new WeatherSourcePeriodDTO("2", "日保供"));
        map.put("3", new WeatherSourcePeriodDTO("3", "日午间低谷"));
        map.put("4", new WeatherSourcePeriodDTO("4", "日夜间低谷"));
        map.put("5", new WeatherSourcePeriodDTO("5", "日最大"));
        List<WeatherSourcePeriodDTO> result = new ArrayList<>();
        List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(Arrays.asList(cityId), sources, date,
                date, null, hour, Integer.parseInt(type));
        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityService.findWeatherCityId(cityId), Integer.parseInt(type), date, date);
        String year = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd").split("-")[0];
        String month = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd").split("-")[1];
        List<SettingAssessDataDTO> assessDataList = settingAssessService.findAssessDataList(year, getCaliberId(cityId));
        Map<String, List<SettingAssessDataDTO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(assessDataList)) {
            collect = assessDataList.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
        }
        String bgStartTime = null;
        String bgEndTime = null;
        String noonStartTime = null;
        String noonEndTime = null;
        String eveningStartTime = null;
        String eveningEndTime = null;
        if (CollectionUtils.isNotEmpty(weatherFcDTOList)) {
            for (WeatherFcDTO weatherFcDTO : weatherFcDTOList) {
                if (weatherFcDTO.getSource().equals(ParamConstants.HIS)) {
                    continue;
                }
                // 96点准确率
                List<BigDecimal> bigDecimals = WeatherCalcUtil.calcAccuracy(weatherCityHisDOs.get(0).getWeatherList(), weatherFcDTO.getPoint());
                BigDecimal bigDecimal = BigDecimalUtils.avgList(bigDecimals, bigDecimals.size(), false);
                WeatherSourcePeriodDTO weatherSourcePeriodDTO = map.get("1");
                if (weatherSourcePeriodDTO.getAccuracy() == null) {
                    weatherSourcePeriodDTO.setAccuracy(bigDecimal);
                    weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                } else if (weatherSourcePeriodDTO.getAccuracy().compareTo(bigDecimal) < 0) {
                    weatherSourcePeriodDTO.setAccuracy(bigDecimal);
                    weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                }
                result.removeIf(t -> t.getType().equals(weatherSourcePeriodDTO.getType()));
                result.add(weatherSourcePeriodDTO);
                // 日保供
                List<SettingAssessDataDTO> bgData = collect.get("日保供");
                if (CollectionUtils.isNotEmpty(bgData)) {
                    List<SettingAssessUnitDTO> collect1 = bgData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        bgStartTime = collect1.get(0).getStartTime();
                        bgEndTime = collect1.get(0).getEndTime();
                    } else {
                        bgStartTime = "00:15";
                        bgEndTime = "24:00";
                    }
                }
                BigDecimal bgdWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), bgStartTime + "-" + bgEndTime, 2);
                WeatherSourcePeriodDTO weatherSourcePeriodDTO2 = map.get("2");
                if (weatherSourcePeriodDTO2.getAccuracy() == null) {
                    weatherSourcePeriodDTO2.setAccuracy(bgdWeatherPeriodAccuracy);
                    weatherSourcePeriodDTO2.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO2.setSourceName(weatherFcDTO.getSourceName());
                } else if (weatherSourcePeriodDTO2.getAccuracy().compareTo(bgdWeatherPeriodAccuracy) < 0) {
                    weatherSourcePeriodDTO2.setAccuracy(bgdWeatherPeriodAccuracy);
                    weatherSourcePeriodDTO2.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO2.setSourceName(weatherFcDTO.getSourceName());
                }
                result.removeIf(t -> t.getType().equals(weatherSourcePeriodDTO2.getType()));
                result.add(weatherSourcePeriodDTO2);
                // 日午间低谷
                List<SettingAssessDataDTO> noonData = collect.get("日午间低谷");
                if (CollectionUtils.isNotEmpty(noonData)) {
                    List<SettingAssessUnitDTO> collect1 = noonData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        noonStartTime = collect1.get(0).getStartTime();
                        noonEndTime = collect1.get(0).getEndTime();
                    } else {
                        noonStartTime = "00:15";
                        noonEndTime = "24:00";
                    }
                }
                BigDecimal noonWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), noonStartTime + "-" + noonEndTime, 3);
                WeatherSourcePeriodDTO weatherSourcePeriodDTO3 = map.get("3");
                if (weatherSourcePeriodDTO3.getAccuracy() == null) {
                    weatherSourcePeriodDTO3.setAccuracy(noonWeatherPeriodAccuracy);
                    weatherSourcePeriodDTO3.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO3.setSourceName(weatherFcDTO.getSourceName());
                } else if (weatherSourcePeriodDTO3.getAccuracy().compareTo(noonWeatherPeriodAccuracy) < 0) {
                    weatherSourcePeriodDTO3.setAccuracy(noonWeatherPeriodAccuracy);
                    weatherSourcePeriodDTO3.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO3.setSourceName(weatherFcDTO.getSourceName());
                }
                result.removeIf(t -> t.getType().equals(weatherSourcePeriodDTO3.getType()));
                result.add(weatherSourcePeriodDTO3);
                // 日夜间低谷
                List<SettingAssessDataDTO> eveningData = collect.get("日夜间低谷");
                if (CollectionUtils.isNotEmpty(eveningData)) {
                    List<SettingAssessUnitDTO> collect1 = eveningData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        eveningStartTime = collect1.get(0).getStartTime();
                        eveningEndTime = collect1.get(0).getEndTime();
                    } else {
                        eveningStartTime = "00:15";
                        eveningEndTime = "24:00";
                    }
                }
                BigDecimal eveningWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), eveningStartTime + "-" + eveningEndTime, 4);
                WeatherSourcePeriodDTO weatherSourcePeriodDTO4 = map.get("4");
                if (weatherSourcePeriodDTO4.getAccuracy() == null) {
                    weatherSourcePeriodDTO4.setAccuracy(eveningWeatherPeriodAccuracy);
                    weatherSourcePeriodDTO4.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO4.setSourceName(weatherFcDTO.getSourceName());
                } else if (weatherSourcePeriodDTO4.getAccuracy().compareTo(eveningWeatherPeriodAccuracy) < 0) {
                    weatherSourcePeriodDTO4.setAccuracy(eveningWeatherPeriodAccuracy);
                    weatherSourcePeriodDTO4.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO4.setSourceName(weatherFcDTO.getSourceName());
                }
                result.removeIf(t -> t.getType().equals(weatherSourcePeriodDTO4.getType()));
                result.add(weatherSourcePeriodDTO4);
                // 日最大
                BigDecimal bigDecimalsMax = WeatherCalcUtil.calcPointAccuracy(BigDecimalUtils.getMax(weatherCityHisDOs.get(0).getWeatherList()),
                        BigDecimalUtils.getMax(weatherFcDTO.getPoint()));
                WeatherSourcePeriodDTO weatherSourcePeriodDTO5 = map.get("5");
                if (weatherSourcePeriodDTO5.getAccuracy() == null) {
                    weatherSourcePeriodDTO5.setAccuracy(bigDecimalsMax);
                    weatherSourcePeriodDTO5.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO5.setSourceName(weatherFcDTO.getSourceName());
                } else if (weatherSourcePeriodDTO5.getAccuracy().compareTo(bigDecimalsMax) < 0) {
                    weatherSourcePeriodDTO5.setAccuracy(bigDecimalsMax);
                    weatherSourcePeriodDTO5.setSource(weatherFcDTO.getSource());
                    weatherSourcePeriodDTO5.setSourceName(weatherFcDTO.getSourceName());
                }
                result.removeIf(t -> t.getType().equals(weatherSourcePeriodDTO5.getType()));
                result.add(weatherSourcePeriodDTO5);
            }
        }
        return result;
    }

    @Override
    public List<WeatherSourcePeriodDTO> findSourceOneWeatherPeriod(String cityId, List<String> sources, Date date, String hour, String type, String periodType) throws Exception {
        List<WeatherSourcePeriodDTO> result = new ArrayList<>();
        List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(Arrays.asList(cityId), sources, date,
                date, null, hour, Integer.parseInt(type));
        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityService.findWeatherCityId(cityId), Integer.parseInt(type), date, date);
        String year = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd").split("-")[0];
        String month = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd").split("-")[1];
        List<SettingAssessDataDTO> assessDataList = settingAssessService.findAssessDataList(year, getCaliberId(cityId));
        Map<String, List<SettingAssessDataDTO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(assessDataList)) {
            collect = assessDataList.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
        }
        String bgStartTime = null;
        String bgEndTime = null;
        String noonStartTime = null;
        String noonEndTime = null;
        String eveningStartTime = null;
        String eveningEndTime = null;
        if (CollectionUtils.isNotEmpty(weatherFcDTOList)) {
            for (WeatherFcDTO weatherFcDTO : weatherFcDTOList) {
                if (weatherFcDTO.getSource().equals(ParamConstants.HIS)) {
                    continue;
                }
                WeatherSourcePeriodDTO weatherSourcePeriodDTO = new WeatherSourcePeriodDTO();
                if (Integer.parseInt(periodType) == 1) {
                    // 96点准确率
                    if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
                        List<BigDecimal> bigDecimals = WeatherCalcUtil.calcAccuracy(weatherCityHisDOs.get(0).getWeatherList(), weatherFcDTO.getPoint());
                        BigDecimal bigDecimal = BigDecimalUtils.avgList(bigDecimals, bigDecimals.size(), false);
                        weatherSourcePeriodDTO.setAccuracy(bigDecimal);
                        weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                        weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                        weatherSourcePeriodDTO.setType("1");
                        weatherSourcePeriodDTO.setTypeName("96点准确率");
                        result.add(weatherSourcePeriodDTO);
                    }
                } else if (Integer.parseInt(periodType) == 2) {
                    // 日保供
                    List<SettingAssessDataDTO> bgData = collect.get("日保供");
                    if (CollectionUtils.isNotEmpty(bgData)) {
                        List<SettingAssessUnitDTO> collect1 = bgData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect1)) {
                            bgStartTime = collect1.get(0).getStartTime();
                            bgEndTime = collect1.get(0).getEndTime();
                        } else {
                            bgStartTime = "00:15";
                            bgEndTime = "24:00";
                        }
                    }
                    if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
                        BigDecimal bgdWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), bgStartTime + "-" + bgEndTime, 2);
                        weatherSourcePeriodDTO.setAccuracy(bgdWeatherPeriodAccuracy);
                        weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                        weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                        weatherSourcePeriodDTO.setType("2");
                        weatherSourcePeriodDTO.setTypeName("日保供");
                        result.add(weatherSourcePeriodDTO);
                    }
                } else if (Integer.parseInt(periodType) == 3) {
                    // 日午间低谷
                    List<SettingAssessDataDTO> noonData = collect.get("日午间低谷");
                    if (CollectionUtils.isNotEmpty(noonData)) {
                        List<SettingAssessUnitDTO> collect1 = noonData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect1)) {
                            noonStartTime = collect1.get(0).getStartTime();
                            noonEndTime = collect1.get(0).getEndTime();
                        } else {
                            noonStartTime = "00:15";
                            noonEndTime = "24:00";
                        }
                    }
                    if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
                        BigDecimal noonWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), noonStartTime + "-" + noonEndTime, 3);
                        weatherSourcePeriodDTO.setAccuracy(noonWeatherPeriodAccuracy);
                        weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                        weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                        weatherSourcePeriodDTO.setType("3");
                        weatherSourcePeriodDTO.setTypeName("日午间低谷");
                        result.add(weatherSourcePeriodDTO);
                    }
                } else if (Integer.parseInt(periodType) == 4) {
                    // 日夜间低谷
                    List<SettingAssessDataDTO> eveningData = collect.get("日夜间低谷");
                    if (CollectionUtils.isNotEmpty(eveningData)) {
                        List<SettingAssessUnitDTO> collect1 = eveningData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect1)) {
                            eveningStartTime = collect1.get(0).getStartTime();
                            eveningEndTime = collect1.get(0).getEndTime();
                        } else {
                            eveningStartTime = "00:15";
                            eveningEndTime = "24:00";
                        }
                    }
                    if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
                        BigDecimal eveningWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), eveningStartTime + "-" + eveningEndTime, 4);
                        weatherSourcePeriodDTO.setAccuracy(eveningWeatherPeriodAccuracy);
                        weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                        weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                        weatherSourcePeriodDTO.setType("4");
                        weatherSourcePeriodDTO.setTypeName("日夜间低谷");
                        result.add(weatherSourcePeriodDTO);
                    }
                } else if (Integer.parseInt(periodType) == 5) {
                    // 日最大
                    if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
                        BigDecimal bigDecimalsMax = WeatherCalcUtil.calcPointAccuracy(BigDecimalUtils.getMax(weatherCityHisDOs.get(0).getWeatherList()),
                                BigDecimalUtils.getMax(weatherFcDTO.getPoint()));
                        weatherSourcePeriodDTO.setAccuracy(bigDecimalsMax);
                        weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                        weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                        weatherSourcePeriodDTO.setType("5");
                        weatherSourcePeriodDTO.setTypeName("日最大");
                        result.add(weatherSourcePeriodDTO);
                    }
                }
            }
        }
        return result.stream().sorted(Comparator.comparing(WeatherSourcePeriodDTO::getAccuracy, Comparator.reverseOrder())).collect(Collectors.toList());
    }

    @Override
    public List<WeatherSourcePeriodDTO> findSourceDaysWeatherPeriod(String cityId, List<String> sources, Date date, String hour, String type, String periodType, String days) throws Exception {
        List<WeatherSourcePeriodDTO> result = new ArrayList<>();
        Date startDate = DateUtils.addDays(date, - Integer.parseInt(days));
        Date endDate = DateUtils.addDays(date, - 1);
        List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(Arrays.asList(cityId), sources, startDate,
                endDate, null, hour, Integer.parseInt(type));
        Map<java.sql.Date, List<WeatherCityHisDO>> collect2 = new HashMap<>();
        List<WeatherCityHisDO> weatherCityHisDOs1 = weatherCityHisService.findWeatherCityHisDOs(cityService.findWeatherCityId(cityId), Integer.parseInt(type), startDate, endDate);
        if (CollectionUtils.isNotEmpty(weatherCityHisDOs1)) {
            collect2 = weatherCityHisDOs1.stream().collect(Collectors.groupingBy(t -> t.getDate()));
        }
        String year = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd").split("-")[0];
        List<SettingAssessDataDTO> assessDataList = settingAssessService.findAssessDataList(year, getCaliberId(cityId));
        Map<String, List<SettingAssessDataDTO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(assessDataList)) {
            collect = assessDataList.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
        }
        String bgStartTime = null;
        String bgEndTime = null;
        String noonStartTime = null;
        String noonEndTime = null;
        String eveningStartTime = null;
        String eveningEndTime = null;
        if (CollectionUtils.isNotEmpty(weatherFcDTOList)) {
            for (Map.Entry<Date, List<WeatherFcDTO>> dateListEntry : weatherFcDTOList.stream().collect(Collectors.groupingBy(t -> t.getDate())).entrySet()) {
                Date key = dateListEntry.getKey();
                String month = DateUtil.getDateToStrFORMAT(key, "yyyy-MM-dd").split("-")[1];
                List<WeatherCityHisDO> weatherCityHisDOs = collect2.get(key);
                List<WeatherFcDTO> value = dateListEntry.getValue();
                if (CollectionUtils.isNotEmpty(value)) {
                    for (WeatherFcDTO weatherFcDTO : value) {
                        if (weatherFcDTO.getSource().equals(ParamConstants.HIS)) {
                            continue;
                        }
                        WeatherSourcePeriodDTO weatherSourcePeriodDTO = new WeatherSourcePeriodDTO();
                        if (Integer.parseInt(periodType) == 1) {
                            // 96点准确率
                            List<BigDecimal> bigDecimals = WeatherCalcUtil.calcAccuracy(weatherCityHisDOs.get(0).getWeatherList(), weatherFcDTO.getPoint());
                            BigDecimal bigDecimal = BigDecimalUtils.avgList(bigDecimals, bigDecimals.size(), false);
                            weatherSourcePeriodDTO.setAccuracy(bigDecimal);
                            weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                            weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                            weatherSourcePeriodDTO.setType("1");
                            weatherSourcePeriodDTO.setTypeName("96点准确率");
                            weatherSourcePeriodDTO.setDateStr(DateUtil.getDateToStrFORMAT(key, "yyyy-MM-dd"));
                            result.add(weatherSourcePeriodDTO);
                        } else if (Integer.parseInt(periodType) == 2) {
                            // 日保供
                            List<SettingAssessDataDTO> bgData = collect.get("日保供");
                            if (CollectionUtils.isNotEmpty(bgData)) {
                                List<SettingAssessUnitDTO> collect1 = bgData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect1)) {
                                    bgStartTime = collect1.get(0).getStartTime();
                                    bgEndTime = collect1.get(0).getEndTime();
                                } else {
                                    bgStartTime = "00:15";
                                    bgEndTime = "24:00";
                                }
                            }
                            BigDecimal bgdWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), bgStartTime + "-" + bgEndTime, 2);
                            weatherSourcePeriodDTO.setAccuracy(bgdWeatherPeriodAccuracy);
                            weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                            weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                            weatherSourcePeriodDTO.setType("2");
                            weatherSourcePeriodDTO.setTypeName("日保供");
                            weatherSourcePeriodDTO.setDateStr(DateUtil.getDateToStrFORMAT(key, "yyyy-MM-dd"));
                            result.add(weatherSourcePeriodDTO);
                        } else if (Integer.parseInt(periodType) == 3) {
                            // 日午间低谷
                            List<SettingAssessDataDTO> noonData = collect.get("日午间低谷");
                            if (CollectionUtils.isNotEmpty(noonData)) {
                                List<SettingAssessUnitDTO> collect1 = noonData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect1)) {
                                    noonStartTime = collect1.get(0).getStartTime();
                                    noonEndTime = collect1.get(0).getEndTime();
                                } else {
                                    noonStartTime = "00:15";
                                    noonEndTime = "24:00";
                                }
                            }
                            BigDecimal noonWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), noonStartTime + "-" + noonEndTime, 3);
                            weatherSourcePeriodDTO.setAccuracy(noonWeatherPeriodAccuracy);
                            weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                            weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                            weatherSourcePeriodDTO.setType("3");
                            weatherSourcePeriodDTO.setTypeName("日午间低谷");
                            weatherSourcePeriodDTO.setDateStr(DateUtil.getDateToStrFORMAT(key, "yyyy-MM-dd"));
                            result.add(weatherSourcePeriodDTO);
                        } else if (Integer.parseInt(periodType) == 4) {
                            // 日夜间低谷
                            List<SettingAssessDataDTO> eveningData = collect.get("日夜间低谷");
                            if (CollectionUtils.isNotEmpty(eveningData)) {
                                List<SettingAssessUnitDTO> collect1 = eveningData.get(0).getUnitList().stream().filter(t -> t.getMonth().equals(month)).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect1)) {
                                    eveningStartTime = collect1.get(0).getStartTime();
                                    eveningEndTime = collect1.get(0).getEndTime();
                                } else {
                                    eveningStartTime = "00:15";
                                    eveningEndTime = "24:00";
                                }
                            }
                            BigDecimal eveningWeatherPeriodAccuracy = this.getBgdWeatherPeriodAccuracy(weatherFcDTO.getPoint(), weatherCityHisDOs.get(0).getWeatherList(), eveningStartTime + "-" + eveningEndTime, 4);
                            weatherSourcePeriodDTO.setAccuracy(eveningWeatherPeriodAccuracy);
                            weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                            weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                            weatherSourcePeriodDTO.setType("4");
                            weatherSourcePeriodDTO.setTypeName("日夜间低谷");
                            weatherSourcePeriodDTO.setDateStr(DateUtil.getDateToStrFORMAT(key, "yyyy-MM-dd"));
                            result.add(weatherSourcePeriodDTO);
                        } else if (Integer.parseInt(periodType) == 5) {
                            // 日最大
                            BigDecimal bigDecimalsMax = WeatherCalcUtil.calcPointAccuracy(BigDecimalUtils.getMax(weatherCityHisDOs.get(0).getWeatherList()),
                                    BigDecimalUtils.getMax(weatherFcDTO.getPoint()));
                            weatherSourcePeriodDTO.setAccuracy(bigDecimalsMax);
                            weatherSourcePeriodDTO.setSource(weatherFcDTO.getSource());
                            weatherSourcePeriodDTO.setSourceName(weatherFcDTO.getSourceName());
                            weatherSourcePeriodDTO.setType("5");
                            weatherSourcePeriodDTO.setTypeName("日最大");
                            weatherSourcePeriodDTO.setDateStr(DateUtil.getDateToStrFORMAT(key, "yyyy-MM-dd"));
                            result.add(weatherSourcePeriodDTO);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            return result.stream().sorted(Comparator.comparing(WeatherSourcePeriodDTO::getDateStr)).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<WeatherDeviationDTO> findSourceWeatherNewFeature(List<String> cityIdS, List<String> sources,
                                                                 Date startDate, Date endDate, String hour, Integer weatherType, Integer weatherFeature) throws Exception {
        List<WeatherDeviationDTO> result = new ArrayList<>();
        if (sources.size() > 0) {
            List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(cityIdS, sources, startDate,
                    endDate, null, hour, weatherType);
            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityService.findWeatherCityId(cityIdS.get(0)), weatherType, startDate, endDate);
            Map<java.sql.Date, List<WeatherCityHisDO>> collect = new HashMap<>();
            if (CollectionUtils.isNotEmpty(weatherCityHisDOs)) {
                collect = weatherCityHisDOs.stream().collect(Collectors.groupingBy(t -> t.getDate()));
            }
            if (weatherFcDTOList.size() > 0) {
                for (WeatherFcDTO weatherFcDTO : weatherFcDTOList) {
                    if (ParamConstants.HIS.equals(weatherFcDTO.getSource())) {
                        continue;
                    }
                    WeatherDeviationDTO weatherDeviationDTO = new WeatherDeviationDTO();
                    weatherDeviationDTO.setDateStr(DateUtil.getDateToStrFORMAT(weatherFcDTO.getDate(), "yyyy-MM-dd"));
                    weatherDeviationDTO.setSource(weatherFcDTO.getSourceName());
                    List<WeatherCityHisDO> weatherCityHisDOS = collect.get(weatherFcDTO.getDate());
                    if (CollectionUtils.isNotEmpty(weatherCityHisDOS)) {
                        List<BigDecimal> hisPoint = weatherCityHisDOS.get(0).getWeatherList();
                        weatherDeviationDTO = this.getWeatherDeviationAccuracyDTO(weatherDeviationDTO, weatherFcDTO.getPoint(), hisPoint, weatherFeature);
                    }
                    result.add(weatherDeviationDTO);
                }
            }
        }
        return result;
    }

    @SneakyThrows
    private Map<String, List<BigDecimal>> getHisMap(String cityId, Date date) {
        Date today = new Date();
        if (date != null && !date.before(today)) {
            return new HashMap<>();
        }
        List<WeatherFcDTO> sourceWeatherPoint = this.findSourceWeatherPoint(Collections.singletonList(cityId),
                Collections.singletonList(WeatherSourceEnum.HIS.getCode()), date, date, null, null, null);
        Map<String, List<BigDecimal>> hisMap = sourceWeatherPoint.stream()
                .filter(src -> src.getSource().equals(WeatherSourceEnum.HIS.getCode()))
                .collect(Collectors.toMap(WeatherFcDTO::getType, WeatherFcDTO::getPoint));
        return hisMap;
    }

    @Override
    public List<WeatherFcAccuracyDTO> findSourceWeatherAccuracy(String cityId, List<String> sources, Date startDate,
                                                                Date endDate, String hour) throws Exception {
        List<WeatherFcAccuracyDTO> weatherFcAccuracyDTOs = new ArrayList<>();
        List<String> cityS = new ArrayList<>();
        cityId = cityService.findWeatherCityId(cityId);
        cityS.add(cityId);
        if (sources.size() > 0) {
            List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(cityS, sources, startDate, endDate, null, hour, null);
            if (weatherFcDTOList.size() > 0) {
                for (WeatherFcDTO weatherFcDTO : weatherFcDTOList) {
                    if (ParamConstants.HIS.equals(weatherFcDTO.getSource())) {
                        continue;
                    }
                    WeatherFcAccuracyDTO weatherFcAccuracyDTO = new WeatherFcAccuracyDTO();
                    BeanUtils.copyProperties(weatherFcDTO, weatherFcAccuracyDTO);
                    if (weatherFcDTO.getPoint() != null && weatherFcDTO.getPoint().size() > 0) {
                        Map<String, BigDecimal> fcmap = ColumnUtil
                                .listToMap(weatherFcDTO.getPoint(), Constants.WEATHER_CURVE_START_WITH_ZERO);
                        List<BigDecimal> fcList = WeatherCalcUtil.get24Point(fcmap);
                        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                                .findWeatherCityHisDOs(weatherFcDTO.getCityId(), Integer.parseInt(weatherFcDTO.getType()),
                                        weatherFcDTO.getDate(), weatherFcDTO.getDate());
                        List<BigDecimal> hisList = new ArrayList<>();
                        if (weatherCityHisDOs.size() > 0) {
                            hisList = BasePeriodUtils.toList(weatherCityHisDOs.get(0), 24,
                                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                        }
                        if (CollectionUtils.isNotEmpty(hisList) && CollectionUtils.isNotEmpty(fcList)) {
                            List<BigDecimal> list = WeatherCalcUtil.calcSourceAccuracy(hisList, fcList);
                            Map<String, BigDecimal> maxMinAvg = BasePeriodUtils.getMaxMinAvg(list, 4);
                            BigDecimal accuracy = maxMinAvg.get("avg");
                            weatherFcAccuracyDTO.setAccuracy(accuracy);
                        }
                    }
                    weatherFcAccuracyDTOs.add(weatherFcAccuracyDTO);
                }
            }
        }
        return weatherFcAccuracyDTOs;
    }

    @Override
    public WeatherFeatureValuesDTO findSourceWeatherNewAccuracy(String cityId, List<String> sources, Date startDate,
                                                                Date endDate, String hour, Integer weatherType, Integer weatherFeature) throws Exception {
        WeatherFeatureValuesDTO weatherFeatureValuesDTO = new WeatherFeatureValuesDTO();
        List<WeatherFcNewAccuracyDTO> accuracyDateList = new ArrayList<>();
        List<WeatherFeatureAccuracyDTO> accuracyAllList = new ArrayList<>();
        if (sources.size() > 0) {
            List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(Arrays.asList(cityId), sources, startDate,
                    endDate, null, hour, weatherType);
            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityService.findWeatherCityId(cityId), weatherType, startDate, endDate);
            Map<java.sql.Date, List<WeatherCityHisDO>> collect = new HashMap<>();
            if (CollectionUtils.isNotEmpty(weatherCityHisDOs)) {
                collect = weatherCityHisDOs.stream().collect(Collectors.groupingBy(t -> t.getDate()));
            }
            if (weatherFcDTOList.size() > 0) {
                for (WeatherFcDTO weatherFcDTO : weatherFcDTOList) {
                    if (ParamConstants.HIS.equals(weatherFcDTO.getSource())) {
                        continue;
                    }
                    WeatherFcNewAccuracyDTO weatherFcNewAccuracyDTO = new WeatherFcNewAccuracyDTO();
                    weatherFcNewAccuracyDTO.setDateStr(DateUtil.getDateToStrFORMAT(weatherFcDTO.getDate(), "yyyy-MM-dd"));
                    weatherFcNewAccuracyDTO.setSource(weatherFcDTO.getSourceName());
                    List<WeatherCityHisDO> weatherCityHisDOS = collect.get(weatherFcDTO.getDate());
                    if (CollectionUtils.isNotEmpty(weatherCityHisDOS)) {
                        List<BigDecimal> hisPoint = weatherCityHisDOS.get(0).getWeatherList();
                        if (weatherFeature == null) {
                            // 降雨
                            weatherFcNewAccuracyDTO = this.getWeatherRainAccuracyDTO(weatherFcNewAccuracyDTO, weatherFcDTO.getPoint(), hisPoint);
                        } else {
                            weatherFcNewAccuracyDTO = this.getWeatherFeatureAccuracyDTO(weatherFcNewAccuracyDTO, weatherFcDTO.getPoint(), hisPoint);
                            if (weatherFeature == 1) {
                                weatherFcNewAccuracyDTO.setAccuracy(weatherFcNewAccuracyDTO.getHighAccuracy());
                            } else if (weatherFeature == 2) {
                                weatherFcNewAccuracyDTO.setAccuracy(weatherFcNewAccuracyDTO.getAveAccuracy());
                            } else if (weatherFeature == 3) {
                                weatherFcNewAccuracyDTO.setAccuracy(weatherFcNewAccuracyDTO.getLowAccuracy());
                            }
                        }
                    }
                    accuracyDateList.add(weatherFcNewAccuracyDTO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(accuracyDateList)) {
            Map<String, List<WeatherFcNewAccuracyDTO>> collect = accuracyDateList.stream().collect(Collectors.groupingBy(t -> t.getSource()));
            for (Map.Entry<String, List<WeatherFcNewAccuracyDTO>> stringListEntry : collect.entrySet()) {
                WeatherFeatureAccuracyDTO weatherFeatureAccuracyDTO = new WeatherFeatureAccuracyDTO();
                weatherFeatureAccuracyDTO.setSource(stringListEntry.getKey());
                List<WeatherFcNewAccuracyDTO> value = stringListEntry.getValue();
                if (CollectionUtils.isEmpty(value)) {
                    continue;
                }
                List<WeatherFcNewAccuracyDTO> collect1 = value.stream().filter(t -> t.getHighAccuracy() != null).collect(Collectors.toList());
                BigDecimal reduce = collect1.stream().map(WeatherFcNewAccuracyDTO::getHighAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal divide = reduce.divide(BigDecimal.valueOf(collect1.size()), 4, RoundingMode.HALF_UP);
                weatherFeatureAccuracyDTO.setHighAccuracy(divide);
                List<WeatherFcNewAccuracyDTO> collect2 = value.stream().filter(t -> t.getLowAccuracy() != null).collect(Collectors.toList());
                reduce = collect2.stream().map(WeatherFcNewAccuracyDTO::getLowAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add);
                divide = reduce.divide(BigDecimal.valueOf(collect2.size()), 4, RoundingMode.HALF_UP);
                weatherFeatureAccuracyDTO.setLowAccuracy(divide);
                List<WeatherFcNewAccuracyDTO> collect3 = value.stream().filter(t -> t.getAveAccuracy() != null).collect(Collectors.toList());
                reduce = collect3.stream().map(WeatherFcNewAccuracyDTO::getAveAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add);
                divide = reduce.divide(BigDecimal.valueOf(collect3.size()), 4, RoundingMode.HALF_UP);
                weatherFeatureAccuracyDTO.setAveAccuracy(divide);
                accuracyAllList.add(weatherFeatureAccuracyDTO);
            }
        }
        weatherFeatureValuesDTO.setAccuracyDateList(accuracyDateList);
        weatherFeatureValuesDTO.setAccuracyAllList(accuracyAllList);
        return weatherFeatureValuesDTO;
    }

    @Override
    public WeatherSourceAlgorithmDTO findSourceAlgorithmResult(String cityId, List<String> sources, Date startDate,
                                                               Date endDate, String batchId, String algorithmId) throws Exception {
        WeatherSourceAlgorithmDTO weatherSourceAlgorithmDTO = new WeatherSourceAlgorithmDTO();
        List<String> strings = algorithmIdMap.get(algorithmId);
        List<LoadCityHisDO> loadHisData = loadCityHisService.findLoadCityHisDOS(cityId, startDate, endDate, getCaliberId(cityId));
        List<LoadCityFcBatchDO> loadFcData = loadCityFcBatchService.findByBatchData(cityId,
                startDate, endDate, getCaliberId(cityId), null, batchId);
        Map<String, List<LoadCityFcBatchDO>> collectFc = new HashMap<>();
        if (CollectionUtils.isNotEmpty(loadFcData)) {
            collectFc = loadFcData.stream().collect(Collectors.groupingBy(t -> t.getAlgorithmId() + "-" + DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
        }
        Map<String, List<LoadCityHisDO>> collectHis = new HashMap<>();
        if (CollectionUtils.isNotEmpty(loadHisData)) {
            collectHis = loadHisData.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
        }
        List<WeatherSourceAlgorithmDataDTO> weatherSourceAlgorithmDataList = new ArrayList<>();
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : listBetweenDay) {
            String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyyMMdd");
            for (String source : sources) {
                if (source.equals(WeatherSourceEnum.HIS.getCode())) {
                    continue;
                }
                if (WeatherSourceEnum.BM.getCode().equals(source) && !Constants.G_TRANSFORMER_ID.equals(algorithmId)) {
                    continue;
                }
                List<LoadCityFcBatchDO> loadCityFcDOS = new ArrayList<>();
                WeatherSourceAlgorithmDataDTO weatherSourceAlgorithmDataDTO = new WeatherSourceAlgorithmDataDTO();
                weatherSourceAlgorithmDataDTO.setDateStr(dateStr);
                if (WeatherSourceEnum.FC.getCode().equals(source)) {
                    loadCityFcDOS = collectFc.get(strings.get(0) + "-" + dateStr);
                    weatherSourceAlgorithmDataDTO.setSourceName("气象局预测气象");
                } else if (WeatherSourceEnum.METEO.getCode().equals(source)) {
                    loadCityFcDOS = collectFc.get(strings.get(1) + "-" + dateStr);
                    weatherSourceAlgorithmDataDTO.setSourceName("EC气象源预测气象");
                } else if (WeatherSourceEnum.BM.getCode().equals(source)) {
                    loadCityFcDOS = collectFc.get(strings.get(2) + "-" + dateStr);
                    weatherSourceAlgorithmDataDTO.setSourceName("BM气象源预测气象");
                }
                if (CollectionUtils.isNotEmpty(loadCityFcDOS)) {
                    LoadCityFcBatchDO loadCityFcDO = loadCityFcDOS.get(0);
                    weatherSourceAlgorithmDataDTO.setBigDecimal(loadCityFcDO.getloadList());
                }
                weatherSourceAlgorithmDataList.add(weatherSourceAlgorithmDataDTO);
            }
            List<LoadCityHisDO> loadCityHisDOS = collectHis.get(dateStr);
            WeatherSourceAlgorithmDataDTO weatherSourceAlgorithmDataDTO = new WeatherSourceAlgorithmDataDTO();
            weatherSourceAlgorithmDataDTO.setSourceName("气象局实际气象");
            weatherSourceAlgorithmDataDTO.setDateStr(dateStr);
            if (CollectionUtils.isNotEmpty(loadCityHisDOS)) {
                weatherSourceAlgorithmDataDTO.setBigDecimal(loadCityHisDOS.get(0).getloadList());
            }
            weatherSourceAlgorithmDataList.add(weatherSourceAlgorithmDataDTO);
        }
        if (CollectionUtils.isEmpty(weatherSourceAlgorithmDataList)) {
            weatherSourceAlgorithmDTO.setWeatherSourceAlgorithmDataDTO(weatherSourceAlgorithmDataList.stream().filter(t->t.getSourceName() != null).collect(Collectors.toList()));
        } else {
            weatherSourceAlgorithmDTO.setWeatherSourceAlgorithmDataDTO(weatherSourceAlgorithmDataList);
        }
        List<AccuracyLoadCityFcDO> loadSourceAccuracy = getLoadSourceAccuracy(collectFc, collectHis, listBetweenDay, sources, strings);
        List<String> periodLoadAccuracyList = getPeriodLoadAccuracyList(loadSourceAccuracy, strings);
        weatherSourceAlgorithmDTO.setSourceNames(periodLoadAccuracyList);
        return weatherSourceAlgorithmDTO;
    }

    @Override
    public List<WeatherSourceAccuracyDTO> findSourceAlgorithmAccuracy(String cityId, List<String> sources,
                                                                      Date startDate, Date endDate, String batchId) throws Exception {
        List<String> algorithmIds = new ArrayList<>();
        for (String source : sources) {
            if (source.equals(WeatherSourceEnum.HIS.getCode())) {
                continue;
            }
            algorithmIds.addAll(weatherAlgorithmMap.get(source));
        }
        List<WeatherSourceAccuracyDTO> result = new ArrayList<>();
        List<AccuracyAssessDO> assessDOS = accuracyAssessDAO
                .selectListByNameList(cityId, getCaliberId(cityId), Arrays.asList("日保供", "日午间低谷", "日夜间低谷", "日最大"),
                        algorithmIds, startDate, endDate);
        assessDOS = batchDataFilterService.filterAssessByBatchId(assessDOS, cityId, batchId, 1);
        // 日综合准确率
        List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyCompositeDAO
                .selectListByNameList(cityId, getCaliberId(cityId), "日综合", algorithmIds, startDate, endDate);
        accuracyCompositeDOS = batchDataFilterService.filterCompositeByBatchId(accuracyCompositeDOS, cityId, batchId, 1);
        for (WeatherSourceAlgorithmEnum value : WeatherSourceAlgorithmEnum.values()) {
            Map<String, BigDecimal> bigDecimal = new HashMap<>();
            String code = value.getCode();
            WeatherSourceAccuracyDTO weatherSourceAccuracyDTO = new WeatherSourceAccuracyDTO();
            weatherSourceAccuracyDTO.setAlgorithmName(value.getDescription());
            List<String> strings = algorithmIdMap.get(code);
            // 日综合
            List<AccuracyCompositeDO> collect = accuracyCompositeDOS.stream().filter(t -> strings.contains(t.getAlgorithmId())).collect(Collectors.toList());
            List<AccuracyAssessDO> collect1 = assessDOS.stream().filter(t -> strings.contains(t.getAlgorithmId())).collect(Collectors.toList());
            Map<String, List<AccuracyCompositeDO>> collectCompositeDOS = new HashMap<>();
            Map<String, List<AccuracyAssessDO>> collectAssessDOS = new HashMap<>();
            if (CollectionUtils.isNotEmpty(collect)) {
                collectCompositeDOS = collect.stream().collect(Collectors.groupingBy(t -> t.getAlgorithmId() + "-" + t.getAccuracyName()));
            }
            if (CollectionUtils.isNotEmpty(collect1)) {
                collectAssessDOS = collect1.stream().collect(Collectors.groupingBy(t -> t.getAlgorithmId() + "-" + t.getAssessName()));
            }
            collectCompositeDOS.entrySet().forEach(t -> {
                String key = t.getKey();
                List<AccuracyCompositeDO> value1 = t.getValue();
                if (CollectionUtils.isNotEmpty(value1)) {
                    BigDecimal reduce = value1.stream().filter(e -> e.getAccuracy() != null)
                            .map(AccuracyCompositeDO::getAccuracy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal divide = reduce.divide(BigDecimal.valueOf(value1.size()), 4, RoundingMode.HALF_UP);
                    bigDecimal.put(key, divide);
                }
            });
            collectAssessDOS.entrySet().forEach(t -> {
                String key = t.getKey();
                List<AccuracyAssessDO> value1 = t.getValue();
                if (CollectionUtils.isNotEmpty(value1)) {
                    BigDecimal reduce = value1.stream().filter(e -> e.getAccuracy() != null)
                            .map(AccuracyAssessDO::getAccuracy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal divide = reduce.divide(BigDecimal.valueOf(value1.size()), 4, RoundingMode.HALF_UP);
                    bigDecimal.put(key, divide);
                }
            });
            Map<String, BigDecimal> bigDecimalResult = new HashMap<>();
            Map<String, String> algorithmMap = new HashMap<>();
            if (MapUtils.isNotEmpty(bigDecimal)) {
                bigDecimal.entrySet().forEach(t -> {
                    String key = t.getKey();
                    BigDecimal value1 = t.getValue();
                    // bigDecimalResult在put之前先判断是否存在，如果存在就取最大值
                    BigDecimal bigDecimal1 = bigDecimalResult.get(key.split("-")[1]);
                    if (bigDecimal1 == null) {
                        // 不存在直接添加
                        bigDecimalResult.put(key.split("-")[1], value1);
                        algorithmMap.put(key.split("-")[1], key.split("-")[0]);
                    } else {
                        // 存在就取值判断和value1哪儿个大，取大的
                        if (value1.compareTo(bigDecimal1) > 0) {
                            bigDecimalResult.put(key.split("-")[1], value1);
                            algorithmMap.put(key.split("-")[1], key.split("-")[0]);
                        }
                    }
                });
            }
            weatherSourceAccuracyDTO.setSourceName(weatherMap.get(algorithmMap.get("日综合")));
            weatherSourceAccuracyDTO.setDayBgdSourceName(weatherMap.get(algorithmMap.get("日保供")));
            weatherSourceAccuracyDTO.setDayNightSourceName(weatherMap.get(algorithmMap.get("日夜间低谷")));
            weatherSourceAccuracyDTO.setDaySourceName(weatherMap.get(algorithmMap.get("日午间低谷")));
            weatherSourceAccuracyDTO.setDayMaxSourceName(weatherMap.get(algorithmMap.get("日最大")));
            weatherSourceAccuracyDTO.setCompositeAccuracy(bigDecimalResult.get("日综合"));
            weatherSourceAccuracyDTO.setDayBgdAccuracy(bigDecimalResult.get("日保供"));
            weatherSourceAccuracyDTO.setDayTroughAccuracy(bigDecimalResult.get("日午间低谷"));
            weatherSourceAccuracyDTO.setDayNightTroughAccuracy(bigDecimalResult.get("日夜间低谷"));
            weatherSourceAccuracyDTO.setDayMaxAccuracy(bigDecimalResult.get("日最大"));
            result.add(weatherSourceAccuracyDTO);
        }
        return result;
    }

    @Override
    public List<WeatherSourceAccuracyDTO> findSourceAlgorithmFeature(String cityId, List<String> sources, Date startDate,
                                                                     Date endDate, String batchId, String algorithmId) throws Exception {
        List<String> algorithmIds = new ArrayList<>();
        for (String source : sources) {
            if (source.equals(WeatherSourceEnum.HIS.getCode())) {
                continue;
            }
            algorithmIds.addAll(weatherAlgorithmMap.get(source));
        }
        List<WeatherSourceAccuracyDTO> result = new ArrayList<>();
        List<AccuracyAssessDO> assessDOS = accuracyAssessDAO
                .selectListByNameList(cityId, getCaliberId(cityId), Arrays.asList("日保供", "日午间低谷", "日夜间低谷", "日最大"),
                        algorithmIds, startDate, endDate);
        assessDOS = batchDataFilterService.filterAssessByBatchId(assessDOS, cityId, batchId, 1);
        // 日综合准确率
        List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyCompositeDAO
                .selectListByNameList(cityId, getCaliberId(cityId), "日综合", algorithmIds, startDate, endDate);
        accuracyCompositeDOS = batchDataFilterService.filterCompositeByBatchId(accuracyCompositeDOS, cityId, batchId, 1);
        List<AccuracyCompositeDO> finalAccuracyCompositeDOS = accuracyCompositeDOS;
        List<AccuracyAssessDO> finalAssessDOS = assessDOS;
        DateUtil.getListBetweenDay(startDate, endDate).forEach(date -> {
            String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyyMMdd");
            for (WeatherSourceEnum value : WeatherSourceEnum.values()) {
                if (WeatherSourceEnum.HIS.getCode().equals(value.getCode())) {
                    continue;
                }
                if (WeatherSourceEnum.BM.getCode().equals(value.getCode()) && !Constants.G_TRANSFORMER_ID.equals(algorithmId)) {
                    continue;
                }
                String code = value.getCode();
                List<String> strings = weatherAlgorithmMap.get(code);
                List<String> strings1 = algorithmIdMap.get(algorithmId);
                WeatherSourceAccuracyDTO weatherSourceAccuracyDTO = new WeatherSourceAccuracyDTO();
                weatherSourceAccuracyDTO.setAlgorithmName(value.getDescription());
                weatherSourceAccuracyDTO.setDateStr(DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd"));
                List<AccuracyCompositeDO> collect = finalAccuracyCompositeDOS.stream().filter(t->strings.contains(t.getAlgorithmId())).collect(Collectors.toList());
                List<AccuracyAssessDO> collect1 = finalAssessDOS.stream().filter(t->strings.contains(t.getAlgorithmId())).collect(Collectors.toList());
                Map<String, List<AccuracyCompositeDO>> collectCompositeDOS = new HashMap<>();
                Map<String, List<AccuracyAssessDO>> collectAssessDOS = new HashMap<>();
                if (CollectionUtils.isNotEmpty(collect)) {
                    collectCompositeDOS = collect.stream().collect(Collectors.groupingBy(t -> t.getAlgorithmId() +  "-" + DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
                }
                if (CollectionUtils.isNotEmpty(collect1)) {
                    collectAssessDOS = collect1.stream().collect(Collectors.groupingBy(t -> t.getAlgorithmId()  + "-" + DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
                }
                List<AccuracyCompositeDO> accuracyCompositeDOS1 = new ArrayList<>();
                List<AccuracyAssessDO> accuracyAssessDOS = new ArrayList<>();
                for (String algorithm : strings1) {
                    String key = algorithm + "-" + dateStr;
                    List<AccuracyCompositeDO> accuracyCompositeDOS2 = collectCompositeDOS.get(key);
                    List<AccuracyAssessDO> accuracyAssessDOS1 = collectAssessDOS.get(key);
                    if (CollectionUtils.isNotEmpty(accuracyCompositeDOS2)) {
                        accuracyCompositeDOS1 = accuracyCompositeDOS2;
                    }
                    if (CollectionUtils.isNotEmpty(accuracyAssessDOS1)) {
                        accuracyAssessDOS = accuracyAssessDOS1;
                    }
                }
                Map<String, List<AccuracyCompositeDO>> collect2 = new HashMap<>();
                if (CollectionUtils.isNotEmpty(accuracyCompositeDOS1)) {
                     collect2 = accuracyCompositeDOS1.stream().collect(Collectors.groupingBy(t -> t.getAccuracyName()));
                }
                Map<String, List<AccuracyAssessDO>> collect3 = new HashMap<>();
                if (CollectionUtils.isNotEmpty(accuracyAssessDOS)) {
                     collect3 = accuracyAssessDOS.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
                }
                if (MapUtils.isNotEmpty(collect2) && MapUtils.isNotEmpty(collect3)) {
                    weatherSourceAccuracyDTO.setSourceName(collect2.get("日综合").get(0).getAccuracyName());
                    weatherSourceAccuracyDTO.setDayBgdSourceName(collect3.get("日保供").get(0).getAssessName());
                    weatherSourceAccuracyDTO.setDayNightSourceName(collect3.get("日夜间低谷").get(0).getAssessName());
                    weatherSourceAccuracyDTO.setDaySourceName(collect3.get("日午间低谷").get(0).getAssessName());
                    weatherSourceAccuracyDTO.setDayMaxSourceName(collect3.get("日最大").get(0).getAssessName());
                    weatherSourceAccuracyDTO.setCompositeAccuracy(collect2.get("日综合").get(0).getAccuracy());
                    weatherSourceAccuracyDTO.setDayBgdAccuracy(collect3.get("日保供").get(0).getAccuracy());
                    weatherSourceAccuracyDTO.setDayTroughAccuracy(collect3.get("日午间低谷").get(0).getAccuracy());
                    weatherSourceAccuracyDTO.setDayNightTroughAccuracy(collect3.get("日夜间低谷").get(0).getAccuracy());
                    weatherSourceAccuracyDTO.setDayMaxAccuracy(collect3.get("日最大").get(0).getAccuracy());
                }
                result.add(weatherSourceAccuracyDTO);
            }
        });
        return result;
    }


    @Override
    public List<WeatherFcPointDTO> findSourceWeatherPointList(String cityId, Date startDate, Date endDate) throws Exception {
        List<WeatherFcPointDTO> weatherFcPointDTOS = new ArrayList<>();
        CityDO city = cityService.findCityById(cityId);
        cityId = cityService.findWeatherCityId(cityId);
        for (WeatherNewEnum weatherEnum : WeatherNewEnum.values()) {
            WeatherFcPointDTO weatherFcPointDTO = new WeatherFcPointDTO();
            weatherFcPointDTO.setCityId(cityId);
            weatherFcPointDTO.setCityName(city.getCity());
            weatherFcPointDTO.setType(String.valueOf(weatherEnum.getType()));
            List<BigDecimal> fc = weatherCityFcService
                    .findListPoint(startDate, endDate, cityId, weatherEnum.getType());
            weatherFcPointDTO.setFcPoint(fc);
            List<BigDecimal> his = weatherCityHisService
                    .findListPoint(startDate, endDate, cityId, weatherEnum.getType());
            weatherFcPointDTO.setHisPoint(his);
            weatherFcPointDTOS.add(weatherFcPointDTO);
        }
        return weatherFcPointDTOS;
    }

    private BigDecimal getBgdWeatherPeriodAccuracy(List<BigDecimal> fc, List<BigDecimal> his, String peakTimeStr, Integer type) throws Exception {
        BigDecimal bigDecimal = BigDecimal.ZERO;
        List<String> times = new ArrayList<String>();
        List<BigDecimal> bgdFcPeeks = new ArrayList<BigDecimal>();
        List<BigDecimal> bgdHisPeeks = new ArrayList<BigDecimal>();
        String[] startAndEndcolumns = peakTimeStr.split("-");
        List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO, false);
        times.addAll(columns);
        Map<String, BigDecimal> loadMap = ColumnUtil.listToMap(fc,false);
        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            column = column.substring(1);
            if (null != load) {
                if (times != null && times.contains(column)) {
                    bgdFcPeeks.add(load);
                }
            }
        }
        Map<String, BigDecimal> loadMap1 = ColumnUtil.listToMap(his,false);
        for (String column : loadMap1.keySet()) {
            BigDecimal load = loadMap1.get(column);
            column = column.substring(1);
            if (null != load) {
                if (times != null && times.contains(column)) {
                    bgdHisPeeks.add(load);
                }
            }
        }
        if (type == 2) {
            List<BigDecimal> bigDecimals = WeatherCalcUtil.calcPointAccuracy(bgdHisPeeks, bgdFcPeeks);
            bigDecimal = BigDecimalUtils.avgList(bigDecimals, bigDecimals.size(), false);
        } else if (type == 3 || type == 4) {
            bigDecimal = WeatherCalcUtil.calcSourceFeatureAccuracy(BigDecimalUtils.getMin(bgdHisPeeks), BigDecimalUtils.getMin(bgdFcPeeks));
        }
        return bigDecimal;
    }

    private WeatherFcNewAccuracyDTO getWeatherFeatureAccuracyDTO(WeatherFcNewAccuracyDTO weatherFcDTO,
                                                               List<BigDecimal> fcPoint, List<BigDecimal> hisPoint) {
        // 计算最大值准确率
        BigDecimal max = BigDecimalUtils.getMax(fcPoint);
        BigDecimal max1 = BigDecimalUtils.getMax(hisPoint);
        if (max!= null && max1!= null) {
            BigDecimal bigDecimals = WeatherCalcUtil.calcSourceFeatureAccuracy(max1, max);
            weatherFcDTO.setHighAccuracy(bigDecimals);
        }
        // 计算平均值准确率
        BigDecimal avg = BigDecimalUtils.avgList(fcPoint, fcPoint.size(), false);
        BigDecimal avg1 = BigDecimalUtils.avgList(hisPoint, hisPoint.size(), false);
        if (avg!= null && avg1!= null) {
            BigDecimal bigDecimals = WeatherCalcUtil.calcSourceFeatureAccuracy(avg1, avg);
            weatherFcDTO.setAveAccuracy(bigDecimals);
        }
        // 计算最小值准确率
        BigDecimal min = BigDecimalUtils.getMin(fcPoint);
        BigDecimal min1 = BigDecimalUtils.getMin(hisPoint);
        if (min!= null && min1!= null) {
            BigDecimal bigDecimals = WeatherCalcUtil.calcSourceFeatureAccuracy(min1, min);
            weatherFcDTO.setLowAccuracy(bigDecimals);
        }
        return weatherFcDTO;
    }

    private WeatherFcNewAccuracyDTO getWeatherRainAccuracyDTO(WeatherFcNewAccuracyDTO weatherFcDTO,
                                                                 List<BigDecimal> fcPoint, List<BigDecimal> hisPoint) {
        BigDecimal avg = BigDecimalUtils.addAllValue(fcPoint);
        BigDecimal avg1 = BigDecimalUtils.addAllValue(hisPoint);
        if (avg!= null && avg1!= null) {
            BigDecimal bigDecimals = WeatherCalcUtil.calcSourceFeatureAccuracy(avg1, avg);
            weatherFcDTO.setAccuracy(bigDecimals);
            weatherFcDTO.setHighAccuracy(bigDecimals);
            weatherFcDTO.setAveAccuracy(bigDecimals);
            weatherFcDTO.setLowAccuracy(bigDecimals);
        }
        return weatherFcDTO;
    }

    private WeatherDeviationDTO getWeatherDeviationAccuracyDTO(WeatherDeviationDTO weatherFcDTO,
                                                              List<BigDecimal> fcPoint, List<BigDecimal> hisPoint, Integer weatherFeature) {
        BigDecimal bigDecimal = null;
        if (weatherFeature == 1) {
           bigDecimal = WeatherCalcUtil.maxDeviation(hisPoint, fcPoint, true);
        } else if (weatherFeature == 2) {
            bigDecimal = WeatherCalcUtil.maxDeviation(hisPoint, fcPoint, false);
        }
        weatherFcDTO.setDeviation(bigDecimal);
        return weatherFcDTO;
    }

    private List<AccuracyLoadCityFcDO> getLoadSourceAccuracy(Map<String, List<LoadCityFcBatchDO>> collectFc, Map<String,
            List<LoadCityHisDO>> collectHis, List<Date> listBetweenDay, List<String> sources, List<String> strings) throws Exception {
        List<AccuracyLoadCityFcDO> result = new ArrayList<>();
        for (Date date : listBetweenDay) {
            String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyyMMdd");
            for (String source : sources) {
                List<LoadCityFcBatchDO> loadCityFcDOS = new ArrayList<>();
                AccuracyLoadCityFcDO accuracyLoadCityFcDO = new AccuracyLoadCityFcDO();
                accuracyLoadCityFcDO.setDate(new java.sql.Date(date.getTime()));
                if (WeatherSourceEnum.FC.getCode().equals(source)) {
                    accuracyLoadCityFcDO.setAlgorithmId(strings.get(0));
                    loadCityFcDOS = collectFc.get(strings.get(0) + "-" + dateStr);
                } else if (WeatherSourceEnum.METEO.getCode().equals(source)) {
                    accuracyLoadCityFcDO.setAlgorithmId(strings.get(1));
                    loadCityFcDOS = collectFc.get(strings.get(1) + "-" + dateStr);
                }
                List<LoadCityHisDO> loadCityHisDOS = collectHis.get(dateStr);
                if (CollectionUtils.isNotEmpty(loadCityFcDOS) && CollectionUtils.isNotEmpty(loadCityHisDOS)) {
                    List<BigDecimal> bigDecimalsFc = loadCityFcDOS.get(0).getloadList();
                    List<BigDecimal> bigDecimalsHis = loadCityHisDOS.get(0).getloadList();
                    List<BigDecimal> bigDecimals = WeatherCalcUtil.calcPointAccuracy(bigDecimalsFc, bigDecimalsHis);
                    if (CollectionUtils.isNotEmpty(bigDecimals)) {
                        BasePeriodUtils.setAllFiled(accuracyLoadCityFcDO,
                                ColumnUtil.listToMap(bigDecimals, Constants.LOAD_CURVE_START_WITH_ZERO));
                    }
                }
                result.add(accuracyLoadCityFcDO);
            }
        }
        return result;
    }

    private List<String> getPeriodLoadAccuracyList(List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS, List<String> strings) throws Exception {
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accuracyLoadCityFcDOS)) {
            Map<String, List<AccuracyLoadCityFcDO>> collect = accuracyLoadCityFcDOS.stream().filter(t->
                    t.getAlgorithmId() != null).collect(Collectors.groupingBy(t -> t.getAlgorithmId()));
            Map<Integer, Map<String, BigDecimal>> stringStringMap = new HashMap<>();
            for (String string : strings) {
                List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS1 = collect.get(string);
                if (CollectionUtils.isNotEmpty(accuracyLoadCityFcDOS1)) {
                    for (AccuracyLoadCityFcDO accuracyLoadCityFcDO : accuracyLoadCityFcDOS1) {
                        List<BigDecimal> bigDecimals = gen24List(accuracyLoadCityFcDO);
                        if (CollectionUtils.isNotEmpty(bigDecimals)) {
                            for (int i = 0; i < bigDecimals.size(); i++) {
                                Map<String, BigDecimal> map = stringStringMap.get(i);
                                if (null == map || map.isEmpty()) {
                                    Map<String, BigDecimal> map1 = new HashMap<>();
                                    map1.put(string, bigDecimals.get(i));
                                    stringStringMap.put(i, map1);
                                } else {
                                    BigDecimal bigDecimal = map.get(string);
                                    if (bigDecimal!= null) {
                                        if (bigDecimals.get(i).compareTo(bigDecimal) > 0) {
                                            map.remove(string);
                                            map.put(string, bigDecimals.get(i));
                                            stringStringMap.put(i, map);
                                        }
                                    } else {
                                        for (Map.Entry<String, BigDecimal> stringBigDecimalEntry : map.entrySet()) {
                                            BigDecimal value = stringBigDecimalEntry.getValue();
                                            if (bigDecimals.get(i).compareTo(value) > 0) {
                                                map.remove(stringBigDecimalEntry.getKey());
                                                map.put(string, bigDecimals.get(i));
                                                stringStringMap.put(i, map);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            stringStringMap.forEach((key, value) -> {
                value.forEach((key1, value1) -> {
                    list.add(weatherMap.get(key1));
                });
            });
        }
        return list;
    }

    private List<BigDecimal> gen24List(AccuracyLoadCityFcDO industryOriginDO) throws Exception {
        List<BigDecimal> list = new ArrayList<>(96);
        for (int i = 100; i < 2500; i+=100) {
            int length = String.valueOf(i).length();
            String methodName = "";
            if (length == 3) {
                methodName = "getT0"+ i;
            } else if (length == 4) {
                methodName = "getT"+ i;
            }
            Method method = industryOriginDO.getClass().getMethod(methodName);
            BigDecimal invoke = (BigDecimal) method.invoke(industryOriginDO);
            if (invoke != null) {
                list.add(invoke);
            } else {
                list.add(BigDecimal.valueOf(0.0));
            }
        }
        return list;
    }

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }
}

