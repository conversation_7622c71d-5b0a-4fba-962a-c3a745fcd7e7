package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcCopy1DO;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherCityFcDAO.java, v 0.1 2018-01-31 10:59:42 tao Exp $$
 */
@Component
public class WeatherCityFcCopy1DAO extends BaseAbstractDAO<WeatherCityFcCopy1DO> {

    public List<WeatherCityFcCopy1DO> findWeatherCityFcDO(String cityId, Integer type, Date startDate, Date endDate,String orderId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("1");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != orderId) {
            param.getQueryConditions().put("_ne_orderId", orderId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityFcCopy1DO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }

    public List<WeatherCityFcCopy1DO> findWeatherCityFcDOS(List<String> cityIds, Integer type, Date startDate, Date endDate,String orderId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("1");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (CollectionUtils.isNotEmpty(cityIds)) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (null != orderId) {
            param.getQueryConditions().put("_ne_orderId", orderId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityFcCopy1DO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }


    public List<WeatherCityFcCopy1DO> findWeatherCityFcDO(String cityId, Date startDate, Date endDate) throws Exception {
        return findWeatherCityFcDO(cityId,null,startDate,endDate,null);
    }
}