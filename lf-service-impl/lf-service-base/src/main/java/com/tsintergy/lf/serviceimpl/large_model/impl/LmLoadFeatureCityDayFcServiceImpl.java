package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.large_model.api.LmLoadFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLoadFeatureCityDayFcServiceDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmLoadFeatureCityDayFcServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/7 14:47
 **/
@Service("lmLoadFeatureCityDayFcService")
public class LmLoadFeatureCityDayFcServiceImpl implements LmLoadFeatureCityDayFcService {

    @Autowired
    private LmLoadFeatureCityDayFcServiceDAO lmLoadFeatureCityDayFcServiceDAO;
    @Override
    public void saveOrUpdateBatch(List<LmLoadFeatureCityDayFcServiceDO> list) {
        lmLoadFeatureCityDayFcServiceDAO.saveOrUpdateBatchByTemplate(list);
    }

    @Override
    public List<LmLoadFeatureCityDayFcServiceDO> find(String algorithmId, Date startDate, Date endDate) {
        return lmLoadFeatureCityDayFcServiceDAO.findAll(JpaWrappers.<LmLoadFeatureCityDayFcServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(algorithmId), LmLoadFeatureCityDayFcServiceDO::getAlgorithmId, algorithmId)
                .ge(LmLoadFeatureCityDayFcServiceDO::getDate, startDate)
                .le(LmLoadFeatureCityDayFcServiceDO::getDate, endDate));
    }
}
