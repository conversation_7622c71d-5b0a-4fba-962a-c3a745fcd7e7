/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.lf.core.enums.TenDaysTypeEnum;
import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcLongService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcLongDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayLongFcDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/17 15:02
 * @Version: 1.0.0
 */
@Slf4j
@Service("weatherFeatureCityDayLongFcService")
public class WeatherFeatureCityDayLongFcServiceImpl implements WeatherFeatureCityDayLongFcService {

    @Autowired
    WeatherFeatureCityDayLongFcDAO weatherFeatureCityDayLongFcDAO;

    @Autowired
    LoadCityFcLongService loadCityFcLongService;

    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    CityService cityService;

    @Autowired
    WeatherFeatureSourceTenDaysFcService weatherFeatureSourceTenDaysFcService;

    @Autowired
    WeatherFeatureSourceMonthFcService weatherFeatureSourceMonthFcService;

    @Autowired
    WeatherCityFcMeteoService weatherCityFcMeteoService;

    @Autowired
    WeatherCityFcBmService weatherCityFcBmService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Override
    public WeatherFeatureLongDTO findMonthFcPageByParam(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        WeatherFeatureLongDTO weatherFeatureLongDTO = new WeatherFeatureLongDTO();
        //查询相关数据
        List<WeatherFeatureCityDayLongFcDO> byParam = this.findByParam(cityId, startDate, endDate);

        List<LoadCityFcLongDO> loadCityFcLongDO = loadCityFcLongService
            .findLoadCityFcLongDO(cityId, AlgorithmEnum.LONG_FORECAST.getId(), caliberId, startDate, endDate);

        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService
            .listWeatherFeatureCityDayHisDO(cityId, DateUtils.addYears(startDate, -1), DateUtils.addYears(endDate, -1));
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, DateUtils.addYears(startDate, -1), DateUtils.addYears(endDate, -1),
                caliberId);
        Map<java.sql.Date, LoadCityFcLongDO> loadCityFcLongDOMap = new HashMap<>();
        Map<java.sql.Date, WeatherFeatureCityDayLongFcDO> weatherFeatureCityDayLongFcDOMap = new HashMap<>();
        Map<java.sql.Date, WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOMap = new HashMap<>();
        Map<java.sql.Date, LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOMap = new HashMap<>();
        if (byParam == null || loadCityFcLongDO == null || weatherFeatureCityDayHisDOS == null
            || loadFeatureCityDayHisDOS == null) {

        } else {
            loadCityFcLongDOMap = loadCityFcLongDO.stream()
                .collect(Collectors.toMap(LoadCityFcLongDO::getDate, Function.identity(), (o, v) -> v));

            weatherFeatureCityDayLongFcDOMap = byParam.stream()
                .collect(Collectors.toMap(WeatherFeatureCityDayLongFcDO::getDate, Function.identity(), (o, v) -> v));

            weatherFeatureCityDayHisDOMap = weatherFeatureCityDayHisDOS
                .stream()
                .collect(Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, Function.identity(), (o, v) -> v));

            loadFeatureCityDayHisDOMap = loadFeatureCityDayHisDOS.stream()
                .collect(Collectors.toMap(LoadFeatureCityDayHisDO::getDate, Function.identity(), (o, v) -> v));
        }

        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<WeatherFeatureCityDayLongDTO> longData = new ArrayList<>();
        for (Date date : listBetweenDay) {
            WeatherFeatureCityDayLongDTO weatherFeatureCityDayLongDTO = new WeatherFeatureCityDayLongDTO();
            LoadCityFcLongDO cityFcLongDO = loadCityFcLongDOMap.get(date);
            WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO = weatherFeatureCityDayLongFcDOMap
                .get(date);
            WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = weatherFeatureCityDayHisDOMap
                .get(DateUtils.addYears(date, -1));
            LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisDOMap
                .get(DateUtils.addYears(date, -1));

            WeatherFeatureCommonLongDTO fcObj = new WeatherFeatureCommonLongDTO();
            WeatherFeatureCommonLongDTO hisObj = new WeatherFeatureCommonLongDTO();
            WeatherFeatureCommonLongDTO modifyObj = new WeatherFeatureCommonLongDTO();
            if (cityFcLongDO != null) {
                fcObj.setAvgLoad(cityFcLongDO.getMeanLoad());
                fcObj.setDayEnergy(cityFcLongDO.getEleLoad());
                fcObj.setMaxLoad(cityFcLongDO.getMaxLoad());
                fcObj.setMinLoad(cityFcLongDO.getMinLoad());
                weatherFeatureCityDayLongDTO.setUpdateTime(
                    DateUtil.getStrDate(cityFcLongDO.getUpdatetime(), DateUtil.DATE_FORMAT11));
            }
            if (weatherFeatureCityDayLongFcDO != null) {
                fcObj.setHighestTemperature(weatherFeatureCityDayLongFcDO.getHighestTemperature());
                fcObj.setLowestTemperature(weatherFeatureCityDayLongFcDO.getLowestTemperature());
                fcObj.setId(weatherFeatureCityDayLongFcDO.getId());
            }
            if (loadFeatureCityDayHisDO != null) {
                hisObj.setAvgLoad(loadFeatureCityDayHisDO.getAveLoad());
                hisObj.setDayEnergy(loadFeatureCityDayHisDO.getEnergy());
                hisObj.setMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
                hisObj.setMinLoad(loadFeatureCityDayHisDO.getMinLoad());
            }
            if (weatherFeatureCityDayHisDO != null) {
                hisObj.setDate(DateUtil.getStrDate(weatherFeatureCityDayHisDO.getDate(), null));
                hisObj.setWeek(DateUtil.getWeek(weatherFeatureCityDayHisDO.getDate()));
                hisObj.setHighestTemperature(weatherFeatureCityDayHisDO.getHighestTemperature());
                hisObj.setLowestTemperature(weatherFeatureCityDayHisDO.getLowestTemperature());
            }
            if (loadFeatureCityDayHisDO != null) {
                modifyObj.setAvgLoad(loadFeatureCityDayHisDO.getAveLoad());
                modifyObj.setDayEnergy(loadFeatureCityDayHisDO.getEnergy());
                modifyObj.setMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
                modifyObj.setMinLoad(loadFeatureCityDayHisDO.getMinLoad());
            }
            if (weatherFeatureCityDayHisDO != null) {
                modifyObj.setWeek(DateUtil.getWeek(weatherFeatureCityDayHisDO.getDate()));
                modifyObj.setDate(DateUtil.getStrDate(weatherFeatureCityDayHisDO.getDate(), null));
                modifyObj.setHighestTemperature(weatherFeatureCityDayHisDO.getHighestTemperature());
                modifyObj.setLowestTemperature(weatherFeatureCityDayHisDO.getLowestTemperature());
            }
            fcObj.setDate(DateUtil.getStrDate(date, null));
            fcObj.setWeek(DateUtil.getWeek(date));
            weatherFeatureCityDayLongDTO.setFcObj(fcObj);
            weatherFeatureCityDayLongDTO.setHisObj(hisObj);
            weatherFeatureCityDayLongDTO.setModifyObj(modifyObj);
            longData.add(weatherFeatureCityDayLongDTO);
        }
        if (longData != null) {
            Optional<WeatherFeatureCityDayLongDTO> max = longData.stream().filter(e -> e.getUpdateTime() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayLongDTO::getUpdateTime));
            weatherFeatureLongDTO.setLongData(longData);
            if (!max.equals(Optional.empty())) {
                weatherFeatureLongDTO.setUpdateTime(max.get().getUpdateTime());
            }
        }
        return weatherFeatureLongDTO;
    }

    @Override
    public List<WeatherFeatureCityDayLongFcDO> findByParam(String cityId, Date startDate, Date endDate) throws
        Exception {
        List<WeatherFeatureCityDayLongFcDO> weatherFeatureCityDayFcDOs = weatherFeatureCityDayLongFcDAO
            .getWeatherFeatureCityDayFcDOs(cityId, startDate, endDate);
        return weatherFeatureCityDayFcDOs;
    }

    @Override
    public List<WeatherFeatureSourceLongDTO> findSourceByParam(String cityId, Integer type, String startDate, String endDate, String sourceType) throws Exception {
        List<WeatherFeatureSourceLongDTO> result = new ArrayList<>();
        CityDO cityById = cityService.findCityById(cityId);
        if (type == 1) {
            // 旬
            List<WeatherFeatureSourceTenDaysFcDO> byParam = weatherFeatureSourceTenDaysFcService.findByParam(cityId, startDate.replaceAll("-", ""), endDate.replaceAll("-", ""), sourceType);
            if (CollectionUtils.isNotEmpty(byParam)) {
                Map<String, List<WeatherFeatureSourceTenDaysFcDO>> collect = byParam.stream().collect(Collectors.groupingBy(t -> t.getYm() + "-" + t.getType()));
                DateUtil.getYearMonthList(startDate, endDate).forEach(e -> {
                    for (TenDaysTypeEnum value : TenDaysTypeEnum.values()) {
                        WeatherFeatureSourceLongDTO weatherFeatureSourceLongDTO = new WeatherFeatureSourceLongDTO();
                        weatherFeatureSourceLongDTO.setCityName(cityById.getCity());
                        weatherFeatureSourceLongDTO.setDateStr(e + "-" + value.getName());
                        for (WeatherSourceEnum valueSorce : WeatherSourceEnum.values()) {
                            if (valueSorce.getCode().equals(sourceType)) {
                                weatherFeatureSourceLongDTO.setSourceName(valueSorce.getDescription());
                            }
                        }
                        String key = e.replaceAll("-", "") + "-" + value.getName();
                        List<WeatherFeatureSourceTenDaysFcDO> weatherFeatureSourceTenDaysFcDOS = collect.get(key);
                        if (CollectionUtils.isNotEmpty(weatherFeatureSourceTenDaysFcDOS)) {
                            WeatherFeatureSourceTenDaysFcDO weatherFeatureSourceTenDaysFcDO = weatherFeatureSourceTenDaysFcDOS.get(0);
                            weatherFeatureSourceLongDTO.setHighTemp(weatherFeatureSourceTenDaysFcDO.getHighestTemperature());
                            weatherFeatureSourceLongDTO.setLowTemp(weatherFeatureSourceTenDaysFcDO.getLowestTemperature());
                            weatherFeatureSourceLongDTO.setAvgTemp(weatherFeatureSourceTenDaysFcDO.getAveTemperature());
                            weatherFeatureSourceLongDTO.setMaxHumidity(weatherFeatureSourceTenDaysFcDO.getHighestHumidity());
                            weatherFeatureSourceLongDTO.setMinHumidity(weatherFeatureSourceTenDaysFcDO.getLowestHumidity());
                            weatherFeatureSourceLongDTO.setAvgHumidity(weatherFeatureSourceTenDaysFcDO.getAveHumidity());
                            weatherFeatureSourceLongDTO.setRainfall(weatherFeatureSourceTenDaysFcDO.getRainfall());
                            weatherFeatureSourceLongDTO.setMaxWindSpeed(weatherFeatureSourceTenDaysFcDO.getMaxWinds());
                            weatherFeatureSourceLongDTO.setMinWindSpeed(weatherFeatureSourceTenDaysFcDO.getMinWinds());
                            weatherFeatureSourceLongDTO.setAvgWindSpeed(weatherFeatureSourceTenDaysFcDO.getAveWinds());
                            weatherFeatureSourceLongDTO.setAvgRadiance(weatherFeatureSourceTenDaysFcDO.getAveRadiance());
                        }
                        result.add(weatherFeatureSourceLongDTO);
                    }
                });
            }
        } else if (type == 2) {
            // 年
            List<WeatherFeatureSourceMonthFcDO> byParam = weatherFeatureSourceMonthFcService.findByParam(cityId, startDate + "01", endDate + "12", sourceType);
            if (CollectionUtils.isNotEmpty(byParam)) {
                Map<String, List<WeatherFeatureSourceMonthFcDO>> collect = byParam.stream().collect(Collectors.groupingBy(t -> t.getYm()));
                DateUtil.getYearMonthList(startDate+"-01", endDate+"-12").forEach(e -> {
                    WeatherFeatureSourceLongDTO weatherFeatureSourceLongDTO = new WeatherFeatureSourceLongDTO();
                    weatherFeatureSourceLongDTO.setDateStr(e);
                    weatherFeatureSourceLongDTO.setCityName(cityById.getCity());
                    for (WeatherSourceEnum valueSorce : WeatherSourceEnum.values()) {
                        if (valueSorce.getCode().equals(sourceType)) {
                            weatherFeatureSourceLongDTO.setSourceName(valueSorce.getDescription());
                        }
                    }
                    List<WeatherFeatureSourceMonthFcDO> weatherFeatureSourceMonthFcDOS = collect.get(e.replaceAll("-", ""));
                    if (CollectionUtils.isNotEmpty(weatherFeatureSourceMonthFcDOS)) {
                        weatherFeatureSourceMonthFcDOS.forEach(t -> {
                            weatherFeatureSourceLongDTO.setHighTemp(t.getHighestTemperature());
                            weatherFeatureSourceLongDTO.setLowTemp(t.getLowestTemperature());
                            weatherFeatureSourceLongDTO.setAvgTemp(t.getAveTemperature());
                            weatherFeatureSourceLongDTO.setMaxHumidity(t.getHighestHumidity());
                            weatherFeatureSourceLongDTO.setMinHumidity(t.getLowestHumidity());
                            weatherFeatureSourceLongDTO.setAvgHumidity(t.getAveHumidity());
                            weatherFeatureSourceLongDTO.setRainfall(t.getRainfall());
                            weatherFeatureSourceLongDTO.setMaxWindSpeed(t.getMaxWinds());
                            weatherFeatureSourceLongDTO.setMinWindSpeed(t.getMinWinds());
                            weatherFeatureSourceLongDTO.setAvgWindSpeed(t.getAveWinds());
                            weatherFeatureSourceLongDTO.setAvgRadiance(t.getAveRadiance());
                        });
                    }
                    result.add(weatherFeatureSourceLongDTO);
                });
            }
        } else {
            // 冬夏
            List<WeatherFeatureSourceMonthFcDO> byParam = weatherFeatureSourceMonthFcService.findByParam(cityId, startDate.replaceAll("-", ""), endDate.replaceAll("-", ""), sourceType);
            if (CollectionUtils.isNotEmpty(byParam)) {
                Map<String, List<WeatherFeatureSourceMonthFcDO>> collect = byParam.stream().collect(Collectors.groupingBy(t -> t.getYm()));
                List<String> yearMonthList = new ArrayList<>();
                if (type == 3) {
                    yearMonthList = DateUtil.getYearMonthList(startDate, endDate);
                } else if (type == 4) {
                    yearMonthList = DateUtil.getYearMonthList(startDate, endDate);
                }
                yearMonthList.forEach(e -> {
                    WeatherFeatureSourceLongDTO weatherFeatureSourceLongDTO = new WeatherFeatureSourceLongDTO();
                    weatherFeatureSourceLongDTO.setDateStr(e);
                    weatherFeatureSourceLongDTO.setCityName(cityById.getCity());
                    for (WeatherSourceEnum valueSorce : WeatherSourceEnum.values()) {
                        if (valueSorce.getCode().equals(sourceType)) {
                            weatherFeatureSourceLongDTO.setSourceName(valueSorce.getDescription());
                        }
                    }
                    List<WeatherFeatureSourceMonthFcDO> weatherFeatureSourceMonthFcDOS = collect.get(e.replaceAll("-", ""));
                    if (CollectionUtils.isNotEmpty(weatherFeatureSourceMonthFcDOS)) {
                        weatherFeatureSourceMonthFcDOS.forEach(t -> {
                            weatherFeatureSourceLongDTO.setHighTemp(t.getHighestTemperature());
                            weatherFeatureSourceLongDTO.setLowTemp(t.getLowestTemperature());
                            weatherFeatureSourceLongDTO.setAvgTemp(t.getAveTemperature());
                            weatherFeatureSourceLongDTO.setMaxHumidity(t.getHighestHumidity());
                            weatherFeatureSourceLongDTO.setMinHumidity(t.getLowestHumidity());
                            weatherFeatureSourceLongDTO.setAvgHumidity(t.getAveHumidity());
                            weatherFeatureSourceLongDTO.setRainfall(t.getRainfall());
                            weatherFeatureSourceLongDTO.setMaxWindSpeed(t.getMaxWinds());
                            weatherFeatureSourceLongDTO.setMinWindSpeed(t.getMinWinds());
                            weatherFeatureSourceLongDTO.setAvgWindSpeed(t.getAveWinds());
                            weatherFeatureSourceLongDTO.setAvgRadiance(t.getAveRadiance());
                        });
                    }
                    result.add(weatherFeatureSourceLongDTO);
                });
            }
        }
        return result;
    }

    @Override
    public WeatherFeatureSourceDTO findSourceFeatureByParam(String cityId, Integer type, String startDate, String endDate, String sourceType) throws Exception {
        WeatherFeatureSourceDTO weatherFeatureSourceDTO = new WeatherFeatureSourceDTO();
        CityDO cityById = cityService.findCityById(cityId);
        weatherFeatureSourceDTO.setCityName(cityById.getCity());
        for (WeatherSourceEnum value : WeatherSourceEnum.values()) {
            if (value.getCode().equals(sourceType)) {
                weatherFeatureSourceDTO.setSourceName(value.getDescription());
            }
        }
        if (type == 2) {
            // 年度
            weatherFeatureSourceDTO.setStartDate(startDate + "-01-01");
            weatherFeatureSourceDTO.setEndDate(endDate + "-12-31");
        } else {
            Date firstDayDateOfMonth = DateUtil.getFirstDayDateOfMonth(DateUtil.getDate(startDate + "-01", "yyyy-MM-dd"));
            Date firstDayDateOfMonth1 = DateUtil.getLastDayOfMonth(DateUtil.getDate(endDate + "-01", "yyyy-MM-dd"));
            weatherFeatureSourceDTO.setStartDate(DateUtil.getStrDate(firstDayDateOfMonth, "yyyy-MM-dd"));
            weatherFeatureSourceDTO.setEndDate(DateUtil.getStrDate(firstDayDateOfMonth1, "yyyy-MM-dd"));
        }
        for (WeatherSourceEnum valueSorce : WeatherSourceEnum.values()) {
            if (valueSorce.getCode().equals(sourceType)) {
                weatherFeatureSourceDTO.setSourceName(valueSorce.getDescription());
            }
        }
        List<WeatherFeatureSourceMonthFcDO> byParam1 = new ArrayList<>();
        if (type == 1) {
            List<WeatherFeatureSourceTenDaysFcDO> byParam = weatherFeatureSourceTenDaysFcService.findByParam(cityId, startDate.replaceAll("-", ""), endDate.replaceAll("-", ""), sourceType);
            if (CollectionUtils.isNotEmpty(byParam)) {
                BigDecimal reduce = byParam.stream().filter(t -> t.getHighestTemperature() != null)
                        .map(WeatherFeatureSourceTenDaysFcDO::getHighestTemperature)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal divide = reduce.divide(BigDecimal.valueOf(byParam.size()), 2, BigDecimal.ROUND_HALF_UP);
                weatherFeatureSourceDTO.setAvgHighTemp(divide);
                BigDecimal reduce1 = byParam.stream().filter(t -> t.getLowestTemperature() != null)
                        .map(WeatherFeatureSourceTenDaysFcDO::getLowestTemperature)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal divide1 = reduce1.divide(BigDecimal.valueOf(byParam.size()), 2, BigDecimal.ROUND_HALF_UP);
                weatherFeatureSourceDTO.setAvgLowTemp(divide1);

                Optional<WeatherFeatureSourceTenDaysFcDO> max = byParam.stream().max(Comparator.comparing(WeatherFeatureSourceTenDaysFcDO::getHighestTemperature));
                if (!max.equals(Optional.empty())) {
                    BigDecimal highestTemperature = max.get().getHighestTemperature();
                    java.sql.Date extremeHighestDate = max.get().getExtremeHighestDate();
                    weatherFeatureSourceDTO.setExtremeHighTempDate(highestTemperature + "（" + DateUtil.getStrDate(extremeHighestDate, "yyyy-MM-dd") + "）");
                }

                Optional<WeatherFeatureSourceTenDaysFcDO> min = byParam.stream().min(Comparator.comparing(WeatherFeatureSourceTenDaysFcDO::getLowestTemperature));
                if (!min.equals(Optional.empty())) {
                    BigDecimal lowestTemperature = min.get().getLowestTemperature();
                    java.sql.Date extremeLowestDate = min.get().getExtremeLowestDate();
                    weatherFeatureSourceDTO.setExtremeLowTempDate(lowestTemperature + "（" + DateUtil.getStrDate(extremeLowestDate, "yyyy-MM-dd") + "）");
                }

                BigDecimal reduce2 = byParam.stream().filter(t -> t.getAveHumidity() != null)
                        .map(WeatherFeatureSourceTenDaysFcDO::getAveHumidity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal divide2 = reduce2.divide(BigDecimal.valueOf(byParam.size()), 2, BigDecimal.ROUND_HALF_UP);
                weatherFeatureSourceDTO.setAvgHumidity(divide2);

                Optional<WeatherFeatureSourceTenDaysFcDO> max1 = byParam.stream().max(Comparator.comparing(WeatherFeatureSourceTenDaysFcDO::getMaxRainfall));
                if (!max1.equals(Optional.empty())) {
                    BigDecimal maxRainfall = max1.get().getMaxRainfall();
                    java.sql.Date extremeRainfallDate = max1.get().getMaxRainfallDate();
                    weatherFeatureSourceDTO.setRainfallDate(maxRainfall + "（" + DateUtil.getStrDate(extremeRainfallDate, "yyyy-MM-dd") + "）");
                }

                BigDecimal reduce3 = byParam.stream().filter(t -> t.getAveWinds()!= null)
                       .map(WeatherFeatureSourceTenDaysFcDO::getAveWinds)
                       .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal divide3 = reduce3.divide(BigDecimal.valueOf(byParam.size()), 2, BigDecimal.ROUND_HALF_UP);
                weatherFeatureSourceDTO.setAvgMaxWindSpeed(divide3);

                Optional<WeatherFeatureSourceTenDaysFcDO> max2 = byParam.stream().max(Comparator.comparing(WeatherFeatureSourceTenDaysFcDO::getMaxWinds));
                if (!max2.equals(Optional.empty())) {
                    BigDecimal maxWinds = max2.get().getMaxWinds();
                    java.sql.Date extremeMaxWindSpeedDate = max2.get().getExtremeMaxWindsDate();
                    weatherFeatureSourceDTO.setExtremeMaxWindSpeedDate(maxWinds + "（" + DateUtil.getStrDate(extremeMaxWindSpeedDate, "yyyy-MM-dd") + "）");
                }
            }
            return weatherFeatureSourceDTO;
        } else if (type == 2) {
            byParam1 = weatherFeatureSourceMonthFcService.findByParam(cityId, startDate + "01", endDate + "12", sourceType);
        } else {
            byParam1 = weatherFeatureSourceMonthFcService.findByParam(cityId, startDate.replaceAll("-", ""), endDate.replaceAll("-", ""), sourceType);
        }
        if (CollectionUtils.isNotEmpty(byParam1)) {
            BigDecimal reduce = byParam1.stream().filter(t -> t.getHighestTemperature() != null)
                    .map(WeatherFeatureSourceMonthFcDO::getHighestTemperature)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal divide = reduce.divide(BigDecimal.valueOf(byParam1.size()), 2, BigDecimal.ROUND_HALF_UP);
            weatherFeatureSourceDTO.setAvgHighTemp(divide);
            BigDecimal reduce1 = byParam1.stream().filter(t -> t.getLowestTemperature() != null)
                    .map(WeatherFeatureSourceMonthFcDO::getLowestTemperature)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal divide1 = reduce1.divide(BigDecimal.valueOf(byParam1.size()), 2, BigDecimal.ROUND_HALF_UP);
            weatherFeatureSourceDTO.setAvgLowTemp(divide1);

            Optional<WeatherFeatureSourceMonthFcDO> max = byParam1.stream().max(Comparator.comparing(WeatherFeatureSourceMonthFcDO::getHighestTemperature));
            if (!max.equals(Optional.empty())) {
                BigDecimal highestTemperature = max.get().getHighestTemperature();
                java.sql.Date extremeHighestDate = max.get().getExtremeHighestDate();
                weatherFeatureSourceDTO.setExtremeHighTempDate(highestTemperature + "（" + DateUtil.getStrDate(extremeHighestDate, "yyyy-MM-dd") + "）");
            }

            Optional<WeatherFeatureSourceMonthFcDO> min = byParam1.stream().min(Comparator.comparing(WeatherFeatureSourceMonthFcDO::getLowestTemperature));
            if (!min.equals(Optional.empty())) {
                BigDecimal lowestTemperature = min.get().getLowestTemperature();
                java.sql.Date extremeLowestDate = min.get().getExtremeLowestDate();
                weatherFeatureSourceDTO.setExtremeLowTempDate(lowestTemperature + "（" + DateUtil.getStrDate(extremeLowestDate, "yyyy-MM-dd") + "）");
            }

            BigDecimal reduce2 = byParam1.stream().filter(t -> t.getAveHumidity() != null)
                    .map(WeatherFeatureSourceMonthFcDO::getAveHumidity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal divide2 = reduce2.divide(BigDecimal.valueOf(byParam1.size()), 2, BigDecimal.ROUND_HALF_UP);
            weatherFeatureSourceDTO.setAvgHumidity(divide2);

            Optional<WeatherFeatureSourceMonthFcDO> max1 = byParam1.stream().max(Comparator.comparing(WeatherFeatureSourceMonthFcDO::getMaxRainfall));
            if (!max1.equals(Optional.empty())) {
                BigDecimal maxRainfall = max1.get().getMaxRainfall();
                java.sql.Date extremeRainfallDate = max1.get().getMaxRainfallDate();
                weatherFeatureSourceDTO.setRainfallDate(maxRainfall + "（" + DateUtil.getStrDate(extremeRainfallDate, "yyyy-MM-dd") + "）");
            }

            BigDecimal reduce3 = byParam1.stream().filter(t -> t.getAveWinds()!= null)
                    .map(WeatherFeatureSourceMonthFcDO::getAveWinds)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal divide3 = reduce3.divide(BigDecimal.valueOf(byParam1.size()), 2, BigDecimal.ROUND_HALF_UP);
            weatherFeatureSourceDTO.setAvgMaxWindSpeed(divide3);

            Optional<WeatherFeatureSourceMonthFcDO> max2 = byParam1.stream().max(Comparator.comparing(WeatherFeatureSourceMonthFcDO::getMaxWinds));
            if (!max2.equals(Optional.empty())) {
                BigDecimal maxWinds = max2.get().getMaxWinds();
                java.sql.Date extremeMaxWindSpeedDate = max2.get().getExtremeMaxWindsDate();
                weatherFeatureSourceDTO.setExtremeMaxWindSpeedDate(maxWinds + "（" + DateUtil.getStrDate(extremeMaxWindSpeedDate, "yyyy-MM-dd") + "）");
            }
        }
        return weatherFeatureSourceDTO;
    }

    @Override
    public void TenDaysFcLongFeature(Date startDate, Date endDate, String ym, String name, String code) throws Exception {
        List<WeatherCityFcDO> allByDateAndCityId;
        if (WeatherSourceEnum.FC.getCode().equals(code)) {
            allByDateAndCityId = weatherCityFcService.findWeatherCityFcDOs(null, null, startDate, endDate);
        } else {
            allByDateAndCityId = new ArrayList<>();
            if (WeatherSourceEnum.METEO.getCode().equals(code)) {
                List<WeatherCityFcMeteoDO> listByCondition = weatherCityFcMeteoService.getListByCondition(null, null, startDate, endDate);
                if (CollectionUtils.isNotEmpty(listByCondition)) {
                    listByCondition.forEach(t -> {
                        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                        BeanUtils.copyProperties(t, weatherCityFcDO);
                        allByDateAndCityId.add(weatherCityFcDO);
                    });
                }
            } else if (WeatherSourceEnum.BM.getCode().equals(code)) {
                List<WeatherCityFcBmDO> listByCondition = weatherCityFcBmService.getListByCondition(null, null, startDate, endDate);
                if (CollectionUtils.isNotEmpty(listByCondition)) {
                    listByCondition.forEach(t -> {
                        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                        BeanUtils.copyProperties(t, weatherCityFcDO);
                        allByDateAndCityId.add(weatherCityFcDO);
                    });
                }
            }
        }
        if (!CollectionUtils.isEmpty(allByDateAndCityId)) {
            Map<String, List<WeatherCityFcDO>> collect = allByDateAndCityId.stream().collect(Collectors.groupingBy(t -> t.getCityId()));

            for (Map.Entry<String, List<WeatherCityFcDO>> stringListEntry : collect.entrySet()) {
                // 最高温度
                BigDecimal highTem = null;
                Date highTemDate = null;
                // 最低温度
                BigDecimal lowTem = null;
                Date lowTemDate = null;
                // 平均温度
                List<BigDecimal> aveTem = new ArrayList<>();
                // 最大湿度
                BigDecimal maxHum = null;
                // 最小湿度
                BigDecimal lowHum = null;
                // 平均湿度
                List<BigDecimal> aveHum = new ArrayList<>();
                // 最大风速
                BigDecimal maxWind = null;
                Date maxWindDate = null;
                // 最小风速
                BigDecimal lowWind = null;
                // 平均风速
                List<BigDecimal> aveWind = new ArrayList<>();
                // 累积降雨
                BigDecimal maxRain = BigDecimal.ZERO;
                BigDecimal maxFlagRain = null;
                Date maxRainDate = null;
                String k = stringListEntry.getKey();
                List<WeatherCityFcDO> v = stringListEntry.getValue();
                if (!CollectionUtils.isEmpty(v)) {
                    WeatherFeatureSourceTenDaysFcDO weatherFeatureSourceTenDaysFcDO = new WeatherFeatureSourceTenDaysFcDO();
                    weatherFeatureSourceTenDaysFcDO.setCityId(k);
                    weatherFeatureSourceTenDaysFcDO.setYm(ym.replaceAll("-", ""));
                    weatherFeatureSourceTenDaysFcDO.setSourceType(code);
                    weatherFeatureSourceTenDaysFcDO.setType(name);
                    List<WeatherCityFcDO> collect1 = v.stream().filter(t -> 2 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect1)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect1) {
                            BigDecimal max = BigDecimalUtils.getMax(weatherCityFcDO.getWeatherList());
                            if (highTem == null || max.compareTo(highTem) > 0) {
                                highTem = max;
                                highTemDate = weatherCityFcDO.getDate();
                            }
                            BigDecimal min = BigDecimalUtils.getMin(weatherCityFcDO.getWeatherList());
                            if (lowTem == null || min.compareTo(lowTem) < 0) {
                                lowTem = min;
                                lowTemDate = weatherCityFcDO.getDate();
                            }
                            BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(weatherCityFcDO.getWeatherList());
                            aveTem.add(ave);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setHighestTemperature(highTem);
                    weatherFeatureSourceTenDaysFcDO.setLowestTemperature(lowTem);
                    if (highTemDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setExtremeHighestDate(new java.sql.Date(highTemDate.getTime()));
                    }
                    if (lowTemDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setExtremeLowestDate(new java.sql.Date(lowTemDate.getTime()));
                    }
                    if (!CollectionUtils.isEmpty(aveTem)) {
                        BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveTem);
                        weatherFeatureSourceTenDaysFcDO.setAveTemperature(ave);
                    }

                    List<WeatherCityFcDO> collect2 = v.stream().filter(t -> 1 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect2)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect2) {
                            BigDecimal max = BigDecimalUtils.getMax(weatherCityFcDO.getWeatherList());
                            if (maxHum == null || max.compareTo(maxHum) > 0) {
                                maxHum = max;
                            }
                            BigDecimal min = BigDecimalUtils.getMin(weatherCityFcDO.getWeatherList());
                            if (lowHum == null || min.compareTo(lowHum) < 0) {
                                lowHum = min;
                            }
                            BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(weatherCityFcDO.getWeatherList());
                            aveHum.add(ave);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setHighestHumidity(maxHum);
                    weatherFeatureSourceTenDaysFcDO.setLowestHumidity(lowHum);
                    if (!CollectionUtils.isEmpty(aveHum)) {
                        BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveHum);
                        weatherFeatureSourceTenDaysFcDO.setAveHumidity(ave);
                    }

                    List<WeatherCityFcDO> collect4 = v.stream().filter(t -> 4 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect4)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect4) {
                            BigDecimal max = BigDecimalUtils.getMax(weatherCityFcDO.getWeatherList());
                            if (maxWind == null || max.compareTo(maxWind) > 0) {
                                maxWind = max;
                                maxWindDate = weatherCityFcDO.getDate();
                            }
                            BigDecimal min = BigDecimalUtils.getMin(weatherCityFcDO.getWeatherList());
                            if (lowWind == null || min.compareTo(lowWind) < 0) {
                                lowWind = min;
                            }
                            BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(weatherCityFcDO.getWeatherList());
                            aveWind.add(ave);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setMaxWinds(maxWind);
                    if (maxWindDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setExtremeMaxWindsDate(new java.sql.Date(maxWindDate.getTime()));
                    }
                    weatherFeatureSourceTenDaysFcDO.setMinWinds(lowWind);
                    if (!CollectionUtils.isEmpty(aveWind)) {
                        BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveWind);
                        weatherFeatureSourceTenDaysFcDO.setAveWinds(ave);
                    }

                    List<WeatherCityFcDO> collect3 = v.stream().filter(t -> 3 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect3)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect3) {
                            List<BigDecimal> weatherList = weatherCityFcDO.getWeatherList();
                            BigDecimal sum = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listSum(weatherList);
                            if (maxFlagRain == null || maxFlagRain.compareTo(sum) < 0) {
                                maxFlagRain = sum;
                                maxRainDate = weatherCityFcDO.getDate();
                            }
                            maxRain = maxRain.add(sum);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setRainfall(maxRain);
                    weatherFeatureSourceTenDaysFcDO.setMaxRainfall(maxFlagRain);
                    if (maxRainDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setMaxRainfallDate(new java.sql.Date(maxRainDate.getTime()));
                    }
                    try {
                        weatherFeatureSourceTenDaysFcService.saveOrUpdate(weatherFeatureSourceTenDaysFcDO);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
    }

    @Override
    public void MonthFcLongFeature(Date startDate, Date endDate, String ym, String code) throws Exception {
        List<WeatherCityFcDO> allByDateAndCityId;
        if (WeatherSourceEnum.FC.getCode().equals(code)) {
            allByDateAndCityId = weatherCityFcService.findWeatherCityFcDOs(null, null, startDate, endDate);
        } else {
            allByDateAndCityId = new ArrayList<>();
            if (WeatherSourceEnum.METEO.getCode().equals(code)) {
                List<WeatherCityFcMeteoDO> listByCondition = weatherCityFcMeteoService.getListByCondition(null, null, startDate, endDate);
                if (CollectionUtils.isNotEmpty(listByCondition)) {
                    listByCondition.forEach(t -> {
                        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                        BeanUtils.copyProperties(t, weatherCityFcDO);
                        allByDateAndCityId.add(weatherCityFcDO);
                    });
                }
            } else if (WeatherSourceEnum.BM.getCode().equals(code)) {
                List<WeatherCityFcBmDO> listByCondition = weatherCityFcBmService.getListByCondition(null, null, startDate, endDate);
                if (CollectionUtils.isNotEmpty(listByCondition)) {
                    listByCondition.forEach(t -> {
                        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                        BeanUtils.copyProperties(t, weatherCityFcDO);
                        allByDateAndCityId.add(weatherCityFcDO);
                    });
                }
            }
        }
        if (!CollectionUtils.isEmpty(allByDateAndCityId)) {
            Map<String, List<WeatherCityFcDO>> collect = allByDateAndCityId.stream().collect(Collectors.groupingBy(t -> t.getCityId()));
            for (Map.Entry<String, List<WeatherCityFcDO>> stringListEntry : collect.entrySet()) {
                // 最高温度
                BigDecimal highTem = null;
                Date highTemDate = null;
                // 最低温度
                BigDecimal lowTem = null;
                Date lowTemDate = null;
                // 平均温度
                List<BigDecimal> aveTem = new ArrayList<>();
                // 最大湿度
                BigDecimal maxHum = null;
                // 最小湿度
                BigDecimal lowHum = null;
                // 平均湿度
                List<BigDecimal> aveHum = new ArrayList<>();
                // 最大风速
                BigDecimal maxWind = null;
                Date maxWindDate = null;
                // 最小风速
                BigDecimal lowWind = null;
                // 平均风速
                List<BigDecimal> aveWind = new ArrayList<>();
                // 累积降雨
                BigDecimal maxRain = BigDecimal.ZERO;
                BigDecimal maxFlagRain = null;
                Date maxRainDate = null;
                String k = stringListEntry.getKey();
                List<WeatherCityFcDO> v = stringListEntry.getValue();
                if (!CollectionUtils.isEmpty(v)) {
                    WeatherFeatureSourceMonthFcDO weatherFeatureSourceTenDaysFcDO = new WeatherFeatureSourceMonthFcDO();
                    weatherFeatureSourceTenDaysFcDO.setCityId(k);
                    weatherFeatureSourceTenDaysFcDO.setYm(ym.replaceAll("-", ""));
                    weatherFeatureSourceTenDaysFcDO.setSourceType(code);
                    List<WeatherCityFcDO> collect1 = v.stream().filter(t -> 2 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect1)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect1) {
                            BigDecimal max = BigDecimalUtils.getMax(weatherCityFcDO.getWeatherList());
                            if (highTem == null || max.compareTo(highTem) > 0) {
                                highTem = max;
                                highTemDate = weatherCityFcDO.getDate();
                            }
                            BigDecimal min = BigDecimalUtils.getMin(weatherCityFcDO.getWeatherList());
                            if (lowTem == null || min.compareTo(lowTem) < 0) {
                                lowTem = min;
                                lowTemDate = weatherCityFcDO.getDate();
                            }
                            BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(weatherCityFcDO.getWeatherList());
                            aveTem.add(ave);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setHighestTemperature(highTem);
                    weatherFeatureSourceTenDaysFcDO.setLowestTemperature(lowTem);
                    if (highTemDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setExtremeHighestDate(new java.sql.Date(highTemDate.getTime()));
                    }
                    if (lowTemDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setExtremeLowestDate(new java.sql.Date(lowTemDate.getTime()));
                    }
                    if (!CollectionUtils.isEmpty(aveTem)) {
                        BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveTem);
                        weatherFeatureSourceTenDaysFcDO.setAveTemperature(ave);
                    }

                    List<WeatherCityFcDO> collect2 = v.stream().filter(t -> 1 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect2)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect2) {
                            BigDecimal max = BigDecimalUtils.getMax(weatherCityFcDO.getWeatherList());
                            if (maxHum == null || max.compareTo(maxHum) > 0) {
                                maxHum = max;
                            }
                            BigDecimal min = BigDecimalUtils.getMin(weatherCityFcDO.getWeatherList());
                            if (lowHum == null || min.compareTo(lowHum) < 0) {
                                lowHum = min;
                            }
                            BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(weatherCityFcDO.getWeatherList());
                            aveHum.add(ave);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setHighestHumidity(maxHum);
                    weatherFeatureSourceTenDaysFcDO.setLowestHumidity(lowHum);
                    if (!CollectionUtils.isEmpty(aveHum)) {
                        BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveHum);
                        weatherFeatureSourceTenDaysFcDO.setAveHumidity(ave);
                    }

                    List<WeatherCityFcDO> collect4 = v.stream().filter(t -> 4 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect4)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect4) {
                            BigDecimal max = BigDecimalUtils.getMax(weatherCityFcDO.getWeatherList());
                            if (maxWind == null || max.compareTo(maxWind) > 0) {
                                maxWind = max;
                                maxWindDate = weatherCityFcDO.getDate();
                            }
                            BigDecimal min = BigDecimalUtils.getMin(weatherCityFcDO.getWeatherList());
                            if (lowWind == null || min.compareTo(lowWind) < 0) {
                                lowWind = min;
                            }
                            BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(weatherCityFcDO.getWeatherList());
                            aveWind.add(ave);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setMaxWinds(maxWind);
                    if (maxWindDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setExtremeMaxWindsDate(new java.sql.Date(maxWindDate.getTime()));
                    }
                    weatherFeatureSourceTenDaysFcDO.setMinWinds(lowWind);
                    if (!CollectionUtils.isEmpty(aveWind)) {
                        BigDecimal ave = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveWind);
                        weatherFeatureSourceTenDaysFcDO.setAveWinds(ave);
                    }

                    List<WeatherCityFcDO> collect3 = v.stream().filter(t -> 3 == t.getType()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect3)) {
                        for (WeatherCityFcDO weatherCityFcDO : collect3) {
                            List<BigDecimal> weatherList = weatherCityFcDO.getWeatherList();
                            BigDecimal sum = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listSum(weatherList);
                            if (maxFlagRain == null || maxFlagRain.compareTo(sum) < 0) {
                                maxFlagRain = sum;
                                maxRainDate = weatherCityFcDO.getDate();
                            }
                            maxRain = maxRain.add(sum);
                        }
                    }
                    weatherFeatureSourceTenDaysFcDO.setRainfall(maxRain);
                    weatherFeatureSourceTenDaysFcDO.setMaxRainfall(maxFlagRain);
                    if (maxRainDate != null) {
                        weatherFeatureSourceTenDaysFcDO.setMaxRainfallDate(new java.sql.Date(maxRainDate.getTime()));
                    }
                    try {
                        weatherFeatureSourceMonthFcService.saveOrUpdate(weatherFeatureSourceTenDaysFcDO);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
    }

    @Override
    public void saveOrUpdate(WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO) throws Exception {
        List<WeatherFeatureCityDayLongFcDO> weatherFeatureCityDayFcDOs = weatherFeatureCityDayLongFcDAO
            .getWeatherFeatureCityDayFcDOs(weatherFeatureCityDayLongFcDO.getCityId(),
                weatherFeatureCityDayLongFcDO.getDate(), weatherFeatureCityDayLongFcDO.getDate());

        if (CollectionUtils.isEmpty(weatherFeatureCityDayFcDOs)) {
            weatherFeatureCityDayLongFcDAO.createAndFlush(weatherFeatureCityDayLongFcDO);
        } else {
            WeatherFeatureCityDayLongFcDO oldVo = weatherFeatureCityDayFcDOs.get(0);
            String id = oldVo.getId();
            BeanUtils.copyProperties(weatherFeatureCityDayLongFcDO, oldVo);
            oldVo.setId(id);
            weatherFeatureCityDayLongFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureCityDayLongFcDAO.updateAndFlush(oldVo);
        }
    }

    @Override
    public void saveOrUpdate(WeatherFeatureCityDayLongListVO weatherFeatureCityDayLongListVO) throws Exception {
        for (WeatherFeatureCommonLongVO weatherFeatureCommonLongDTO : weatherFeatureCityDayLongListVO.getFcList()) {
            WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO = new WeatherFeatureCityDayLongFcDO();
            weatherFeatureCityDayLongFcDO.setId(weatherFeatureCommonLongDTO.getId());
            weatherFeatureCityDayLongFcDO
                .setDate(new java.sql.Date(DateUtil.getDate(weatherFeatureCommonLongDTO.getDate(), null).getTime()));
            weatherFeatureCityDayLongFcDO.setCityId(weatherFeatureCommonLongDTO.getCityId());
            weatherFeatureCityDayLongFcDO.setHighestTemperature(weatherFeatureCommonLongDTO.getHighestTemperature());
            weatherFeatureCityDayLongFcDO.setLowestTemperature(weatherFeatureCommonLongDTO.getLowestTemperature());
            weatherFeatureCityDayLongFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            if (weatherFeatureCommonLongDTO.getId() == null) {
                weatherFeatureCityDayLongFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                weatherFeatureCityDayLongFcDAO.save(weatherFeatureCityDayLongFcDO);
            } else {
                weatherFeatureCityDayLongFcDAO.update(weatherFeatureCityDayLongFcDO);
            }


        }


    }

    @Override
    public void saveOrUpdateList(List<WeatherFeatureCityDayLongFcDO> fcDOS) {
        if (CollectionUtils.isNotEmpty(fcDOS)) {
            fcDOS.forEach(fcDO -> {
                try {
                    saveOrUpdate(fcDO);
                } catch (Exception e) {
                    log.error("保存中长期气象特性异常",e);
                }
            });
        }
    }
}
