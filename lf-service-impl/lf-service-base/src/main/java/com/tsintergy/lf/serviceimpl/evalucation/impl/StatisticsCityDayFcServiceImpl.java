
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.analyze.dto.StatisticsDeviationAnalyzeDTO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityDayAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.MultipleAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsCityDayDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcDAO;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version $Id: StatisticsCityDayFcServiceImpl.java, v 0.1 2018-01-31 10:20:10 tao Exp $$
 */

@Service("statisticsCityDayFcService")
public class StatisticsCityDayFcServiceImpl extends BaseServiceImpl implements StatisticsCityDayFcService {

    private static final Logger logger = LogManager.getLogger(StatisticsCityDayFcServiceImpl.class);

    @Autowired
    private StatisticsCityDayFcDAO statisticsCityDayFcDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    private SettingCheckService settingCheckService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Override
    public List<StatisticsCityDayFcDO> queryStatisticsCityDayFcDO(Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_report", true);
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.setPageSize("0");
        try {
            return statisticsCityDayFcDAO.query(param).getDatas();
        } catch (BusinessException e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<MultipleAccuracyDTO> getMultipleDayAccuracyList(Map<String, String> cityMap, String caliberId,
        String algorithmId, Date startDate, Date endDate) throws Exception {
        List<MultipleAccuracyDTO> multipleAccuracyList = new ArrayList<>();
        for (Map.Entry<String, String> city : cityMap.entrySet()) {
            List<StatisticsCityDayFcDO> statisticsCityDayFcDOs = statisticsCityDayFcDAO
                .getStatisticsCityDayFcDOs(city.getKey(), caliberId, null, startDate, endDate, true);
            if (CollectionUtils.isEmpty(statisticsCityDayFcDOs)) {
                continue;
            }

            // 免考查询
            Map<String, List<Date>> passCheckMap = settingCheckService.findPassCheckMap(city.getKey());

            //计算时间区间内平均准确率
            MultipleAccuracyDTO multipleAccuracyDTO = new MultipleAccuracyDTO();
            BigDecimal avgAccuracy = new BigDecimal("0.0000");
            int passNumber = statisticsCityDayFcDOs.size();
            int checkNumber = statisticsCityDayFcDOs.size();
            for (StatisticsCityDayFcDO statisticsCityDayFcDO : statisticsCityDayFcDOs) {
                if (passCheckMap.get(city.getKey()) != null && passCheckMap.get(city.getKey())
                    .contains(statisticsCityDayFcDO.getDate())) {
                    checkNumber--;
                    continue;
                }
                if (statisticsCityDayFcDO.getPass()!=null&&statisticsCityDayFcDO.getPass().compareTo(BigDecimal.ZERO) == 0) {
                    passNumber--;
                }
                avgAccuracy = avgAccuracy.add(statisticsCityDayFcDO.getAccuracy());
            }

            BigDecimal passRate = new BigDecimal(Integer.toString(passNumber))
                .divide(new BigDecimal(statisticsCityDayFcDOs.size()), 4, BigDecimal.ROUND_HALF_UP);
            if (checkNumber != 0) {
                avgAccuracy = avgAccuracy.divide(new BigDecimal(checkNumber), 4, BigDecimal.ROUND_HALF_UP);
                multipleAccuracyDTO.setAveAccuracy(avgAccuracy);
            }
            multipleAccuracyDTO.setPassRate(passRate);
            multipleAccuracyDTO.setCityId(city.getKey());
            multipleAccuracyDTO.setName(city.getValue());
            multipleAccuracyList.add(multipleAccuracyDTO);

        }

        return multipleAccuracyList;
    }

    @Override
    public List<StatisticsCityDayFcDO> queryStatisticsCity(String cityId, String algorithmId, String caliberId,
        Date date, Boolean report) throws Exception {
        DBQueryParam autoQueryParam = DBQueryParamBuilder.create().build();
        autoQueryParam.getQueryConditions().put("_se_cityId", cityId);
        autoQueryParam.getQueryConditions().put("_se_algorithmId", algorithmId);
        autoQueryParam.getQueryConditions().put("_se_caliberId", caliberId);
        if (report != null) {
            autoQueryParam.getQueryConditions().put("_ne_report", report);
        }
        autoQueryParam.getQueryConditions().put("_de_date",
            new java.sql.Date(date.getTime()));
        autoQueryParam.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        autoQueryParam.setPageSize("0");
        return statisticsCityDayFcDAO.query(autoQueryParam).getDatas();
    }

    @Override
    public List<StatisticsCityDayFcDO> findStatisticsByDate(String cityId, String algorithmId, String caliberId,
        Date startDate, Date endDate, Boolean report) throws Exception {
        DBQueryParamBuilder paramBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (caliberId != null) {
            paramBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (algorithmId != null) {
            paramBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (caliberId != null) {
            paramBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (startDate != null && endDate != null) {
            paramBuilder.where(QueryOp.DateNoLessThan, "date", startDate)
                .where(QueryOp.DateNoMoreThan, "date", endDate);
        }
        if (report != null) {
            paramBuilder.where(QueryOp.NumberEqualTo, "report", report);
        }
        return statisticsCityDayFcDAO.query(paramBuilder.build()).getDatas();
    }

    @Override
    public List<StatisticsCityDayFcDO> getDayAccuracyList(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate) throws Exception {
        List<StatisticsCityDayFcDO> StatisticsCityDayFcDOs = statisticsCityDayFcDAO
            .getStatisticsCityDayFcDOs(cityId, caliberId, algorithmId, startDate, endDate, null);
        return StatisticsCityDayFcDOs;
    }

    @Override
    public List<StatisticsCityDayFcDO> getDayAccuracyList(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate, Boolean isReport) throws Exception {
        List<StatisticsCityDayFcDO> StatisticsCityDayFcDOs = statisticsCityDayFcDAO
            .getStatisticsCityDayFcDOs(cityId, caliberId, algorithmId, startDate, endDate, isReport);
        return StatisticsCityDayFcDOs;
    }

    @Override
    public StatisticsCityDayFcDO doCreate(StatisticsCityDayFcDO vo) throws Exception {
        try {
            return (StatisticsCityDayFcDO) statisticsCityDayFcDAO
                .create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveStatisticsCityDayFcDO(StatisticsCityDayFcDO vo) throws Exception {
        try {
            statisticsCityDayFcDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveStatisticsCityDayFcDOByPK(Serializable pk) throws Exception {
        try {
            statisticsCityDayFcDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public StatisticsCityDayFcDO doUpdateStatisticsCityDayFcDO(StatisticsCityDayFcDO vo) throws Exception {
        try {
            return (StatisticsCityDayFcDO) statisticsCityDayFcDAO
                .update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public StatisticsCityDayFcDO findStatisticsCityDayFcDOByPk(Serializable pk) throws Exception {
        try {
            return (StatisticsCityDayFcDO) statisticsCityDayFcDAO
                .findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public StatisticsDeviationAnalyzeDTO findStatisticsDeviationAnalyzeDTO(String cityId, String caliberId,
        Date startTime, Date endTime) throws Exception {

        //查询所有上报的预测统计
        List<StatisticsCityDayFcDO> StatisticsCityDayFcDOs = statisticsCityDayFcDAO
            .getStatisticsCityDayFcDOs(cityId, caliberId, null, startTime, endTime, true);
        StatisticsDeviationAnalyzeDTO statisticsDeviationAnalyzeDTO = null;
        if (StatisticsCityDayFcDOs.size() > 1) {
            //人工修正准确率
            List<BigDecimal> repairAccuracys = new ArrayList<BigDecimal>();
            //自动预测准确率
            List<BigDecimal> autoAccuracys = new ArrayList<BigDecimal>();
            //上报准确率
            List<BigDecimal> reportAccuracys = new ArrayList<BigDecimal>();
            //人工修正合格率
            List<BigDecimal> repairPss = new ArrayList<BigDecimal>();
            //自动预测合格率
            List<BigDecimal> autoPss = new ArrayList<BigDecimal>();
            //最终上报合格率
            List<BigDecimal> reportPss = new ArrayList<BigDecimal>();

            int autoCount = 0;
            for (StatisticsCityDayFcDO vo : StatisticsCityDayFcDOs) {
                if (AlgorithmConstants.MD_ALGORITHM_ID.equals(vo.getAlgorithmId())) {
                    repairAccuracys.add(vo.getAccuracy());
                    repairPss.add(vo.getPass());
                } else {
                    autoAccuracys.add(vo.getAccuracy());
                    autoPss.add(vo.getPass());
                    autoCount++;
                }
                //最终上报的
                reportAccuracys.add(vo.getAccuracy());
                reportPss.add(vo.getPass());
            }
            statisticsDeviationAnalyzeDTO = new StatisticsDeviationAnalyzeDTO();
            //人工修正准确率
            statisticsDeviationAnalyzeDTO.setAccuracyRepairRatio(BigDecimalUtils.avgList(repairAccuracys, 2, false));
            //自动预测准确率
            statisticsDeviationAnalyzeDTO.setAccuracyAutoRatio(BigDecimalUtils.avgList(autoAccuracys, 2, false));
            //上报准确率
            statisticsDeviationAnalyzeDTO.setAccuracyReportRatio(BigDecimalUtils.avgList(reportAccuracys, 2, false));
            //人工修正合格率
            statisticsDeviationAnalyzeDTO.setPassRepairRatio(BigDecimalUtils.avgList(repairPss, 2, true));
            //自动预测合格率
            statisticsDeviationAnalyzeDTO.setPassAutoRatio(BigDecimalUtils.avgList(autoPss, 2, true));
            //最终上报合格率
            statisticsDeviationAnalyzeDTO.setPassReportRatio(BigDecimalUtils.avgList(reportPss, 2, true));

            //自动预测天数占的比例
            statisticsDeviationAnalyzeDTO.setCountAutoRatio(
                (new BigDecimal(autoCount).divide(new BigDecimal(StatisticsCityDayFcDOs.size()), 2, BigDecimal.ROUND_UP)
                    .multiply(new BigDecimal(100))));
        }
        return statisticsDeviationAnalyzeDTO;
    }


    @Override
    public List<StatisticsCityDayFcDO> getReportAccuracy(String cityId, String caliberId, Date startTime, Date endTime)
        throws Exception {
        List<StatisticsCityDayFcDO> StatisticsCityDayFcDOs = statisticsCityDayFcDAO
            .getStatisticsCityDayFcDOs(cityId, caliberId, null, startTime, endTime, true);
        if (StatisticsCityDayFcDOs == null && StatisticsCityDayFcDOs.size() != 1) {
            throw TsieExceptionUtils.newBusinessException(
                "查询昨日最终上报准确率数据条数为" + StatisticsCityDayFcDOs == null ? "null" : StatisticsCityDayFcDOs.size() + "");
        }
        return StatisticsCityDayFcDOs;
    }


    @Override
    public StatisticsCityDayDTO getAccuracyAvg(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        StatisticsCityDayDTO dayDTO = new StatisticsCityDayDTO();
        List<StatisticsCityDayFcDO> reportList = getReportAccuracy(cityId, caliberId, startDate, endDate);
        List<StatisticsCityDayFcDO> algorithmList = statisticsCityDayFcDAO
            .getStatisticsCityDayFcDOs(cityId, caliberId, algorithmId, startDate, endDate, null);
        if (reportList.size() < 1 && algorithmList.size() < 1) {
            return null;
        }
        //求平均值
        List<BigDecimal> report = reportList.stream().map(StatisticsCityDayFcDO::getAccuracy)
            .collect(Collectors.toList());
        List<BigDecimal> algorithm = algorithmList.stream().map(StatisticsCityDayFcDO::getAccuracy)
            .collect(Collectors.toList());
        BigDecimal reportAccuracy = BigDecimalUtils.avgList(report, 4, true);
        BigDecimal algorithmAccuracy = BigDecimalUtils.avgList(algorithm, 4, true);
        dayDTO.setFcAccuracy(algorithmAccuracy);
        dayDTO.setReportAccuracy(reportAccuracy);
        return dayDTO;
    }


    @Override
    public List<StatisticsDayDTO> getDayAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        List<StatisticsDayDTO> statisticsDayDTOS = new ArrayList<>();
        List<StatisticsCityDayFcDO> reportList = getReportAccuracy(cityId, caliberId, startDate, endDate);
        List<StatisticsCityDayFcDO> algorithmList = statisticsCityDayFcDAO
            .getStatisticsCityDayFcDOs(cityId, caliberId, algorithmId, startDate, endDate, null);
        Map<Date, StatisticsCityDayFcDO> reportMap = reportList.stream()
            .collect(Collectors.toMap(StatisticsCityDayFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, StatisticsCityDayFcDO> algorithmMap = algorithmList.stream()
            .collect(Collectors.toMap(StatisticsCityDayFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : dateList) {
            StatisticsCityDayFcDO report = reportMap.get(date);
            StatisticsCityDayFcDO algorithm = algorithmMap.get(date);
            if (report == null && algorithm == null) {
                continue;
            }
            StatisticsDayDTO statisticsDayDTO = new StatisticsDayDTO();
            statisticsDayDTO.setDate(date);
            if (report != null) {
                statisticsDayDTO.setReport(report.getAccuracy().multiply(new BigDecimal(100)));
            }
            if (algorithm != null) {
                statisticsDayDTO.setAlgorithm(algorithm.getAccuracy());
            }
            statisticsDayDTOS.add(statisticsDayDTO);
        }
        return statisticsDayDTOS;
    }

    @Override
    public List<StatisticsCityDayFcDO> getDayAlgorithmsAccuracy(String cityId, String caliberId, List<String> algorithmIds, Date startDate, Date endDate) throws Exception {
        List<StatisticsCityDayFcDO> algorithmList = statisticsCityDayFcDAO
                .getStatisticsCityAlgorithmDayFcDOs(cityId, caliberId, algorithmIds, startDate, endDate);
        return algorithmList;
    }

    @Override
    public List<StatisticsCityDayFcDO> doSaveOrUpdateStatisticsCityDayFcDOs(
        List<StatisticsCityDayFcDO> statisticsCityDayFcVOS) throws Exception {

        return statisticsCityDayFcDAO.doSaveOrUpdateStatisticsCityDayFcDOs(statisticsCityDayFcVOS);
    }

    /**
     * 获取地势准确率排名 List
     */
    @Override
    public List<CityDayAccuracyDTO> getCityDayAccuracyDTOList(List<String> cityIds, String caliberId, String algorithmId, Date date) {
        try {
            List<StatisticsCityDayFcDO> statisticsCityDayFcDOList = statisticsCityDayFcDAO.getStatisticsCityDayFcDOs(cityIds,caliberId,"0",date,null);
            Map<String, List<StatisticsCityDayFcDO>> statisticsCityDayFcDOMap = new HashMap<>();
            if (statisticsCityDayFcDOList != null){
                statisticsCityDayFcDOMap = statisticsCityDayFcDOList.stream().collect(Collectors.groupingBy(StatisticsCityDayFcDO::getCityId));
            }
            ArrayList<CityDayAccuracyDTO> cityDayAccuracyDTOList = new ArrayList<>(cityIds.size());
            for (String cityId : cityIds){
                CityDayAccuracyDTO cityDayAccuracyDTO = new CityDayAccuracyDTO();
                cityDayAccuracyDTO.setCityName(cityService.findCityById(cityId).getCity() + "市");
                List<StatisticsCityDayFcDO> cityDayFcDOList = statisticsCityDayFcDOMap.get(cityId);
                if (cityDayFcDOList != null){
                    StatisticsCityDayFcDO dayFcDO = cityDayFcDOList.get(0);
                    if (dayFcDO != null){
                        BigDecimal accuracy = dayFcDO.getAccuracy();
                        if (accuracy != null){
                            cityDayAccuracyDTO.setAccuracy(accuracy);
                        }
                        BigDecimal standardAccuracy = dayFcDO.getStandardAccuracy();
                        if (standardAccuracy != null){
                            cityDayAccuracyDTO.setStandard(standardAccuracy);
                        }
                    }
                }
                cityDayAccuracyDTOList.add(cityDayAccuracyDTO);
            }
            // 把集合根据 standard 降序排列
            ArrayList<CityDayAccuracyDTO> sortList = new ArrayList<>(cityDayAccuracyDTOList.size());
            for (CityDayAccuracyDTO dto : cityDayAccuracyDTOList){
                if (dto.getStandard() != null && dto.getAccuracy() != null){
                    sortList.add(dto);
                }
            }

            for(int i = 0;i<sortList.size()-1;i++){
                for (int j=0;j<sortList.size()-i-1;j++){
                    if (sortList.get(j).getStandard().compareTo(sortList.get(j+1).getStandard()) == 1){
                        CityDayAccuracyDTO dayAccuracyDTO = sortList.get(j);
                        sortList.set(j,sortList.get(j+1));
                        sortList.set(j+1,dayAccuracyDTO);
                    }else {
                        if (sortList.get(j).getStandard().compareTo(sortList.get(j+1).getStandard()) == 0){
                            if (sortList.get(j).getAccuracy().compareTo(sortList.get(j+1).getAccuracy()) == 1){
                                CityDayAccuracyDTO accuracyDTO = sortList.get(j);
                                sortList.set(j,sortList.get(j+1));
                                sortList.set(j+1,accuracyDTO);
                            }
                        }
                    }
                }
            }

            return sortList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
