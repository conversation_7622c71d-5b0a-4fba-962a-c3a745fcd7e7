package com.tsintergy.lf.serviceimpl.evalucation.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.AlgorithmAccuracyEnum;
import com.tsintergy.lf.core.enums.CompareRuleEnum;
import com.tsintergy.lf.core.enums.DateTypeEnum;
import com.tsintergy.lf.core.enums.WeatherSeceneEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingCompositeAccuracyService;
import com.tsintergy.lf.serviceapi.base.assess.dto.EquationDTO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyCompositeService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DailyForecastSceneService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAlgorithmAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.ForecastDeviationDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.ForecastSceneAccracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DailyForecastSceneDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingForecastSceneService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingForecastSceneDO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.AccessStatisResultDTO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.DailyForecastSceneDAO;
import com.tsintergy.lf.serviceimpl.ultra.forecast.impl.BaseServiceImpl;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("dailyForecastSceneService")
public class DailyForecastSceneServiceImpl extends BaseServiceImpl implements DailyForecastSceneService {

    @Autowired
    private DailyForecastSceneDAO dailyForecastSceneDAO;

    @Autowired
    private SettingForecastSceneService settingForecastSceneService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private HolidayService holidayService;

    @Resource
    private AccuracyCompositeService accuracyCompositeService;

    @Autowired
    private SettingAssessService settingAssessService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private AccuracyAssessService accuracyAssessService;

    @Autowired
    private SettingCompositeAccuracyService settingCompositeAccuracyService;

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private CityService cityService;

    @Override
    public List<DailyForecastSceneDO> findDailyForecastScene(String cityId, Date startDate, Date endDate, Integer dateType,
                                                             Integer weatherType) {
        return dailyForecastSceneDAO.findAll(JpaWrappers.<DailyForecastSceneDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(cityId), DailyForecastSceneDO::getCityId, cityId)
                .ge(startDate != null, DailyForecastSceneDO::getStartDate, startDate)
                .le(endDate != null, DailyForecastSceneDO::getEndDate, endDate)
                .eq(dateType != null, DailyForecastSceneDO::getDateType, dateType)
                .eq(weatherType != null, DailyForecastSceneDO::getWeatherType, weatherType));
    }

    @Override
    public void saveOrUpdate(List<DailyForecastSceneDO> dailyForecastSceneList) throws Exception {
        int batchSize = 100;
        for (int i = 0; i < dailyForecastSceneList.size(); i += batchSize) {
            List<DailyForecastSceneDO> subList = dailyForecastSceneList.subList(i, Math.min(dailyForecastSceneList.size(), i + batchSize));
            dailyForecastSceneDAO.saveOrUpdateBatch(subList);
        }
    }

    @Override
    public void doStatDailyForecastScene(String cityId, String settingId) throws Exception {
        List<SettingForecastSceneDO> settingByYear = new ArrayList<>();
        if (StrUtil.isNotBlank(settingId)) {
            settingByYear = settingForecastSceneService.findSettingSceneListById(settingId);
        } else {
            settingByYear = settingForecastSceneService.findSettingSceneDOList(null);
        }
        if (CollectionUtil.isEmpty(settingByYear)) {
            return;
        }
        List<HolidayDO> allHolidayVOS = holidayService.getAllHolidayVOS();
        Date queryStartDate = null;
        Date queryEndDate = null;
        Map<String, List<Date>> allDateMap = new HashMap<>();
        for (SettingForecastSceneDO setting : settingByYear) {
            List<Date> allDateList = new ArrayList<>();
            List<Integer> year = setting.getYear();
            List<Integer> month = setting.getMonth();
            for (Integer y : year) {
                for (Integer m : month) {
                    allDateList.addAll(DateUtil.getDateListByYearAndMonth(y, m));
                }
            }
            if (CollectionUtil.isEmpty(allDateList)) {
                continue;
            }
            allDateList = allDateList.stream().distinct().sorted(Comparator.comparing(Date::getTime)).collect(Collectors.toList());
            Date minDate = allDateList.stream().min(Comparator.comparing(Date::getTime)).get();
            Date maxDate = allDateList.stream().max(Comparator.comparing(Date::getTime)).get();
            if (ObjectUtil.isNull(queryStartDate) || queryStartDate.after(minDate)) {
                queryStartDate = minDate;
            }
            if (ObjectUtil.isNull(queryEndDate) || queryEndDate.before(maxDate)) {
                queryEndDate = maxDate;
            }
            allDateMap.put(setting.getId(), allDateList);
        }
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
                cityId, queryStartDate, queryEndDate);
        if (CollectionUtil.isEmpty(weatherFeatureCityDayHisDOS)) {
            return;
        }
        Map<String, List<WeatherFeatureCityDayHisDO>> weatherMap = weatherFeatureCityDayHisDOS.stream().collect(
                Collectors.groupingBy(WeatherFeatureCityDayHisDO::getCityId));
        List<DailyForecastSceneDO> saveList = new ArrayList<>();
        for (String featureId : weatherMap.keySet()) {
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOList = weatherMap.get(featureId);
            for (SettingForecastSceneDO setting : settingByYear) {
                List<Date> allDateList = allDateMap.get(setting.getId());
                List<Date> dateListByDateType = getDateListByDateType(allDateList, setting.getDateType(), allHolidayVOS);
                List<DailyForecastSceneDO> dailyForecastSceneDOList = getForecastSeceneMatch(setting, dateListByDateType,
                        weatherFeatureCityDayHisDOList, allDateList, featureId);
                if (CollectionUtil.isEmpty(dailyForecastSceneDOList)){
                    continue;
                }
                saveList.addAll(dailyForecastSceneDOList);
            }
        }
        List<String> settingIdList = settingByYear.stream().map(SettingForecastSceneDO::getId).collect(Collectors.toList());
        deleteDailyForecastScene(settingIdList);
        saveOrUpdate(saveList);
    }

    @Override
    public ForecastSceneAccracyDTO getCityAvgAccuracyList(String cityId, String caliberId, Date startDate, Date endDate,
                                                          WeatherSeceneEnum weatherSecene, DateTypeEnum dateType, List<String> algorithmId) {
        List<DailyForecastSceneDO> dailyForecastScene = findDailyForecastScene(cityId, startDate, endDate, dateType.getId(), weatherSecene.getId());
        if (CollectionUtil.isEmpty(dailyForecastScene)) {
            return null;
        }
        List<Date> datelist = new ArrayList<>();
        String dateInterval = "";
        for (DailyForecastSceneDO dailyForecastSceneDO : dailyForecastScene) {
            datelist.addAll(DateUtil.getListBetweenDay(dailyForecastSceneDO.getStartDate(), dailyForecastSceneDO.getEndDate()));
            dateInterval = dateInterval.concat(DateUtil.formateDate(dailyForecastSceneDO.getStartDate())).concat("~").concat(DateUtil.formateDate(dailyForecastSceneDO.getEndDate())).concat("、");
        }
        if (StrUtil.isNotBlank(dateInterval)) {
            dateInterval = dateInterval.substring(0, dateInterval.lastIndexOf("、"));
        }
        ForecastSceneAccracyDTO forecastSceneAccracyDTO = new ForecastSceneAccracyDTO();
        forecastSceneAccracyDTO.setWeatherScene(weatherSecene.getText());
        forecastSceneAccracyDTO.setDateInterval(dateInterval);
        List<EquationDTO> allEquation = settingCompositeAccuracyService.findAllEquation(DateUtil.getYearByDate(startDate), caliberId, true);
        List<CityAccuracyDTO> filterItemList = getAccuracyList(cityId, caliberId, startDate, endDate, datelist, dateType, allEquation.get(0).getAccuracyName());
        if (CollectionUtil.isEmpty(filterItemList)) {
            return forecastSceneAccracyDTO;
        }
        if (CollectionUtil.isNotEmpty(algorithmId)) {
            for (CityAccuracyDTO cityAccuracyDTO : filterItemList) {
                List<AlgorithmAccuracyDTO> collect = cityAccuracyDTO.getAlgorithmDetail().stream().filter(dto -> algorithmId.contains(dto.getAlgorithmId())).collect(Collectors.toList());
                cityAccuracyDTO.setAlgorithmDetail(collect);
            }
        }
        List<AlgorithmAccuracyDTO> cityAvgAccuracy = accuracyCompositeService.getCityAvgAccuracy(filterItemList);
        cityAvgAccuracy.sort(Comparator.comparing(dto -> dto.getOrderNo()));
        forecastSceneAccracyDTO.setAvgAccuracyList(cityAvgAccuracy);
        return forecastSceneAccracyDTO;
    }

    private List<CityAccuracyDTO> getAccuracyList(String cityId, String caliberId, Date startDate, Date endDate,
                                                  List<Date> datelist, DateTypeEnum dateType, String accuracyName) {
        List<CityAccuracyDTO> accuracyList = accuracyCompositeService.getCityAccuracy(cityId, caliberId, accuracyName, startDate, endDate,
                DateTypeEnum.HOLIDAY.getId().equals(dateType.getId()) ? "1" : "0", "1", 1);
        if (CollectionUtil.isEmpty(accuracyList)) {
            return null;
        }
        List<String> dateStrList = datelist.stream().map(x -> DateUtil.formateDate(x)).collect(Collectors.toList());
        List<CityAccuracyDTO> filterItemList = accuracyList.stream()
                .filter(x -> dateStrList.contains(DateUtil.formateDate(x.getDate()))).collect(Collectors.toList());
        return filterItemList;
    }

    @Override
    public List<CityAccuracyDTO> getCityAccuracyList(String cityId, String caliberId, Date startDate, Date endDate,
                                                     WeatherSeceneEnum weatherSecene, DateTypeEnum dateType, String algorithmId, String accuracyName) {
        List<DailyForecastSceneDO> dailyForecastScene = findDailyForecastScene(cityId, startDate, endDate, dateType.getId(), weatherSecene.getId());
        if (CollectionUtil.isEmpty(dailyForecastScene)) {
            return null;
        }
        List<Date> datelist = new ArrayList<>();
        for (DailyForecastSceneDO dailyForecastSceneDO : dailyForecastScene) {
            datelist.addAll(DateUtil.getListBetweenDay(dailyForecastSceneDO.getStartDate(), dailyForecastSceneDO.getEndDate()));
        }
        List<CityAccuracyDTO> accuracyList = getAccuracyList(cityId, caliberId, startDate, endDate, datelist, dateType, accuracyName);
        if (CollectionUtil.isEmpty(accuracyList)) {
            return null;
        }
        for (CityAccuracyDTO cityAccuracyDTO : accuracyList) {
            List<AlgorithmAccuracyDTO> algorithmDetail = cityAccuracyDTO.getAlgorithmDetail();
            List<AlgorithmAccuracyDTO> filterDetail = algorithmDetail.stream().filter(x -> x.getAlgorithmId().equals(algorithmId)).collect(Collectors.toList());
            cityAccuracyDTO.setAlgorithmDetail(filterDetail);
        }
        accuracyList.sort(Comparator.comparing(CityAccuracyDTO::getDate));
        return accuracyList;
    }

    @Override
    public List<CityAlgorithmAccuracyDTO> getCityAlgorithmAccuracyList(String cityId, String caliberId, Date startDate, Date endDate, WeatherSeceneEnum weatherSecene, DateTypeEnum dateType, List<String> algorithmIds, String accuracyName) throws Exception {
        List<CityAlgorithmAccuracyDTO> result = new ArrayList<>();
        List<StatisticsCityDayFcDO> dayAlgorithmsAccuracy = statisticsCityDayFcService.getDayAlgorithmsAccuracy(cityId, caliberId, algorithmIds, startDate, endDate);
        CityDO cityById = cityService.findCityById(cityId);
        List<DailyForecastSceneDO> dailyForecastScene = findDailyForecastScene(cityId, startDate, endDate, dateType.getId(), weatherSecene.getId());
        if (CollectionUtil.isEmpty(dailyForecastScene)) {
            return null;
        }
        List<Date> datelist = new ArrayList<>();
        for (DailyForecastSceneDO dailyForecastSceneDO : dailyForecastScene) {
            datelist.addAll(DateUtil.getListBetweenDay(dailyForecastSceneDO.getStartDate(), dailyForecastSceneDO.getEndDate()));
        }
        if (CollectionUtil.isEmpty(datelist)) {
            return null;
        }
        for (AlgorithmAccuracyEnum value : AlgorithmAccuracyEnum.values()) {
            CityAlgorithmAccuracyDTO cityAlgorithmAccuracyDTO = new CityAlgorithmAccuracyDTO();
            cityAlgorithmAccuracyDTO.setName(value.getName());
            Map<String, List<BigDecimal>> algorithmAccuracyMap = new HashMap<>();
            Map<String, String> algorithmMap = new HashMap<>();
            List<CityAccuracyDTO> accuracyList = getAccuracyList(cityId, caliberId, startDate, endDate, datelist,
                    dateType, value.getName());
            if (CollectionUtil.isNotEmpty(accuracyList)) {
                for (CityAccuracyDTO cityAccuracyDTO : accuracyList) {
                    Map<String, List<AlgorithmAccuracyDTO>> collect = cityAccuracyDTO.getAlgorithmDetail().stream().filter(x -> algorithmIds.contains(x.getAlgorithmId())).collect(Collectors.groupingBy(AlgorithmAccuracyDTO::getAlgorithmId));
                    for (Map.Entry<String, List<AlgorithmAccuracyDTO>> stringListEntry : collect.entrySet()) {
                        String key = stringListEntry.getKey();
                        algorithmMap.put(key, stringListEntry.getValue().get(0).getName());
                        if (algorithmAccuracyMap.containsKey(key)) {
                            List<AlgorithmAccuracyDTO> algorithmAccuracyDTOS = stringListEntry.getValue();
                            List<BigDecimal> accuracyList1 = algorithmAccuracyDTOS.stream().map(AlgorithmAccuracyDTO::getAccuracy).collect(Collectors.toList());
                            algorithmAccuracyMap.get(key).addAll(accuracyList1);
                        } else {
                            List<BigDecimal> accuracyList1 = stringListEntry.getValue().stream().map(AlgorithmAccuracyDTO::getAccuracy).collect(Collectors.toList());
                            algorithmAccuracyMap.put(key, accuracyList1);
                        }
                    }
                }
            }
            String maxKey = "";
            BigDecimal maxValue = BigDecimal.ZERO;
            for (Map.Entry<String, List<BigDecimal>> stringListEntry : algorithmAccuracyMap.entrySet()) {
                if (CollectionUtil.isNotEmpty(stringListEntry.getValue()) && stringListEntry.getValue().size() > 1) {
                    BigDecimal reduce = stringListEntry.getValue().stream().filter(Objects::nonNull) // 过滤掉 null 元素
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal average = reduce.divide(BigDecimal.valueOf(stringListEntry.getValue().size()), 4, BigDecimal.ROUND_HALF_UP);
                    if (average.compareTo(maxValue) > 0) {
                        maxKey = stringListEntry.getKey();
                        maxValue = average;
                    }
                }
            }
            String algorithmCn = algorithmMap.get(maxKey);
            if (algorithmCn.contains("{单位}")) {
                algorithmCn = algorithmCn.replace("{单位}", cityById.getCity());
                cityAlgorithmAccuracyDTO.setAlgorithmName(algorithmCn);
            }
            cityAlgorithmAccuracyDTO.setAlgorithmName(algorithmCn);
            cityAlgorithmAccuracyDTO.setAccuracy(maxValue);
            result.add(cityAlgorithmAccuracyDTO);
        }
        // 96点准确率
        CityAlgorithmAccuracyDTO cityAlgorithmAccuracyDTO = new CityAlgorithmAccuracyDTO();
        cityAlgorithmAccuracyDTO.setName("日96点");
        if (!CollectionUtil.isEmpty(dayAlgorithmsAccuracy)) {
            String maxKey = "";
            BigDecimal maxValue = BigDecimal.ZERO;
            Map<String, List<StatisticsCityDayFcDO>> collect = dayAlgorithmsAccuracy.stream().filter(x -> algorithmIds.contains(x.getAlgorithmId())).collect(Collectors.groupingBy(t -> t.getAlgorithmId()));
            for (Map.Entry<String, List<StatisticsCityDayFcDO>> stringListEntry : collect.entrySet()) {
                List<StatisticsCityDayFcDO> value = stringListEntry.getValue();
                if (CollectionUtil.isNotEmpty(value)) {
                    BigDecimal reduce = value.stream().map(StatisticsCityDayFcDO::getAccuracy).filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal average = reduce.divide(BigDecimal.valueOf(value.size()), 2, BigDecimal.ROUND_HALF_UP);
                    if (average.compareTo(maxValue) > 0) {
                        maxKey = stringListEntry.getKey();
                        maxValue = average;
                    }
                }
            }
            String algorithmCn = algorithmService.getAlgorithmCn(maxKey);
            cityAlgorithmAccuracyDTO.setAccuracy(maxValue);
            if (algorithmCn.contains("{单位}")) {
                algorithmCn = algorithmCn.replace("{单位}", cityById.getCity());
                cityAlgorithmAccuracyDTO.setAlgorithmName(algorithmCn);
            } else {
                cityAlgorithmAccuracyDTO.setAlgorithmName(algorithmCn);
            }
            result.add(cityAlgorithmAccuracyDTO);
        }
        return result;
    }

    @Override
    public List<ForecastDeviationDTO> getForecastDeviationList(String cityId, String caliberId, Date startDate, Date endDate,
                                                               WeatherSeceneEnum weatherSecene, DateTypeEnum dateType,
                                                               String algorithmId, String accuracyName) throws Exception {
        List<DailyForecastSceneDO> dailyForecastScene = findDailyForecastScene(cityId, startDate, endDate, dateType.getId(), weatherSecene.getId());
        if (CollectionUtil.isEmpty(dailyForecastScene)) {
            return null;
        }
        List<Date> datelist = new ArrayList<>();
        for (DailyForecastSceneDO dailyForecastSceneDO : dailyForecastScene) {
            datelist.addAll(DateUtil.getListBetweenDay(dailyForecastSceneDO.getStartDate(), dailyForecastSceneDO.getEndDate()));
        }
        Map<String, List<SettingAssessDO>> assessSettingByData =
                settingAssessService.findAssessSettingByData(startDate, endDate, caliberId);
        List<LoadCityFcDO> fcList = loadCityFcService.findFcByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate);
        List<LoadCityHisDO> hisList = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, datelist, caliberId);
        if (CollectionUtil.isEmpty(fcList) || CollectionUtil.isEmpty(hisList)) {
            return null;
        }
        Map<String, LoadCityFcDO> fcMap = fcList.stream().collect(Collectors.toMap( x -> DateUtil.formateDate(x.getDate()),
                Function.identity(), (key1, key2) -> key2));
        Map<String, LoadCityHisDO> hisDOMap = hisList.stream().collect(Collectors.toMap(x -> DateUtil.formateDate(x.getDate()),
                Function.identity(), (key1, key2) -> key2));
        datelist.sort(Comparator.comparing(Date::getTime));
        List<String> columns = ColumnUtil.getColumns(Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO, false);
        List<ForecastDeviationDTO> result = new ArrayList<>();
        for (Date date : datelist) {
            String formatDate = DateUtil.formateDate(date);
            LoadCityFcDO loadCityFcDO = fcMap.get(formatDate);
            LoadCityHisDO loadCityHisDO = hisDOMap.get(formatDate);
            List<BigDecimal> fcLoadList = BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
            List<BigDecimal> hisLoadList = BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
            String dateToStrFORMAT = DateUtil.getDateToStrFORMAT(date, "yyyy-MM");
            List<SettingAssessDO> settingAssessDOList = assessSettingByData.get(dateToStrFORMAT);
            Optional<SettingAssessDO> noonMinSetting = settingAssessDOList.stream().filter(x -> x.getAssessName().equals(accuracyName)).findFirst();
            ForecastDeviationDTO deviationDTO = new ForecastDeviationDTO();
            deviationDTO.setDate(date);
            if (noonMinSetting.isPresent()) {
                AccessStatisResultDTO fcLoad = null;
                AccessStatisResultDTO hisLoad = null;
                if (CollectionUtil.isNotEmpty(fcLoadList)) {
                    fcLoad = accuracyAssessService.getAccessLoad(fcLoadList, noonMinSetting.get(), columns);
                }
                if (CollectionUtil.isNotEmpty(hisLoadList)) {
                    hisLoad = accuracyAssessService.getAccessLoad(hisLoadList, noonMinSetting.get(), columns);
                }
                if (fcLoad != null) {
                    deviationDTO.setFcValue(fcLoad.getResult());
                }
                if (hisLoad != null) {
                    deviationDTO.setHisValue(hisLoad.getResult());
                }
                if (deviationDTO.getFcValue() != null && deviationDTO.getHisValue() != null) {
                    deviationDTO.setDeviation(deviationDTO.getFcValue().subtract(deviationDTO.getHisValue()));
                }
            }
            result.add(deviationDTO);
        }
        return result;
    }

    @Override
    public void deleteDailyForecastScene(List<String> settingId) {
        dailyForecastSceneDAO.delete(JpaWrappers.<DailyForecastSceneDO>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(settingId), DailyForecastSceneDO::getSettingId, settingId));
    }

    private DateTypeEnum getDateType(Date date, List<HolidayDO> holidayList) {
        DateTypeEnum day_type = DateTypeEnum.WORKDAY;
        if (DateUtil.isWeekend(date)) {
            day_type = DateTypeEnum.RESTDAY;
        }
        if (CollectionUtil.isNotEmpty(holidayList)) {
            Optional<HolidayDO> holidayDOOptional = holidayList.stream().filter(t -> !date.before(t.getStartDate()) && !date.after(t.getEndDate())).findFirst();
            if (holidayDOOptional.isPresent()) {
                day_type = DateTypeEnum.HOLIDAY;
            }
            Optional<List<String>> offDateOp = holidayList.stream().filter(t -> ObjectUtil.isNotNull(t.getOffDates()))
                    .map(t -> Arrays.asList(t.getOffDates().split(","))).filter(x -> x.contains(DateUtil.formateDate(date)))
                    .findFirst();
            if (offDateOp.isPresent()) {
                day_type = DateTypeEnum.WORKDAY;
            }
        }
        return day_type;
    }

    private List<Date> getDateListByDateType(List<Date> allDateList, Integer dateType, List<HolidayDO> allHolidayVOS) {
        if (dateType == DateTypeEnum.ALL.getId()){
            return allDateList;
        }
        List<Date> result = new ArrayList<>();
        for (Date date : allDateList) {
            DateTypeEnum dailyType = getDateType(date, allHolidayVOS);
            if (dailyType.getId() == dateType) {
                result.add(date);
            }
        }
        return result;
    }

    private List<DailyForecastSceneDO> getForecastSeceneMatch(SettingForecastSceneDO setting, List<Date> dateListByDateType,
                                                              List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS,
                                                              List<Date> allDateList, String featureId) throws Exception {
        Map<Date, BigDecimal> weatherFeatureMap = getFeatureValue(setting.getWeatherIndex(), weatherFeatureCityDayHisDOS, dateListByDateType);
        List<DailyForecastSceneDO> dailyForecastSeceneList = getDailyForecastSceneByFeatureValue(weatherFeatureMap, setting.getContinuousDayValue()
                , setting.getContinuousDayCompareRule(), setting.getConditionCompareRule(), setting.getConditionValue(), allDateList);
        if (CollectionUtil.isNotEmpty(dailyForecastSeceneList)) {
            dailyForecastSeceneList.forEach(x -> {
                x.setWeatherType(setting.getWeatherType());
                x.setDateType(setting.getDateType());
                x.setCityId(featureId);
                x.setSettingId(setting.getId());
            });
        }
        return dailyForecastSeceneList;
    }

    private Map<Date, BigDecimal> getFeatureValue(Integer weatherIndex, List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS, List<Date> dateListByDateType) throws Exception {
        Map<String, WeatherFeatureCityDayHisDO> featureMap = weatherFeatureCityDayHisDOS.stream()
                .collect(Collectors.toMap( x -> DateUtil.formateDate(x.getDate()), Function.identity(), (o, v) -> v));
        Map<Date, BigDecimal> resultMap = new HashMap<>();
        for (Date date : dateListByDateType) {
            WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = featureMap.get(DateUtil.formateDate(date));
            if (weatherFeatureCityDayHisDO == null) {
                continue;
            }
            BigDecimal featureValue = null;
            WeatherFeatureCityDayHisDO yesterdayWeatherFeature = null;
            switch (weatherIndex) {
                case 1:
                    featureValue = weatherFeatureCityDayHisDO.getHighestTemperature();
                    break;
                case 2:
                    yesterdayWeatherFeature = featureMap.get(DateUtils.addDays(date, -1));
                    if (yesterdayWeatherFeature == null) {
                        yesterdayWeatherFeature = weatherFeatureCityDayHisService.findWeatherFeatureCityHisVOByDate(weatherFeatureCityDayHisDOS.get(0).getCityId(), DateUtils.addDays(date, -1));
                    }
                    if (yesterdayWeatherFeature == null) {
                        break;
                    }
                    featureValue = BigDecimalFunctions.subtract(weatherFeatureCityDayHisDO.getHighestTemperature(), yesterdayWeatherFeature.getHighestTemperature());
                    break;
                case 3:
                    featureValue = weatherFeatureCityDayHisDO.getLowestTemperature();
                    break;
                case 4:
                    yesterdayWeatherFeature = featureMap.get(DateUtils.addDays(date, -1));
                    if (yesterdayWeatherFeature == null) {
                        yesterdayWeatherFeature = weatherFeatureCityDayHisService.findWeatherFeatureCityHisVOByDate(weatherFeatureCityDayHisDOS.get(0).getCityId(), DateUtils.addDays(date, -1));
                    }
                    if (yesterdayWeatherFeature == null) {
                        break;
                    }
                    featureValue = BigDecimalFunctions.subtract(weatherFeatureCityDayHisDO.getLowestTemperature(), yesterdayWeatherFeature.getLowestTemperature());
                    break;
                case 5:
                    featureValue = weatherFeatureCityDayHisDO.getRainfall();
                    break;
            }
            if(featureValue!=null){
                resultMap.put(date, featureValue);
            }
        }
        return resultMap;
    }

    private List<DailyForecastSceneDO> getDailyForecastSceneByFeatureValue(
            Map<Date, BigDecimal> weatherFeatureMap,
            Integer continuousDayValue,
            Integer continuousDayCompareRule,
            Integer conditionCompareRule,
            BigDecimal compareValue,
            List<Date> allDateList) {

        List<DailyForecastSceneDO> result = new ArrayList<>();

        // 获取比较规则枚举
        CompareRuleEnum conditionRuleEnum = CompareRuleEnum.getById(conditionCompareRule);
        CompareRuleEnum dayRuleEnum = CompareRuleEnum.getById(continuousDayCompareRule);

        if (conditionRuleEnum == null || dayRuleEnum == null) {
            return result; // 规则无效，直接返回空列表
        }

        List<Date> currentRange = new ArrayList<>();

        for (Date date : allDateList) {
            BigDecimal featureValue = weatherFeatureMap.get(date);

            if (featureValue == null) {
                // 如果没有数据，视为不满足条件，结束当前连续区间
                if (!currentRange.isEmpty()) {
                    checkAndAddRange(result, currentRange, continuousDayValue, dayRuleEnum);
                    currentRange.clear();
                }
                continue;
            }

            // 判断当前日期是否满足条件（使用传入的 compareValue）
            boolean meetsCondition = false;
            switch (conditionRuleEnum) {
                case GREATER_THAN:
                    meetsCondition = featureValue.compareTo(compareValue) > 0;
                    break;
                case GREATER_THAN_OR_EQUAL_TO:
                    meetsCondition = featureValue.compareTo(compareValue) >= 0;
                    break;
                case LESS_THAN:
                    meetsCondition = featureValue.compareTo(compareValue) < 0;
                    break;
                case LESS_THAN_OR_EQUAL_TO:
                    meetsCondition = featureValue.compareTo(compareValue) <= 0;
                    break;
            }

            if (meetsCondition) {
                currentRange.add(date); // 添加到当前连续区间
            } else {
                // 不满足条件，结束当前连续区间
                checkAndAddRange(result, currentRange, continuousDayValue, dayRuleEnum);
                currentRange.clear();
            }
        }

        // 检查最后一个区间
        checkAndAddRange(result, currentRange, continuousDayValue, dayRuleEnum);

        return result;
    }


    // 检查当前区间是否满足连续天数条件，并添加到结果中
    private void checkAndAddRange(
            List<DailyForecastSceneDO> result,
            List<Date> currentRange,
            Integer continuousDayValue,
            CompareRuleEnum dayRuleEnum) {

        if (currentRange.isEmpty()) {
            return;
        }

        int rangeSize = currentRange.size();
        boolean meetsDayCondition = false;

        switch (dayRuleEnum) {
            case GREATER_THAN:
                meetsDayCondition = rangeSize > continuousDayValue;
                break;
            case GREATER_THAN_OR_EQUAL_TO:
                meetsDayCondition = rangeSize >= continuousDayValue;
                break;
            case LESS_THAN:
                meetsDayCondition = rangeSize < continuousDayValue;
                break;
            case LESS_THAN_OR_EQUAL_TO:
                meetsDayCondition = rangeSize <= continuousDayValue;
                break;
        }

        if (meetsDayCondition) {
            DailyForecastSceneDO sceneDO = new DailyForecastSceneDO();
            sceneDO.setStartDate(currentRange.get(0));
            sceneDO.setEndDate(currentRange.get(currentRange.size() - 1));
            result.add(sceneDO);
        }
    }

}
