
package com.tsintergy.lf.serviceimpl.forecast.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.CacheVO;
import com.tsieframework.core.component.cache.business.CacheService;
import com.tsieframework.core.component.cache.entity.CacheRequest;
import com.tsieframework.core.component.cache.entity.CacheResponse;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.CityExtraAlgorithmRunConfigService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.CityExtraAlgorithmRunConfigDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceimpl.forecast.dao.AlgorithmDAO;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: AlgorithmServiceImpl.java, v 0.1 2018-01-31 10:23:29 tao Exp $$
 */

@Service("algorithmService")
public class AlgorithmServiceImpl implements AlgorithmService {

    @Autowired
    CacheService cacheService;

    @Autowired
    AlgorithmDAO algorithmDAO;

    @Autowired
    private CityExtraAlgorithmRunConfigService cityExtraAlgorithmRunConfigService;

    @Autowired
    private CityService cityService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    public String getAlgorithmCn(String algorithmId) {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + algorithmId);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if (response.getData() != null && response.getData().size() > 0) {
            return ((AlgorithmDO) response.getData().get(0)).getAlgorithmCn();
        }
        return null;
    }

    @Override
    public AlgorithmDO findAlgorithmVOByPk(Serializable pk) throws Exception {
        try {
            return algorithmDAO.findVOByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public AlgorithmDO getAlgorithmDOById(String algorithmId) {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + algorithmId);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if (response.getData() != null && response.getData().size() > 0) {
            return (AlgorithmDO) response.getData().get(0);
        }
        return null;
    }

    @Override
    public String getAlgorithmEn(String algorithmId) {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + algorithmId);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if (response.getData() != null && response.getData().size() > 0) {
            return ((AlgorithmDO) response.getData().get(0)).getAlgorithmEn();
        }
        return null;
    }

    @Override
    public List<AlgorithmDO> getAllAlgorithms() {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + "*");
        CacheResponse response = cacheService.queryCacheItemList(request);
        List<CacheVO> list = response.getData();
        List<AlgorithmDO> algorithmVOS = new ArrayList<>();
        for (CacheVO cacheVO : list) {
            algorithmVOS.add((AlgorithmDO) cacheVO);
        }
        Collections.sort(algorithmVOS, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                AlgorithmDO obj1 = (AlgorithmDO) o1;
                AlgorithmDO obj2 = (AlgorithmDO) o2;
                return obj1.getOrderNo().compareTo(obj2.getOrderNo());
            }
        });
        return algorithmVOS;
    }

    @Override
    public AlgorithmDO getAlgorithmVOByCode(String code) {
        List<AlgorithmDO> algorithmVOS = this.getAllAlgorithms();
        for (AlgorithmDO algorithmVO : algorithmVOS
        ) {
            if (code.equals(algorithmVO.getCode())) {
                return algorithmVO;
            }
        }
        return null;
    }

    @Override
    public List<AlgorithmDO> getAllAlgorithmsNotCache() throws Exception {
        return (List<AlgorithmDO>) algorithmDAO.findAll();
    }

    @SneakyThrows
    @Override
    public List<AlgorithmDO> getAdditionalAlgorithmsToDisplay(String cityId) {
        List<CityExtraAlgorithmRunConfigDO> extraAlgorithmRunConfigDOS = cityExtraAlgorithmRunConfigService.findAll();
        List<String> algorithmIds = new ArrayList<>();
        for (CityExtraAlgorithmRunConfigDO config : extraAlgorithmRunConfigDOS) {
            String[] cityIdArray = config.getCityIds().split(",");
            for (String id : cityIdArray) {
                if (id.trim().equals(cityId)) {
                    algorithmIds.add(config.getAlgorithmId());
                }
            }
        }
        List<AlgorithmDO> allAlgorithms = this.getAllAlgorithms();
        List<AlgorithmDO> algorithmDOList = allAlgorithms.stream().filter(t -> algorithmIds.contains(t.getId())).collect(Collectors.toList());
        CityDO cityDO = cityService.findCityById(cityId);
        return algorithmDOList.stream().map(algorithmDO -> {
            AlgorithmDO algo = new AlgorithmDO();
            BeanUtils.copyProperties(algorithmDO, algo);
            algo.setProvinceView(true);
            algo.setCityView(true);
            if (BooleanUtils.isTrue(algorithmDO.getRegionalCustomAlgorithm())) {
                String originalName = algorithmDO.getAlgorithmCn();
                String newName = originalName.replace("{单位}", cityDO.getCity());
                algo.setAlgorithmCn(newName);
            }
            return algo;
        }).collect(Collectors.toList());
    }

    @SneakyThrows
    @Override
    public List<AlgorithmDO> fillRegionalCustomAlgoName(List<AlgorithmDO> algorithmDOS, String cityId) {
        CityDO cityDO = cityService.findCityById(cityId);
        return algorithmDOS.stream().map(algorithmDO -> {
            AlgorithmDO algo = new AlgorithmDO();
            BeanUtils.copyProperties(algorithmDO, algo);
            if (BooleanUtils.isTrue(algorithmDO.getRegionalCustomAlgorithm())) {
                String originalName = algorithmDO.getAlgorithmCn();
                String newName = originalName.replace("{单位}", cityDO.getCity());
                algo.setAlgorithmCn(newName);
            }
            return algo;
        }).collect(Collectors.toList());
    }

    @Override
    @SneakyThrows
    public List<AlgorithmDO> getPageAlgorithms(List<AlgorithmDO> algorithmDOS, String cityId) {
        List<AlgorithmDO> pageAlgorithms = algorithmDOS.stream().filter(
                        t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType())
                                || AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());
        pageAlgorithms.addAll(this.getAdditionalAlgorithmsToDisplay(cityId));
        return pageAlgorithms;
    }

    @Override
    public List<AlgorithmDTO> getAllAlgorithmDTOByCityId(String cityId) throws Exception {
        // 获取算法列表
        List<AlgorithmDO> allAlgorithmsNotCache = this.getAllAlgorithmsNotCache();
        List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                        t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                                .equals(t.getType()) || AlgorithmConstants.HOLIDAY_ALGORITHM_TYPE.equals(t.getType())
                                || AlgorithmConstants.SPECIAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());


        // 获取自动预测算法
        String defaultAlgorithm = null;
        SystemData systemSetting = settingSystemService.getSystemSetting();
        List<AlgorithmDO> viewAlgorithms = null;
        CityDO cityDO = cityService.findCityById(cityId);
        if(!CollectionUtils.isEmpty(pageAlgorithms)){
            if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                defaultAlgorithm = systemSetting.getProvinceNormalAlgorithm();
                viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
            } else if (CityConstants.CITY_TYPE.equals(cityDO.getType())) {
                defaultAlgorithm = systemSetting.getCityNormalAlgorithm();
                viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true)
                        .filter(t -> !"201".equals(t.getCode())).collect(Collectors.toList());
            }
        }

        viewAlgorithms.addAll(getAdditionalAlgorithmsToDisplay(cityId));
        List<AlgorithmDTO> dtos = new ArrayList<>();
        for (AlgorithmDO algorithmVO : viewAlgorithms) {
            if (algorithmVO.getId().equals(AlgorithmEnum.QR_FORECAST.getId()) || algorithmVO.getId().equals(AlgorithmEnum.FORECAST_MODIFY.getId())) {
                continue;
            }
            AlgorithmDTO dto = new AlgorithmDTO();
            dto.setId(algorithmVO.getId());
            dto.setAlgorithm(algorithmVO.getAlgorithmCn());
            if ("分区预测".equals(algorithmVO.getAlgorithmCn())) {
                dto.setAlgorithm("子网累加");
            }
            if (defaultAlgorithm.equals(algorithmVO.getId())) {
                dto.setDefault(true);
            }
            dtos.add(dto);
        }
        return dtos;
    }
}
