package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.large_model.api.LmStatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmStatisticsCityDayFcDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmStatisticsCityDayFcDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/6 11:14
 **/
@Service("lmStatisticsCityDayFcService")
public class LmStatisticsCityDayFcServiceImpl implements LmStatisticsCityDayFcService {

    @Autowired
    private LmStatisticsCityDayFcDAO lmStatisticsCityDayFcDAO;

    @Override
    public void saveOrUpdateBatch(List<LmStatisticsCityDayFcDO> statisticsCityDayFcDOList) {
        lmStatisticsCityDayFcDAO.saveOrUpdateBatchByTemplate(statisticsCityDayFcDOList);
    }

    @Override
    public List<LmStatisticsCityDayFcDO> find(String cityId, String caliberId, Date startDate, Date endDate, String algorithmName) {
        return lmStatisticsCityDayFcDAO.findAll(JpaWrappers.<LmStatisticsCityDayFcDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), LmStatisticsCityDayFcDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LmStatisticsCityDayFcDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(algorithmName), LmStatisticsCityDayFcDO::getAlgorithmName, algorithmName)
                .ge(LmStatisticsCityDayFcDO::getDate, startDate)
                .le(LmStatisticsCityDayFcDO::getDate, endDate));
    }
}
