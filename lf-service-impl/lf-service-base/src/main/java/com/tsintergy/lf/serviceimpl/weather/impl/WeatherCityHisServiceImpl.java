
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.HttpClientUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.properties.WeatherReportDownloadProperties;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.dto.CityValueDTO;
import com.tsintergy.lf.serviceapi.base.weather.api.StatisticsSynthesizeWeatherCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisMapper;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import net.sf.cglib.beans.BeanMap;
import okhttp3.*;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityHisServiceImpl.java, v 0.1 2018-01-31 11:00:09 tao Exp $$
 */
@Service("weatherCityHisService")
public class WeatherCityHisServiceImpl extends AbstractBaesWeatherServiceImpl implements WeatherCityHisService {

    private static final Logger logger = LogManager.getLogger(WeatherCityHisServiceImpl.class);

    @Autowired
    private StatisticsSynthesizeWeatherCityDayHisService statisticsSynthesizeWeatherCityDayHisService;
    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    CityService cityService;

    @Autowired
    WeatherCityHisDAO weatherCityHisDAO;

    @Autowired
    WeatherCityHisMapper weatherCityHisMapper;

    @Autowired
    private WeatherReportDownloadProperties weatherReportDownloadProperties;

    @Override
    protected List<? extends BaseWeatherDO> findWeatherCityVO(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, startDate, endDate);
    }

    @Override
    protected List<? extends BaseWeatherDO> findStatisticsSynthesizeWeatherCityDayVO(String cityId, Integer type,
        Date startDate, Date endDate) throws Exception {
        return statisticsSynthesizeWeatherCityDayHisService
            .findStatisticsSynthesizeWeatherCityDayHisDO(cityId, type, startDate, endDate);
    }

    @Override
    public DataPackage queryWeatherCityHisDO(DBQueryParam param) throws Exception {
        try {
            return weatherCityHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityHisDO doCreate(WeatherCityHisDO vo) throws Exception {
        try {
            return weatherCityHisDAO.createAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityHisDO(WeatherCityHisDO vo) throws Exception {
        try {
            weatherCityHisDAO.removeAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityHisDOByPK(Serializable pk) throws Exception {
        try {
            weatherCityHisDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityHisDO doUpdateWeatherCityHisDO(WeatherCityHisDO vo) throws Exception {
        try {
            return (WeatherCityHisDO) weatherCityHisDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityHisDO findWeatherCityHisDOByPk(Serializable pk) throws Exception {
        try {
            return (WeatherCityHisDO) weatherCityHisDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherCityHisDO> findWeatherCityHisDOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, startDate, endDate);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<CityValueDTO> find24WeahterCityByDateAndCityIds(Date date, List<String> cityIds, Integer weatherType)
        throws Exception {
        List<CityValueDTO> cityValueDTOS = new ArrayList<CityValueDTO>();
        try {
            List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisDAO
                .getWeatherCityHisDOS(cityIds, weatherType, date, date);
            if (weatherCityHisVOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706");
            } else {
                for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
                    CityValueDTO cityValueDTO = new CityValueDTO();
                    cityValueDTO.setCityId(weatherCityHisVO.getCityId());
                    cityValueDTO.setCity(cityService.findCityById(weatherCityHisVO.getCityId()).getCity());
                    cityValueDTO.setValue(
                        BasePeriodUtils.toList(weatherCityHisVO, 24, true));
                    cityValueDTOS.add(cityValueDTO);
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
        return cityValueDTOS;
    }

    @Override
    public List<WeatherDTO> findWeatherCityHisDTOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {

        return super.findWeatherDTO(cityId, type, startDate, endDate);
    }

    @Override
    public List<WeatherDTO> findWeatherCityHisAllDTOs(String cityId, Integer type, Date startDate, Date endDate, String source) throws Exception {
        List<WeatherDTO> result = new ArrayList<>();


        return result;
    }


    @Override
    public void doInsertOrUpdate(WeatherCityHisDO weatherCityHisVO) throws Exception {
        if (weatherCityHisVO.getDate() == null || weatherCityHisVO.getCityId() == null
            || weatherCityHisVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", weatherCityHisVO.getDate())
            .where(QueryOp.StringEqualTo, "cityId", weatherCityHisVO.getCityId())
            .where(QueryOp.NumberEqualTo, "type", weatherCityHisVO.getType());
        List<WeatherCityHisDO> weatherCityHisV0List = null;
        try {
            weatherCityHisV0List = this.queryWeatherCityHisDO(dbQueryParamBuilder.build()).getDatas();
        } catch (Exception e) {
            logger.error("select weatherCityHisVO 异常", e);
            throw new BusinessException("", "select weatherCityHisVO 异常", e);
        }
        if (weatherCityHisV0List != null && weatherCityHisV0List.size() > 0) {
            this.weatherCityHisDAO.getSession().flush();
            this.weatherCityHisDAO.getSession().clear();
            weatherCityHisVO.setId(weatherCityHisV0List.get(0).getId());
            weatherCityHisVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            try {
                this.doUpdateWeatherCityHisDO(weatherCityHisVO);
            } catch (Exception e) {
                logger.error("update weatherCityHisVO 异常", e);
                throw new BusinessException("", "update weatherCityHisVO 异常", e);
            }
            return;
        }
        try {
            this.doCreate(weatherCityHisVO);
        } catch (Exception e) {
            logger.error("create weatherCityHisVO 异常", e);
            throw new BusinessException("", "create weatherCityHisVO 异常", e);
        }
    }

    @Override
    public void doSaveAllData(WeatherCityHisDO weatherCityHisDO) {
        weatherCityHisDAO.save(weatherCityHisDO);
    }


    @Override
    public List<WeatherCityHisDO> findWeatherCityHisDOSBySQLDate(String cityId, Integer type, java.sql.Date startDate,
        java.sql.Date endDate) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .where(QueryOp.NumberEqualTo, "type", type)
            .where(QueryOp.DateNoLessThan, "date", startDate)
            .where(QueryOp.DateNoMoreThan, "date", endDate);
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        return this.queryWeatherCityHisDO(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    public List<WeatherCityHisDO> findWeatherCityHisDOSByCityIds(List<String> cityIds, Integer type, Date date)
        throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringIsIn, "cityId", cityIds);
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        return weatherCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    public WeatherCityHisDO findWeatherCityHisDO(String cityIds, Integer type, Date date) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "cityId", cityIds);
        if(type != null){
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        List<WeatherCityHisDO> datas = weatherCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
        if (datas!=null&&datas.size()!=0)
        return datas.get(0);
        return null;
    }

    @Override
    public List<WeatherCityHisDO> findWeatherByDates(String cityId, Integer type, List<Date> dateList)
        throws Exception {
        return weatherCityHisDAO.getWeatherCityHisDOSByDates(dateList, cityId, type);
    }

    @Override
    public List<WeatherCityHisDO> findAllWeather() throws Exception {
        return null;
    }


    @Override
    public List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisDAO
            .findWeatherCityHisDO(cityId, null, targetDate, targetDate);
        if (weatherCityHisVOS.size() < 1) {
            return null;
        }
        for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityHisVO.getType()));
            weatherNameDTO.setWeather(BasePeriodUtils
                .toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherNameDTOS.add(weatherNameDTO);
        }
        return weatherNameDTOS;
    }

    @Override
    public List<BigDecimal> find96WeatherCityHisValue(Date date, String cityId, Integer type) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = this
            .findWeatherCityHisDOSBySQLDate(cityId, type, new java.sql.Date(date.getTime()), new java.sql.Date(date.getTime()));
        if (!org.springframework.util.CollectionUtils.isEmpty(weatherCityHisVOS)) {
            return BasePeriodUtils.toList(weatherCityHisVOS.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    @Override
    public List<BigDecimal> findWeatherCityHisValueList(String cityId, Date date, Integer weatherType)
        throws Exception {
        List<WeatherCityHisDO> weatherList = findWeatherCityHisDOSBySQLDate(cityId, weatherType,
            new java.sql.Date(date.getTime()), new java.sql.Date(date.getTime()));
        if (!org.springframework.util.CollectionUtils.isEmpty(weatherList)) {
            return BasePeriodUtils
                .toList(weatherList.get(0), Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    @Override
    public List<BigDecimal> findWeatherCityHisValueList(String cityId, Date startDate, Date endDate,
        Integer weatherType) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisClctDOS = null;
        try {
            weatherCityHisClctDOS = this
                .findWeatherCityHisDOs(cityId, weatherType, startDate, endDate);
        } catch (Exception e) {
            logger.error("历史气象查询异常...", e);
            throw new BusinessException("01C20180003", "");
        }
        List<BigDecimal> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(weatherCityHisClctDOS)) {
            Map<Date, WeatherCityHisDO> weatherMap = weatherCityHisClctDOS.stream()
                .collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity()));
            List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
            List nullList = new ArrayList(96);
            for (int i = 0; i < 96; i++) {
                nullList.add(null);
            }
            dateList.forEach(date -> {
                WeatherCityHisDO weatherCityHisClctDO = weatherMap.get(date);
                if (weatherCityHisClctDO == null) {
                    result.addAll(nullList);
                    return;
                }

                result.addAll(BasePeriodUtils
                    .toList(weatherCityHisClctDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO));
            });

        }
        return result;
    }


    @Override
    public List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception {
        List<BigDecimal> points = new ArrayList<>();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : dates) {
            List<BigDecimal> value = find96WeatherCityHisValue(date, cityId, type);
            if (value != null && value.size()>0){
                points.addAll(value);
            }else {
                points.addAll(DataUtil.getNullList(96));
            }
        }
        return points;
    }

    @Override
    public void doSaveOrUpdateBatch(List<WeatherCityHisDO> WeatherCityHisDOS) {
        if (CollectionUtils.isNotEmpty(WeatherCityHisDOS)) {
            WeatherCityHisDOS.forEach(weatherCityHisDO -> {
                try {
                    this.doSaveOrUpdate(weatherCityHisDO);
                } catch (Exception e) {
                    logger.error("更新城市历史气象异常",e);
                }
            });
        }
    }
    @Override
    public List<WeatherCityHisDO> queryWeatherCityHisDOAllTypeList(String weatherCityId, List<Integer> typeList, List<Date> dateList) {
        return weatherCityHisMapper.selectList(
            new QueryWrapper<WeatherCityHisDO>().lambda()
                .eq(StringUtils.isNotEmpty(weatherCityId), WeatherCityHisDO::getCityId, weatherCityId)
                .in(CollectionUtils.isNotEmpty(typeList), WeatherCityHisDO::getType, typeList)
                .in(CollectionUtils.isNotEmpty(dateList), WeatherCityHisDO::getDate, dateList)
        );
    }

    @Override
    public List<WeatherCityHisDO> queryWeatherCityHisDOList(List<Date> showDates, String cityId, Integer type) {
        return weatherCityHisMapper.selectList(
            new QueryWrapper<WeatherCityHisDO>().lambda()
                .eq(StringUtils.isNotEmpty(cityId), WeatherCityHisDO::getCityId, cityId)
                .eq(WeatherCityHisDO::getType, type)
                .in(!CollectionUtils.isEmpty(showDates), WeatherCityHisDO::getDate, showDates)
        );
    }

    @Override
    public void downloadWeatherReportDaily(Date date, String key) throws Exception {
        String uri = weatherReportDownloadProperties.getUri();
        String token = weatherReportDownloadProperties.getToken();
        String savePath = weatherReportDownloadProperties.getSavePath();
        Date baseDate = DateUtils.string2Date(DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String strdateTime = DateUtils.date2String(baseDate, DateFormatType.DATE_FORMAT_STR);

        // 判断是上午还是下午
        String strKey = key; // 默认上午
        if (key == null) { // 假设传入"PM"表示下午
            if (DateUtil.isAM(date)) {
                strKey = "0";
            } else {
                strKey = "1";
            }
        }

        // 构建GET请求的URL
        String requestUrl = uri +
                "?__strToken=" + URLEncoder.encode(token, StandardCharsets.UTF_8.toString()) +
                "&__strdateTime=" + URLEncoder.encode(strdateTime, StandardCharsets.UTF_8.toString()) +
                "&__strKey=" + URLEncoder.encode(strKey, StandardCharsets.UTF_8.toString());

        // 发送HTTP GET请求
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(requestUrl);
        logger.error("请求气象日报URL: " + requestUrl);
        CloseableHttpResponse response = httpClient.execute(httpGet);

        try {
            // 检查响应状态码
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                throw new RuntimeException("请求失败，状态码: " + statusCode);
            }

            // 获取响应体中的文件下载地址
            String responseBody = EntityUtils.toString(response.getEntity());
            // 这里假设返回的body直接是文件下载地址
            String fileDownloadUrl = responseBody.trim();
            logger.error("请求气象日报下载URL: " + fileDownloadUrl);

            // 下载文件并保存到指定路径
            downloadFile(fileDownloadUrl, savePath, baseDate);
        } finally {
            response.close();
            httpClient.close();
        }
    }

    // 下载文件的方法
    private void downloadFile(String fileUrl, String saveDir, Date date) throws IOException {
        OkHttpClient client = new OkHttpClient();

        Request request = new Request.Builder()
                .url(fileUrl)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);

            // 获取文件名（优先从Content-Disposition头获取）
            String fileName = getFileName(response, fileUrl);

            // 创建保存目录
            File dir = new File(saveDir + DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
            if (!dir.exists() && !dir.mkdirs()) {
                throw new IOException("Failed to create directory: " + saveDir);
            }

            // 组合完整保存路径
            File destFile = new File(dir, fileName);

            // 写入文件
            try (InputStream is = response.body().byteStream();
                 FileOutputStream fos = new FileOutputStream(destFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
        }
    }

    // 获取文件名逻辑
    private String getFileName(Response response, String fileUrl) {
        // 1. 尝试从Content-Disposition头获取
        String disposition = response.header("Content-Disposition");
        if (disposition != null) {
            String[] parts = disposition.split(";");
            for (String part : parts) {
                if (part.trim().startsWith("filename=")) {
                    String fileName = part.split("=")[1]
                            .replace("\"", "")
                            .replace("'", "");
                    return sanitizeFileName(fileName);
                }
            }
        }

        // 2. 从URL路径获取
        String path = HttpUrl.parse(fileUrl).encodedPath();
        String urlFileName = path.substring(path.lastIndexOf('/') + 1);
        if (!urlFileName.isEmpty()) {
            return sanitizeFileName(urlFileName);
        }

        // 3. 生成随机文件名
        return "file_" + System.currentTimeMillis();
    }

    // 过滤非法字符
    private String sanitizeFileName(String name) {
        return name.replaceAll("[\\\\/:*?\"<>|]", "_");
    }



    public void doSaveOrUpdate(WeatherCityHisDO weatherCityHisDO) throws Exception {
        WeatherCityHisDO old = this
            .findWeatherCityHisDO(weatherCityHisDO.getCityId(), weatherCityHisDO.getType(),
                weatherCityHisDO.getDate());
        if (old == null) {
            this.weatherCityHisDAO.save(weatherCityHisDO);
        } else {
            this.weatherCityHisDAO.getSession().flush();
            this.weatherCityHisDAO.getSession().clear();
            weatherCityHisDO.setId(old.getId());
            this.weatherCityHisDAO.update(weatherCityHisDO);
        }
    }

}
