/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/8/26 21:11
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.common.util;

import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import org.springframework.util.CollectionUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/26 
 * @since 1.0.0
 */
public class WeatherCalcUtil {

    /**
     * 开尔文常量
     */
    private static BigDecimal KELVIN_CONSTANT = new BigDecimal(273.15);


    /**
     * 计算实感温度 列表
     *
     * @param temperatures 温度
     * @param winds 风速
     * @param humidity 湿度
     */
    public static List<BigDecimal> calcEffectiveTmpTemperature(List<BigDecimal> temperatures, List<BigDecimal> winds,
                                                               List<BigDecimal> humidity) {
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            BigDecimal value = calcEffectiveTmpTemperature(temperatures.get(i), winds.get(i), humidity.get(i));
            result.add(value);
        }
        return result;
    }

    /**
     * 焓值计算 列表
     *
     * @param temperatures 温度
     * @param humidity 湿度
     */
    public static List<BigDecimal> calcEnthalpyTmpList(List<BigDecimal> temperatures, List<BigDecimal> humidity) {
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            BigDecimal value = calcEnthalpyTmpPoint(temperatures.get(i), humidity.get(i));
            result.add(value);
        }
        return result;
    }

    /**
     * 焓值计算 单点
     *
     * @param temperature 温度
     * @param humidity 湿度
     */
    public static BigDecimal calcEnthalpyTmpPoint(BigDecimal temperature, BigDecimal humidity) {
        if (temperature != null && humidity != null) {
            double hum = humidity.doubleValue() / 100;
            double tem = temperature.doubleValue();
            double kelvinTemp = tem + 273.15;
            double a =
                    -5800.206 * Math.pow(kelvinTemp, -1) + 1.3914993 + (-0.04860239) * kelvinTemp + 0.000041764768 * Math
                            .pow(kelvinTemp, 2) + (-0.000000014452093) * Math.pow(kelvinTemp, 3) + 6.5459673 * Math
                            .log(kelvinTemp);
            double psb = Math.exp(a);
            double ps = psb * hum + 0.001;
            double d = 0.622 * ps / (101325 - ps);
            double enthalpy = 1.01 * tem + d * (2500 + 1.84 * tem);
            return new BigDecimal(enthalpy).setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    /**
     * 计算实感温度 单点
     *
     * @param temperature 温度
     * @param wind 风速
     * @param humidity 湿度
     */
    public static BigDecimal calcEffectiveTmpTemperature(BigDecimal temperature, BigDecimal wind, BigDecimal humidity) {
        if (temperature != null && wind != null && humidity != null) {
            double result =
                    37 - (37 - temperature.doubleValue()) / (0.68 - 0.14 * humidity.doubleValue() / 100 + 1 / (1.76
                            + 1.4 * Math.pow(wind.doubleValue(), 0.75))) - 0.29 * temperature.doubleValue() * (1
                            - humidity.doubleValue() / 100);
            return new BigDecimal(result).setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    /**
     * 寒湿指数 列表
     *
     * @param temperatures 温度
     * @param winds 风速
     * @param humidity 湿度
     */
    public static List<BigDecimal> calcColdDampness(List<BigDecimal> temperatures, List<BigDecimal> winds,
                                                    List<BigDecimal> humidity) {
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            BigDecimal value = calcColdDampness(temperatures.get(i), winds.get(i), humidity.get(i));
            result.add(value);
        }
        return result;
    }

    /**
     * 寒湿指数 单点
     */
    public static BigDecimal calcColdDampness(BigDecimal temperature, BigDecimal wind, BigDecimal humidity) {
        if (temperature != null && wind != null && humidity != null) {
            double result =
                    (33 - temperature.doubleValue()) * (3.3 * Math.sqrt(wind.doubleValue()) - wind.doubleValue() / 3 + 20)
                            * Math.pow(Math.E, 0.005 * Math.abs(humidity.doubleValue() / 100 - 40));
            return new BigDecimal(result).setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    public static BigDecimal calcPointAccuracy(BigDecimal his, BigDecimal fc) {
        BigDecimal accuracy = null;
        if (his != null && fc != null) {
            if (his != null && fc != null) {
                if (his.compareTo(fc) == 0) {
                    accuracy = new BigDecimal(1);
                } else if (his.compareTo(BigDecimal.ZERO) == 0) {
                    accuracy = BigDecimal.ZERO;
                } else {
                    double acc = 1 - Math.abs(his.doubleValue() - fc.doubleValue()) / Math.abs(his.doubleValue());
                    if (acc < 0) {
                        acc = 0;
                    }
                    accuracy = new BigDecimal(acc);
                }
            }
        }
        return accuracy;
    }

    /**
     * 计算准确率
     */
    public static List<BigDecimal> calcAccuracy(List<BigDecimal> hisValues, List<BigDecimal> fcValues) {
        List<BigDecimal> accuracyList = new ArrayList<>();
        if (hisValues != null && fcValues != null) {
            for (int i = 0; i < 96; i++) {
                BigDecimal his = hisValues.get(i);
                BigDecimal fc = fcValues.get(i);
                BigDecimal accuracy = null;
                if (his != null && fc != null) {
                    if (his.compareTo(fc) == 0) {
                        accuracy = new BigDecimal(1);
                    } else if (his.compareTo(BigDecimal.ZERO) == 0) {
                        accuracy = BigDecimal.ZERO;
                    } else {
                        double acc = 1 - Math.abs(his.doubleValue() - fc.doubleValue()) / Math.abs(his.doubleValue());
                        if (acc < 0) {
                            acc = 0;
                        }
                        accuracy = new BigDecimal(acc);
                    }
                }
                accuracyList.add(accuracy);
            }
        }
        return accuracyList;
    }

    public static List<BigDecimal> calcPointAccuracy(List<BigDecimal> hisValues, List<BigDecimal> fcValues) {
        List<BigDecimal> accuracyList = new ArrayList<>();
        if (hisValues != null && fcValues != null) {
            for (int i = 0; i < hisValues.size(); i++) {
                BigDecimal his = hisValues.get(i);
                BigDecimal fc = fcValues.get(i);
                BigDecimal accuracy = null;
                if (his != null && fc != null) {
                    if (his.compareTo(fc) == 0) {
                        accuracy = new BigDecimal(1);
                    } else if (his.compareTo(BigDecimal.ZERO) == 0) {
                        accuracy = BigDecimal.ZERO;
                    } else {
                        double acc = 1 - Math.abs(his.doubleValue() - fc.doubleValue()) / Math.abs(his.doubleValue());
                        if (acc < 0) {
                            acc = 0;
                        }
                        accuracy = new BigDecimal(acc);
                    }
                }
                accuracyList.add(accuracy);
            }
        }
        return accuracyList;
    }

    /**
     * 计算不同数据气象准确率（24个点）
     */
    public static List<BigDecimal> calcSourceAccuracy(List<BigDecimal> hisValues, List<BigDecimal> fcValues) {
        List<BigDecimal> accuracyList = new ArrayList<>();
        if (hisValues != null && fcValues != null) {
            for (int i = 0; i < 24; i++) {
                BigDecimal his = hisValues.get(i);
                BigDecimal fc = fcValues.get(i);
                BigDecimal accuracy = null;
                if (his != null && fc != null) {
                    if (his.equals(fc)) {
                        accuracy = new BigDecimal(1);
                    } else if (his.equals(BigDecimal.ZERO)) {
                        accuracy = BigDecimal.ZERO;
                    } else {
                        double acc = 1 - Math.abs(his.doubleValue() - fc.doubleValue()) / Math.abs(his.doubleValue());
                        if (acc < 0) {
                            acc = 0;
                        }
                        accuracy = new BigDecimal(acc);
                    }
                }
                accuracyList.add(accuracy);
            }
        }
        return accuracyList;
    }

    /**
     * 计算不同数据气象准确率（单点极值）
     */
    public static BigDecimal calcSourceFeatureAccuracy(BigDecimal hisValue, BigDecimal fcValue) {
        BigDecimal accuracy = null;
        if (hisValue != null && fcValue != null) {
            if (hisValue.compareTo(fcValue) == 0) {
                accuracy = new BigDecimal(1);
            } else if (hisValue.compareTo(BigDecimal.ZERO) == 0) {
                accuracy = BigDecimal.ZERO;
            } else {
                double acc = 1 - Math.abs(hisValue.doubleValue() - fcValue.doubleValue()) / Math.abs(hisValue.doubleValue());
                if (acc < 0) {
                    acc = 0;
                }
                accuracy = new BigDecimal(acc);
            }
        }
        return accuracy;
    }

    /**
     * 计算最大偏差
     */
    public static BigDecimal maxDeviation(List<BigDecimal> his, List<BigDecimal> fc, Boolean type) {
        BigDecimal maxDeviation = null;
        List<BigDecimal> deviationList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(his) && !CollectionUtils.isEmpty(fc)) {
            for (int i = 0; i < his.size(); i++) {
                BigDecimal hisBigDecimal = his.get(i);
                BigDecimal fcBigDecimal1 = fc.get(i);
                if (null == hisBigDecimal && null != fcBigDecimal1) {
                    deviationList.add(fcBigDecimal1);
                } else if (null != hisBigDecimal && null == fcBigDecimal1) {
                    deviationList.add(hisBigDecimal);
                } else if (null != hisBigDecimal && null != fcBigDecimal1) {
                    deviationList.add(fcBigDecimal1.subtract(hisBigDecimal).abs());
                }
            }
        }
        if (!CollectionUtils.isEmpty(deviationList)) {
            if (type) {
                maxDeviation = deviationList.stream().max(BigDecimal::compareTo).get();
            } else {
                BigDecimal sum = deviationList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                maxDeviation = sum.divide(new BigDecimal(deviationList.size()), 4, BigDecimal.ROUND_HALF_UP);
            }
        }
        return maxDeviation;
    }

    public static List<BigDecimal> get24Point(Map<String,BigDecimal> map){
        List<BigDecimal> list = new ArrayList<>();
        List<String> columns = ColumnUtil.getColumns(24, Constants.WEATHER_CURVE_START_WITH_ZERO, true);
        for (int i = 0; i < 24; i++) {
            String s = columns.get(i);
            BigDecimal point = map.get(s);
            list.add(point);
        }
        return list;
    }

//    public static void main(String[] args) {
//        BigDecimal decimal = calcEnthalpyTmpPoint(new BigDecimal(91), new BigDecimal(6));
//        System.out.println(decimal);
//    }
}
