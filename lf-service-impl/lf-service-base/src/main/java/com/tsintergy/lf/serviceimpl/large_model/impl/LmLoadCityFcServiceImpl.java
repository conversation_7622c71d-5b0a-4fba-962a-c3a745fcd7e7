package com.tsintergy.lf.serviceimpl.large_model.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.large_model.api.LmLoadCityFcService;
import com.tsintergy.lf.serviceapi.large_model.pojo.LmLoadCityFcDO;
import com.tsintergy.lf.serviceimpl.large_model.dao.LmLoadCityFcDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/7/7 13:54
 **/
@Service("lmLoadCityFcService")
public class LmLoadCityFcServiceImpl implements LmLoadCityFcService {
    @Autowired
    private LmLoadCityFcDAO lmLoadCityFcDAO;
    @Override
    public void saveOrUpdateList(List<LmLoadCityFcDO> list) {
        lmLoadCityFcDAO.saveOrUpdateBatchByTemplate(list);
    }

    @Override
    public List<LmLoadCityFcDO> find(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate) {
        return lmLoadCityFcDAO.findAll(JpaWrappers.<LmLoadCityFcDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), LmLoadCityFcDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LmLoadCityFcDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(algorithmId), LmLoadCityFcDO::getAlgorithmId, algorithmId)
                .ge(LmLoadCityFcDO::getDate, startDate)
                .le(LmLoadCityFcDO::getDate, endDate));
    }
}
