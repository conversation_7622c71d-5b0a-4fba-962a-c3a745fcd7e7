package com.tsintergy.lf.jobhandler.collect.handler.weather;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@JobHandler(value = "weatherCityLongFcJob")
@Component
@Slf4j
public class WeatherCityLongFcJob extends AbstractBaseHandler {

    @Autowired
    WeatherFeatureCityDayLongFcService weatherFeatureCityDayLongFcService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = com.tsieframework.core.base.format.datetime.DateUtils.addDays(new Date(), 1);
        Date endDate = com.tsieframework.core.base.format.datetime.DateUtils.addDays(new Date(), 1);
        if (!StringUtils.isEmpty(s)) {
            String[] params = s.split(",");
            startDate = com.tsieframework.core.base.format.datetime.DateUtils.string2Date(params[0], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = com.tsieframework.core.base.format.datetime.DateUtils.string2Date(params[1], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }
        // 每日统计旬气象预测特性
        TenDaysFcFeature(startDate, endDate);
        // 每日统计月气象预测特性
        MonthFcFeature(startDate, endDate);
        return ReturnT.SUCCESS;
    }

    private void TenDaysFcFeature(Date startDate, Date endDate) throws Exception {
        String startMonth = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").substring(0, 7);
        String endMonth = DateUtil.getDateToStrFORMAT(endDate, "yyyy-MM-dd").substring(0, 7);
        for (String ymn : DateUtil.getTargetTenDaysList(startMonth, endMonth)) {
            List<Date> targetTenDaysDateList = DateUtil.getTargetTenDaysDateList(ymn.substring(0, 7), ymn.substring(8, 10));
            startDate = targetTenDaysDateList.get(0);
            endDate = targetTenDaysDateList.get(1);
            for (WeatherSourceEnum value : WeatherSourceEnum.values()) {
                if (value.getCode().equals(WeatherSourceEnum.HIS.getCode())) {
                    continue;
                }
                weatherFeatureCityDayLongFcService.TenDaysFcLongFeature(startDate, endDate, ymn.substring(0, 7), ymn.substring(8, 10), value.getCode());
            }
        }
    }

    private void MonthFcFeature(Date startDate, Date endDate) throws Exception {
        String start = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").substring(0, 7);
        String end = DateUtil.getDateToStrFORMAT(endDate, "yyyy-MM-dd").substring(0, 7);
        List<String> targetYearList = DateUtil.getTargetYearList(start, end);
        for (String ym : targetYearList) {
            for (WeatherSourceEnum value : WeatherSourceEnum.values()) {
                if (value.getCode().equals(WeatherSourceEnum.HIS.getCode())) {
                    continue;
                }
                Date firstDayDateOfMonth = DateUtil.getFirstDayDateOfMonth(DateUtil.getDate(ym + "-01", "yyyy-MM-dd"));
                Date lastDayOfMonth = DateUtil.getLastDayOfMonth(DateUtil.getDate(ym + "-01", "yyyy-MM-dd"));
                weatherFeatureCityDayLongFcService.MonthFcLongFeature(firstDayDateOfMonth, lastDayOfMonth, ym, value.getCode());
            }
        }
    }
}
