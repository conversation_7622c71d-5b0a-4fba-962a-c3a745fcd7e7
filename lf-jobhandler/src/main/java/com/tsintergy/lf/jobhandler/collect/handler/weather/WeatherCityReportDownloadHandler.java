package com.tsintergy.lf.jobhandler.collect.handler.weather;

import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.collect.HBQXWebService.METAutoStationHourMessage;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import com.tsintergy.lf.serviceimpl.common.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@JobHandler(value = "weatherCityReportDownloadHandler")
@Component
@Slf4j
public class WeatherCityReportDownloadHandler extends AbstractBaseHandler {

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {



        //获取采集日期列表
        List<Date> listBetweenDay = getBetweenDay(s);

        for (Date date : listBetweenDay) {
            //采集元数据
            XxlJobLogger.log("开始采集气象报告..." + DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd HH:mm:ss"));
            weatherCityHisService.downloadWeatherReportDaily(date, null);
            XxlJobLogger.log("结束采集气象报告...:----");
        }

        return ReturnT.SUCCESS;
    }

    private List<Date> getBetweenDay(String s) {
        String startStr;
        String endStr;
        String format = "yyyy-MM-dd HH:mm:ss";
        if (!StringUtils.isEmpty(s)) {
            String[] params = s.split(",");
            startStr = params[0]+" 01:00:00";
            endStr = params[1]+" 13:00:00";
        } else {
            Date end = DateUtil.getCureDate();
            startStr = DateUtils.getDateToStrFORMAT(end, format);
            endStr = DateUtils.getDateToStrFORMAT(end, format);
        }
        Date start = DateUtil.getDate(startStr, format);
        Date end = DateUtil.getDate(endStr, format);
        List<Date> list = new ArrayList<>();
        while (!start.after(end)) {
            Date date = new Date(start.getTime());
            list.add(date);
            start = DateUtil.getMoveHour(start, 12);
        }
        return list;
    }
}
